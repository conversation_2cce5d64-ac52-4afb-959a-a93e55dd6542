{% extends "base.html" %}

{% block title %}Share Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    .share-card {
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .share-link-container {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin: 2rem 0;
        border: 1px solid #dee2e6;
    }
    
    .share-link {
        font-family: monospace;
        word-break: break-all;
        padding: 1rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }
    
    .qr-code-container {
        display: flex;
        justify-content: center;
        margin: 2rem 0;
    }
    
    .copy-success {
        color: #28a745;
        display: none;
        margin-left: 0.5rem;
    }
    
    .share-options {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .share-option {
        flex: 1;
        text-align: center;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #fff;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .share-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .share-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .email-icon {
        color: #d44638;
    }
    
    .whatsapp-icon {
        color: #25d366;
    }
    
    .telegram-icon {
        color: #0088cc;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-share-alt me-2"></i>Share Configuration
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ form_data.form_title }}</h5>
                        {% if form_data.form_description %}
                        <p>{{ form_data.form_description }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('customer_review', form_id=form_id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Review
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Success!</strong> Your form configuration is ready to share. Use the link below to share this configuration with others.
        </div>

        <div class="share-link-container">
            <h5><i class="fas fa-link me-2"></i>Shareable Link</h5>
            <p>Anyone with this link can view your form configuration:</p>
            
            <div class="share-link" id="shareLink">{{ share_link }}</div>
            
            <div class="d-grid gap-2 d-md-flex">
                <button class="btn btn-primary" id="copyLinkBtn">
                    <i class="fas fa-copy me-1"></i>Copy Link
                    <span class="copy-success" id="copySuccess">
                        <i class="fas fa-check"></i> Copied!
                    </span>
                </button>
            </div>
        </div>

        <div class="qr-code-container">
            <div id="qrcode"></div>
        </div>

        <h5 class="mt-4"><i class="fas fa-share-square me-2"></i>Share via</h5>
        <div class="share-options">
            <a href="#" class="share-option" id="emailShare">
                <div class="share-icon email-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div>Email</div>
            </a>
            <a href="#" class="share-option" id="whatsappShare">
                <div class="share-icon whatsapp-icon">
                    <i class="fab fa-whatsapp"></i>
                </div>
                <div>WhatsApp</div>
            </a>
            <a href="#" class="share-option" id="telegramShare">
                <div class="share-icon telegram-icon">
                    <i class="fab fa-telegram-plane"></i>
                </div>
                <div>Telegram</div>
            </a>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4 mb-5">
            <a href="{{ url_for('customer_review', form_id=form_id) }}" class="btn btn-secondary me-md-2">
                <i class="fas fa-arrow-left me-1"></i>Back to Review
            </a>
            <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-primary">
                <i class="fas fa-home me-1"></i>Return to Form Details
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode.js/lib/qrcode.min.js"></script>

<script>
    $(document).ready(function() {
        // Generate QR code
        const shareLink = "{{ share_link }}";
        QRCode.toCanvas(document.getElementById('qrcode'), shareLink, {
            width: 200,
            margin: 1,
            color: {
                dark: '#007bff',
                light: '#ffffff'
            }
        }, function(error) {
            if (error) console.error(error);
        });
        
        // Copy link functionality
        $('#copyLinkBtn').on('click', function() {
            const shareLink = document.getElementById('shareLink');
            const range = document.createRange();
            range.selectNode(shareLink);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            
            // Show success message
            $('#copySuccess').fadeIn().delay(2000).fadeOut();
        });
        
        // Share via email
        $('#emailShare').on('click', function(e) {
            e.preventDefault();
            const subject = encodeURIComponent("Form Configuration: {{ form_data.form_title }}");
            const body = encodeURIComponent("Here's the link to the form configuration I created: {{ share_link }}");
            window.location.href = `mailto:?subject=${subject}&body=${body}`;
        });
        
        // Share via WhatsApp
        $('#whatsappShare').on('click', function(e) {
            e.preventDefault();
            const text = encodeURIComponent("Form Configuration: {{ form_data.form_title }} - {{ share_link }}");
            window.open(`https://wa.me/?text=${text}`, '_blank');
        });
        
        // Share via Telegram
        $('#telegramShare').on('click', function(e) {
            e.preventDefault();
            const text = encodeURIComponent("Form Configuration: {{ form_data.form_title }} - {{ share_link }}");
            window.open(`https://t.me/share/url?url=${encodeURIComponent("{{ share_link }}")}&text=${text}`, '_blank');
        });
    });
</script>
{% endblock %}
