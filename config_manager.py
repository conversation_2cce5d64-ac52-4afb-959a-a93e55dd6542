"""
Configuration Manager Module for Google Form AutoFill

This module handles the configuration settings for the application.
"""

import os
import json
import time
from typing import Dict, Any, Optional, Union

class ConfigManager:
    """Class for managing application configuration"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the configuration manager
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file
        
        Returns:
            Dictionary containing configuration settings
        """
        # Default configuration
        default_config = {
            "customer_plugin_enabled": True,
            "max_responses_per_customer": 100,
            "features": {
                "ai_generation": True,
                "manual_review": True,
                "batch_submission": True
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True
            },
            "created_at": time.time(),
            "last_modified_at": time.time()
        }
        
        # If config file doesn't exist, create it with default values
        if not os.path.exists(self.config_file):
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            return default_config
            
        # Load config from file
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # Update with any missing default values
            updated = False
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
                    updated = True
                    
            # Save updated config if needed
            if updated:
                self.save_config(config)
                
            return config
        except json.JSONDecodeError:
            print(f"Error: Could not parse config file {self.config_file}")
            return default_config
            
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """
        Save configuration to file
        
        Args:
            config: Configuration to save (uses current config if None)
            
        Returns:
            True if successful, False otherwise
        """
        if config is None:
            config = self.config
            
        # Update last modified timestamp
        config["last_modified_at"] = time.time()
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config: {str(e)}")
            return False
            
    def get_config(self) -> Dict[str, Any]:
        """
        Get the current configuration
        
        Returns:
            Dictionary containing configuration settings
        """
        return self.config
        
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """
        Update configuration with new values
        
        Args:
            updates: Dictionary containing configuration updates
            
        Returns:
            True if successful, False otherwise
        """
        # Update config with new values
        for key, value in updates.items():
            if key in self.config:
                if isinstance(self.config[key], dict) and isinstance(value, dict):
                    # Merge dictionaries for nested configs
                    self.config[key].update(value)
                else:
                    # Replace value for non-dict types
                    self.config[key] = value
            else:
                # Add new key
                self.config[key] = value
                
        # Save updated config
        return self.save_config()
        
    def get_value(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value
        
        Args:
            key: Configuration key (supports dot notation for nested keys)
            default: Default value if key not found
            
        Returns:
            Configuration value or default if not found
        """
        # Handle nested keys with dot notation
        if '.' in key:
            parts = key.split('.')
            value = self.config
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return default
            return value
        
        # Handle simple keys
        return self.config.get(key, default)
        
    def set_value(self, key: str, value: Any) -> bool:
        """
        Set a configuration value
        
        Args:
            key: Configuration key (supports dot notation for nested keys)
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        # Handle nested keys with dot notation
        if '.' in key:
            parts = key.split('.')
            config = self.config
            for i, part in enumerate(parts[:-1]):
                if part not in config:
                    config[part] = {}
                elif not isinstance(config[part], dict):
                    config[part] = {}
                config = config[part]
            config[parts[-1]] = value
        else:
            # Handle simple keys
            self.config[key] = value
            
        # Save updated config
        return self.save_config()
        
    def is_feature_enabled(self, feature_name: str) -> bool:
        """
        Check if a feature is enabled
        
        Args:
            feature_name: Name of the feature to check
            
        Returns:
            True if feature is enabled, False otherwise
        """
        return self.get_value(f"features.{feature_name}", False)
