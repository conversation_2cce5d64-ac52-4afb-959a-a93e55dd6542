---
description:
globs:
alwaysApply: false
---
# UI/UX Patterns and Modal Management Rules

## Critical Modal Flashing Prevention

### ⚠️ NEVER Use Transform Hover Effects
The following CSS patterns cause modal flashing and MUST be avoided:

```css
/* ❌ FORBIDDEN - Causes modal flashing */
.card:hover { transform: translateY(-2px); }
.btn:hover { transform: translateY(-1px); }
.badge:hover { transform: translateY(-1px); }
.navbar-brand:hover { transform: translateY(-1px); }
.nav-link:hover { transform: translateY(-1px); }
.hover-lift:hover { transform: translateY(-2px); }
.list-group-item:hover { transform: translateX(2px); }
```

### ✅ Required Modal-Safe CSS Patterns
Always use these patterns in [web/static/css/style.css](mdc:web/static/css/style.css):

```css
/* ✅ REQUIRED - Modal-safe hover effects */
.card:hover { transform: none !important; box-shadow: var(--shadow-lg); }
.btn:hover { transform: none !important; box-shadow: var(--shadow-md); }
.badge:hover { transform: none !important; box-shadow: var(--shadow-sm); }
.navbar-brand:hover { transform: none !important; }
.nav-link:hover { transform: none !important; }
.hover-lift:hover { transform: none !important; }
.list-group-item:hover { transform: none !important; }
```

## Dark Theme Text Visibility

### Text Color Hierarchy
1. **Primary Text**: `var(--text-primary)` (#ffffff) - Main content
2. **Secondary Text**: `var(--text-secondary)` (#b0b0b0) - Supporting content
3. **White Text**: `text-white` - Critical UI elements in dark areas
4. **Avoid**: `text-muted` for important elements (hard to see)

### Critical Text Elements
Always use `text-white` for:
- Selected item counts in batch operations
- Important status indicators
- Critical action feedback
- Modal header text
- Badge text on dark backgrounds

Example from [web/templates/review_responses.html](mdc:web/templates/review_responses.html):
```html
<!-- ✅ CORRECT - Visible selected count -->
<span class="ms-2 text-white">
    <span class="selected-count" data-question="{{ question_index }}">0</span> selected
</span>

<!-- ❌ WRONG - Hard to see -->
<span class="ms-2 text-muted">
    <span class="selected-count">0</span> selected
</span>
```

## Modal Management in JavaScript

### Required Modal Utilities
Always use these utilities in [web/static/js/main.js](mdc:web/static/js/main.js):

```javascript
// ✅ REQUIRED - Safe modal management
AppUtils.safelyShowModal('modalId');
AppUtils.safelyHideModal('modalId');
AppUtils.safelyManageModals();
```

### Event Handler Best Practices
```javascript
// ✅ CORRECT - Namespaced events with immediate propagation stop
$('.batch-delete').off('click.reviewResponses').on('click.reviewResponses', function(e) {
    e.preventDefault();
    e.stopImmediatePropagation();
    // Handler code
});

// ❌ WRONG - No namespace, potential conflicts
$('.batch-delete').on('click', function(e) {
    // Handler code
});
```

### Modal State Management
```javascript
// ✅ REQUIRED - Proper modal lifecycle
const pageInitialized = 'reviewResponsesPageInitialized';
if (window[pageInitialized]) {
    return; // Prevent multiple initializations
}
window[pageInitialized] = true;

// Proper cleanup on modal hide
$(document).off('hidden.bs.modal.reviewResponses').on('hidden.bs.modal.reviewResponses', '.modal', function() {
    // Reset modal state
    setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open').css('padding-right', '');
    }, 100);
});
```

## CSS Architecture for Dark Theme

### Color Variables Usage
Reference from [web/static/css/style.css](mdc:web/static/css/style.css):
```css
:root {
    --bg-primary: #1a1a1a;        /* Main background */
    --bg-secondary: #2d2d2d;      /* Navbar, footer */
    --bg-tertiary: #3a3a3a;       /* Form inputs */
    --bg-card: #2a2a2a;           /* Card backgrounds */
    --bg-card-hover: #343434;     /* Hover states */
    
    --text-primary: #ffffff;      /* Main text */
    --text-secondary: #b0b0b0;    /* Secondary text */
    --text-muted: #808080;        /* Muted text (use sparingly) */
    
    --accent-primary: #6c5ce7;    /* Primary actions */
    --accent-success: #00b894;    /* Success states */
    --accent-warning: #fdcb6e;    /* Warning states */
    --accent-danger: #e17055;     /* Danger states */
}
```

### Gradient Patterns
```css
/* ✅ STANDARD - Consistent gradient patterns */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}

.card-header {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}
```

## Responsive Design Patterns

### Mobile-First Breakpoints
```css
/* Base styles - Mobile first */
.btn-group { display: flex; gap: 0.5rem; }

/* Tablet and up */
@media (max-width: 768px) {
    .btn-group { flex-direction: column; align-items: stretch; }
}

/* Mobile only */
@media (max-width: 576px) {
    .modal-body { padding: 1rem; }
}
```

### Touch-Friendly Sizing
- Minimum touch target: 44px x 44px
- Button padding: `0.6rem 1.2rem` minimum
- Form inputs: `0.75rem 1rem` padding
- Modal close buttons: Adequate spacing from edges

## Animation Performance

### Allowed Animations
```css
/* ✅ SAFE - Non-transform animations */
.fade-in { opacity: 0; animation: fadeIn 0.3s ease-out forwards; }
.scale-in { transform: scale(0.95); animation: scaleIn 0.2s ease-out forwards; }

@keyframes fadeIn { to { opacity: 1; } }
@keyframes scaleIn { to { transform: scale(1); } }
```

### Performance CSS Properties
```css
/* ✅ RECOMMENDED - GPU-accelerated properties */
.animated-element {
    will-change: transform, opacity;
    transform: translateZ(0); /* Force GPU layer */
}
```

## Accessibility Requirements

### Color Contrast
- Minimum contrast ratio: 4.5:1 for normal text
- Minimum contrast ratio: 3:1 for large text
- Use tools to verify contrast in dark theme

### Focus Management
```css
/* ✅ REQUIRED - Visible focus indicators */
.btn:focus, .form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    outline: none;
}
```

### Semantic HTML
- Use proper heading hierarchy (h1 → h2 → h3)
- Include ARIA labels for interactive elements
- Ensure keyboard navigation works properly
- Test with screen readers when possible

## Testing Guidelines

### Modal Testing Checklist
1. Open modal → cursor movement → no flashing
2. Rapid open/close → no duplicate modals
3. Background click → proper closing behavior
4. Escape key → closes modal correctly
5. Multiple modals → proper z-index stacking

### Text Visibility Testing
1. Test in bright lighting conditions
2. Test with different monitor settings
3. Verify contrast ratios with tools
4. Test with browser zoom at 200%
5. Check mobile device visibility
