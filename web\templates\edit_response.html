{% extends "base.html" %}

{% block title %}Edit Response - Google Form AutoFill{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0"><i class="fas fa-edit me-2"></i>Edit Response</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h5>Question: {{ question.title }}</h5>
                    <p>
                        <span class="badge bg-info">{{ question.type_name }}</span>
                        <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %}">
                            {% if question.required %}Required{% else %}Optional{% endif %}
                        </span>
                    </p>
                    {% if question.description %}
                    <p><strong>Description:</strong> {{ question.description }}</p>
                    {% endif %}
                </div>
                
                <form method="POST" action="{{ url_for('edit_response', form_id=form_id, question_index=question_index, response_index=response_index) }}">
                    <div class="mb-3">
                        <label for="text" class="form-label">Response Text</label>
                        <textarea id="text" name="text" class="form-control" rows="5" required>{{ response.text }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight</label>
                        <input type="number" id="weight" name="weight" class="form-control" value="{{ response.weight }}" min="1" required>
                        <div class="form-text">Higher weight means this response will be selected more often during submission.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('review_responses', form_id=form_id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Review
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
