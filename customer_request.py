"""
Customer Request Module for Google Form AutoFill

This module provides functionality for storing, managing, and retrieving
customer submission requests.
"""

import json
import os
import time
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime


class CustomerRequest:
    """Class representing a customer submission request"""
    
    def __init__(self, customer_id: str, form_id: str, form_url: str, form_title: str,
                 submission_count: int, priority: int = 1, notes: str = None):
        """
        Initialize a customer request
        
        Args:
            customer_id: Unique identifier for the customer
            form_id: The ID of the form to submit
            form_url: The URL of the form
            form_title: The title of the form
            submission_count: Number of submissions requested
            priority: Priority level (1-5, with 5 being highest)
            notes: Additional notes or instructions
        """
        self.id = str(uuid.uuid4())
        self.customer_id = customer_id
        self.form_id = form_id
        self.form_url = form_url
        self.form_title = form_title
        self.submission_count = submission_count
        self.priority = min(max(priority, 1), 5)  # Ensure priority is between 1-5
        self.notes = notes
        self.created_at = time.time()
        self.updated_at = time.time()
        self.status = "pending"  # pending, in_progress, completed, failed, cancelled
        self.scheduled_time = None  # Time when the request is scheduled to be processed
        self.completed_at = None
        self.result = None  # Will store submission results when completed
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the request to a dictionary for storage"""
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "form_id": self.form_id,
            "form_url": self.form_url,
            "form_title": self.form_title,
            "submission_count": self.submission_count,
            "priority": self.priority,
            "notes": self.notes,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "status": self.status,
            "scheduled_time": self.scheduled_time,
            "completed_at": self.completed_at,
            "result": self.result
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CustomerRequest':
        """Create a CustomerRequest instance from a dictionary"""
        request = cls(
            customer_id=data.get("customer_id"),
            form_id=data.get("form_id"),
            form_url=data.get("form_url"),
            form_title=data.get("form_title"),
            submission_count=data.get("submission_count", 0),
            priority=data.get("priority", 1),
            notes=data.get("notes")
        )
        
        # Set additional attributes
        request.id = data.get("id", request.id)
        request.created_at = data.get("created_at", request.created_at)
        request.updated_at = data.get("updated_at", request.updated_at)
        request.status = data.get("status", request.status)
        request.scheduled_time = data.get("scheduled_time")
        request.completed_at = data.get("completed_at")
        request.result = data.get("result")
        
        return request


class CustomerRequestManager:
    """Class for managing customer requests"""
    
    def __init__(self, storage_dir: str = "responses/customer_requests"):
        """
        Initialize the customer request manager
        
        Args:
            storage_dir: Directory to store customer request files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_requests_filename(self) -> str:
        """Get the filename for customer requests"""
        return os.path.join(self.storage_dir, "customer_requests.json")
        
    def _load_requests(self) -> List[Dict[str, Any]]:
        """Load customer requests from storage"""
        filename = self._get_requests_filename()
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _save_requests(self, requests: List[Dict[str, Any]]) -> None:
        """Save customer requests to storage"""
        filename = self._get_requests_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(requests, f, ensure_ascii=False, indent=2)
            
    def add_request(self, request: CustomerRequest) -> str:
        """
        Add a new customer request
        
        Args:
            request: The CustomerRequest object to add
            
        Returns:
            The ID of the added request
        """
        requests = self._load_requests()
        request_dict = request.to_dict()
        
        # Check if request with this ID already exists
        for i, existing_request in enumerate(requests):
            if existing_request.get("id") == request.id:
                # Update existing request
                requests[i] = request_dict
                self._save_requests(requests)
                return request.id
                
        # Add new request
        requests.append(request_dict)
        self._save_requests(requests)
        return request.id
        
    def get_request(self, request_id: str) -> Optional[CustomerRequest]:
        """
        Get a customer request by ID
        
        Args:
            request_id: The ID of the request
            
        Returns:
            The CustomerRequest object, or None if not found
        """
        requests = self._load_requests()
        for request_data in requests:
            if request_data.get("id") == request_id:
                return CustomerRequest.from_dict(request_data)
        return None
        
    def update_request(self, request: CustomerRequest) -> bool:
        """
        Update an existing customer request
        
        Args:
            request: The CustomerRequest object to update
            
        Returns:
            True if successful, False otherwise
        """
        requests = self._load_requests()
        request.updated_at = time.time()
        request_dict = request.to_dict()
        
        for i, existing_request in enumerate(requests):
            if existing_request.get("id") == request.id:
                requests[i] = request_dict
                self._save_requests(requests)
                return True
                
        return False
        
    def delete_request(self, request_id: str) -> bool:
        """
        Delete a customer request
        
        Args:
            request_id: The ID of the request to delete
            
        Returns:
            True if successful, False otherwise
        """
        requests = self._load_requests()
        initial_count = len(requests)
        
        requests = [r for r in requests if r.get("id") != request_id]
        
        if len(requests) < initial_count:
            self._save_requests(requests)
            return True
            
        return False
        
    def get_all_requests(self, status: Optional[str] = None, 
                        customer_id: Optional[str] = None,
                        sort_by: str = "created_at",
                        sort_desc: bool = True) -> List[CustomerRequest]:
        """
        Get all customer requests, optionally filtered by status or customer
        
        Args:
            status: Filter by status (pending, in_progress, completed, failed, cancelled)
            customer_id: Filter by customer ID
            sort_by: Field to sort by (created_at, updated_at, priority, etc.)
            sort_desc: Sort in descending order if True, ascending if False
            
        Returns:
            List of CustomerRequest objects
        """
        requests = self._load_requests()
        
        # Apply filters
        if status:
            requests = [r for r in requests if r.get("status") == status]
            
        if customer_id:
            requests = [r for r in requests if r.get("customer_id") == customer_id]
            
        # Sort requests
        if sort_by in ["created_at", "updated_at", "priority", "scheduled_time"]:
            reverse = sort_desc
            requests = sorted(requests, key=lambda r: r.get(sort_by, 0), reverse=reverse)
            
        # Convert to CustomerRequest objects
        return [CustomerRequest.from_dict(r) for r in requests]
        
    def get_pending_requests(self, sort_by_priority: bool = True) -> List[CustomerRequest]:
        """
        Get all pending customer requests
        
        Args:
            sort_by_priority: Sort by priority if True, otherwise by creation time
            
        Returns:
            List of pending CustomerRequest objects
        """
        sort_by = "priority" if sort_by_priority else "created_at"
        return self.get_all_requests(status="pending", sort_by=sort_by, sort_desc=sort_by_priority)
        
    def update_request_status(self, request_id: str, status: str, 
                             result: Optional[Dict[str, Any]] = None) -> bool:
        """
        Update the status of a customer request
        
        Args:
            request_id: The ID of the request
            status: The new status (pending, in_progress, completed, failed, cancelled)
            result: Optional result data for completed or failed requests
            
        Returns:
            True if successful, False otherwise
        """
        request = self.get_request(request_id)
        if not request:
            return False
            
        request.status = status
        request.updated_at = time.time()
        
        if status in ["completed", "failed"]:
            request.completed_at = time.time()
            if result:
                request.result = result
                
        return self.update_request(request)
        
    def get_request_count_by_status(self) -> Dict[str, int]:
        """
        Get count of requests by status
        
        Returns:
            Dictionary with counts for each status
        """
        requests = self._load_requests()
        counts = {
            "pending": 0,
            "in_progress": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0,
            "total": len(requests)
        }
        
        for request in requests:
            status = request.get("status", "pending")
            if status in counts:
                counts[status] += 1
                
        return counts
        
    def get_customer_statistics(self, customer_id: str) -> Dict[str, Any]:
        """
        Get statistics for a specific customer
        
        Args:
            customer_id: The ID of the customer
            
        Returns:
            Dictionary with customer statistics
        """
        requests = self.get_all_requests(customer_id=customer_id)
        
        if not requests:
            return {
                "total_requests": 0,
                "total_submissions": 0,
                "completed_submissions": 0,
                "failed_submissions": 0,
                "pending_requests": 0,
                "in_progress_requests": 0,
                "success_rate": 0
            }
            
        total_submissions = sum(r.submission_count for r in requests)
        completed_submissions = 0
        failed_submissions = 0
        
        for request in requests:
            if request.status == "completed" and request.result:
                completed_submissions += request.result.get("successful", 0)
                failed_submissions += request.result.get("failed", 0)
                
        pending_requests = sum(1 for r in requests if r.status == "pending")
        in_progress_requests = sum(1 for r in requests if r.status == "in_progress")
        
        success_rate = (completed_submissions / total_submissions) if total_submissions > 0 else 0
        
        return {
            "total_requests": len(requests),
            "total_submissions": total_submissions,
            "completed_submissions": completed_submissions,
            "failed_submissions": failed_submissions,
            "pending_requests": pending_requests,
            "in_progress_requests": in_progress_requests,
            "success_rate": success_rate
        }
