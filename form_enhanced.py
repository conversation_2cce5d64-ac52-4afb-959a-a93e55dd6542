"""
Enhanced Form Module for Google Form AutoFill

This module extends the base form.py functionality to provide more detailed
question information and better categorization for the enhanced Google Form AutoFill tool.
"""

import re
import json
import requests
import traceback
from typing import Dict, List, Any, Optional, Union, Tuple

import form

# Constants
QUESTION_TYPE_MAP = {
    0: "short_answer",
    1: "paragraph",
    2: "multiple_choice",
    3: "dropdown",
    4: "checkboxes",
    5: "linear_scale",
    6: "rating",
    7: "grid_choice",
    8: "checkbox_grid",  # Checkbox grid questions
    9: "date",
    10: "time",
    11: "multiple_choice_grid",  # Multiple choice grid questions
    18: "rating"
}

# Human-readable question type names
QUESTION_TYPE_NAMES = {
    0: "Short Answer",
    1: "Paragraph",
    2: "Multiple Choice",
    3: "Dropdown",
    4: "Checkboxes",
    5: "Linear Scale",
    6: "Rating",
    7: "Grid Choice",
    8: "Checkbox Grid",
    9: "Date",
    10: "Time",
    11: "Multiple Choice Grid",
    18: "Rating"
}



def get_fb_public_load_data_improved(url):
    """Improved version of get_fb_public_load_data that is more reliable"""
    try:
        # Use improved headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.google.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }

        # Add cookies to help bypass Google bot detection
        cookies = {
            'NID': 'some_value',
        }

        # Make the request with a longer timeout
        response = requests.get(url, headers=headers, cookies=cookies, timeout=15)

        if response.status_code != 200:
            print(f"Error! HTTP status code: {response.status_code}")
            return None

        # Extract the form data with improved function
        pattern = re.compile(r'var\s+FB_PUBLIC_LOAD_DATA_\s*=\s*(\[.*?\]);', re.DOTALL)
        match = pattern.search(response.text)

        if not match:
            print("Error! Form data pattern not found in the page.")
            return None

        value_str = match.group(1)
        try:
            form_data = json.loads(value_str)
        except json.JSONDecodeError as e:
            print(f"Error! Failed to parse form data JSON: {e}")
            return None

        # Check if form data has the expected structure
        if len(form_data) <= 1 or not form_data[1]:
            print("Error! Form data has invalid structure. The form might be corrupted or in an unsupported format.")
            return None

        return form_data
    except Exception as e:
        print(f"Error getting form data: {e}")
        traceback.print_exc()
        return None


class EnhancedFormParser:
    """Enhanced parser for Google Forms with additional functionality"""

    def __init__(self, url: str):
        """
        Initialize the enhanced form parser

        Args:
            url: The URL of the Google Form
        """
        self.url = url
        self.response_url = form.get_form_response_url(url)
        self.form_data = None
        self.form_title = None
        self.form_description = None
        self.questions = []

    def load_form(self) -> bool:
        """
        Load and parse the form

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the raw form data using the improved local function
            self.form_data = get_fb_public_load_data_improved(self.url) # Use the improved version
            if not self.form_data:
                # The improved function already prints detailed errors
                # print("Error! Can't get form data. Login may be required or form structure changed.")
                return False

            # Check if form data has the expected structure (some checks might be redundant if get_fb_public_load_data_improved handles them)
            if len(self.form_data) <= 1:
                print("Error! Form data structure is invalid (index [1] missing).")
                return False

            if not self.form_data[1]:
                print("Error! Form data[1] is None or empty.")
                return False

            if len(self.form_data[1]) <= 1:
                print("Error! Form data[1] structure is invalid (index [1][1] missing).")
                return False

            if not self.form_data[1][1]:
                print("Error! Form data[1][1] is None or empty.")
                return False

            # Extract form title and description
            try:
                # self.form_data[0] is a description, self.form_data[1][8] is title based on common structure
                # However, the structure from FB_PUBLIC_LOAD_DATA can vary.
                # A more robust way is to look for known indices or rely on what get_fb_public_load_data_improved guarantees.
                # Assuming form_data[1] is the main content array as before.
                self.form_description = self.form_data[0] # Usually the description is at the top level
                self.form_title = self.form_data[1][8] if len(self.form_data) > 1 and len(self.form_data[1]) > 8 else "Untitled Form"
            except (IndexError, TypeError) as e:
                print(f"Warning: Could not extract form title/description: {e}")
                self.form_title = "Untitled Form"
                self.form_description = ""

            # Parse questions
            self._parse_questions()

            # Check if any questions were parsed
            if not self.questions:
                print("Warning: No questions were found in the form.")

            return True
        except Exception as e:
            print(f"Error loading form: {e}")
            traceback.print_exc()
            return False

    def _parse_questions(self) -> None:
        """Parse all questions from the form data"""
        self.questions = []
        page_count = 0

        try:
            # Check if form_data[1][1] exists and is iterable
            if not self.form_data or len(self.form_data) <= 1:
                print("Error: Form data is invalid or empty")
                return

            if not self.form_data[1] or len(self.form_data[1]) <= 1:
                print("Error: Form data[1] is invalid or empty")
                return

            if not self.form_data[1][1]:
                print("Error: Form data[1][1] is None or empty")
                return

            # Process each entry in form_data[1][1]
            for entry in self.form_data[1][1]:
                if not entry or len(entry) <= 3:
                    print(f"Warning: Entry {entry} is invalid or too short")
                    continue

                if entry[3] == form.FORM_SESSION_TYPE_ID:
                    page_count += 1
                    continue

                # Process each question
                entry_name = entry[1]
                entry_type_id = entry[3]

                # Check if entry[4] exists and is iterable
                if len(entry) <= 4:
                    print(f"Warning: Entry does not have index [4]: {entry}")
                    continue

                if entry[4] is None:
                    print(f"Warning: Entry[4] is None for entry: {entry}")
                    continue

                # Special handling for grid questions (Type 7)
                if entry_type_id == 7:
                    try:
                        question = self._create_grid_question_object(entry_name, entry_type_id, entry[4])
                        if question:
                            self.questions.append(question)
                    except Exception as e:
                        print(f"Error creating grid question: {e}")
                        print(f"Entry: {entry}")
                        continue
                else:
                    # Process each sub_entry in entry[4] for non-grid questions
                    try:
                        for sub_entry in entry[4]:
                            question = self._create_question_object(entry_name, entry_type_id, sub_entry)
                            self.questions.append(question)
                    except TypeError as e:
                        print(f"Error iterating over entry[4]: {e}")
                        print(f"Entry: {entry}")
                        continue

            # Add email field if required
            try:
                if len(self.form_data[1]) > 10 and self.form_data[1][10] and len(self.form_data[1][10]) > 6 and self.form_data[1][10][6] > 1:
                    self.questions.append({
                        "id": "emailAddress",
                        "title": "Email Address",
                        "type": "email",
                        "type_name": "Email",
                        "required": True,
                        "description": "Your email address",
                        "options": None,
                        "is_open_ended": True
                    })
            except (IndexError, TypeError) as e:
                print(f"Error adding email field: {e}")


        except Exception as e:
            print(f"Error parsing questions: {e}")
            traceback.print_exc()

    def _create_question_object(self, entry_name: str, entry_type_id: int, sub_entry: List[Any]) -> Dict[str, Any]:
        """
        Create a structured question object

        Args:
            entry_name: The name of the entry
            entry_type_id: The type ID of the entry
            sub_entry: The sub-entry data

        Returns:
            A structured question object
        """
        # Get question type information
        question_type = QUESTION_TYPE_MAP.get(entry_type_id, "unknown")
        question_type_name = QUESTION_TYPE_NAMES.get(entry_type_id, "Unknown")

        # Determine if this is an open-ended question
        is_open_ended = entry_type_id in [0, 1]  # Short answer or paragraph

        # Get options if available
        options = None
        grid_rows = None
        grid_columns = None

        if sub_entry[1]:
            options = [(x[0] or form.ANY_TEXT_FIELD) for x in sub_entry[1]]
            # Remove ANY_TEXT_FIELD placeholder
            if form.ANY_TEXT_FIELD in options:
                options.remove(form.ANY_TEXT_FIELD)

        # Special handling for grid questions (Type 7)
        if entry_type_id == 7:
            # For grid questions, sub_entry[3] contains row labels
            # and sub_entry[1] contains column options
            if len(sub_entry) > 3 and sub_entry[3]:
                grid_rows = sub_entry[3] if isinstance(sub_entry[3], list) else [sub_entry[3]]
                grid_columns = options

                # Determine if this is a checkbox grid or multiple choice grid
                # This is a heuristic - you may need to adjust based on actual form structure
                if len(grid_rows) > 1 and len(grid_columns) > 1:
                    # Check if this appears to be a checkbox grid vs multiple choice grid
                    # This detection might need refinement based on actual Google Forms structure
                    question_type = "checkbox_grid"  # Default to checkbox grid
                    question_type_name = "Checkbox Grid"
                    entry_type_id = 8  # Update type ID for consistency

        # Get question name/title
        question_name = sub_entry[3] if (len(sub_entry) > 3 and sub_entry[3]) else None
        if question_name:
            if isinstance(question_name, list):
                if entry_type_id == 7:  # Grid question
                    # For grid questions, use the entry name as title and sub_entry[3] as rows
                    question_name = entry_name
                else:
                    question_name = ' - '.join(question_name)
        else:
            question_name = entry_name

        # Create the question object
        question_obj = {
            "id": sub_entry[0],
            "title": question_name,
            "type": question_type,
            "type_id": entry_type_id,
            "type_name": question_type_name,
            "required": sub_entry[2] == 1,
            "description": entry_name if question_name != entry_name else None,
            "options": options,
            "is_open_ended": is_open_ended
        }

        # Add grid-specific properties
        if grid_rows:
            question_obj["grid_rows"] = grid_rows
        if grid_columns:
            question_obj["grid_columns"] = grid_columns

        return question_obj

    def _create_grid_question_object(self, entry_name: str, entry_type_id: int, sub_entries: List[Any]) -> Dict[str, Any]:
        """
        Create a structured grid question object by combining all sub-entries

        Args:
            entry_name: The name of the entry
            entry_type_id: The type ID of the entry (should be 7 for grid)
            sub_entries: List of sub-entry data for all rows in the grid

        Returns:
            A structured grid question object or None if invalid
        """
        if not sub_entries:
            return None

        # Use the first sub_entry to get basic information
        first_sub_entry = sub_entries[0]

        # Get question type information
        question_type = QUESTION_TYPE_MAP.get(entry_type_id, "unknown")
        question_type_name = QUESTION_TYPE_NAMES.get(entry_type_id, "Unknown")

        # Get grid columns from the first sub_entry's options
        grid_columns = None
        if first_sub_entry[1]:
            grid_columns = [(x[0] or form.ANY_TEXT_FIELD) for x in first_sub_entry[1]]
            # Remove ANY_TEXT_FIELD placeholder
            if form.ANY_TEXT_FIELD in grid_columns:
                grid_columns.remove(form.ANY_TEXT_FIELD)

        # Collect all row labels from sub_entries
        grid_rows = []
        question_ids = []
        required_status = []

        for sub_entry in sub_entries:
            # Get row label from sub_entry[3]
            if len(sub_entry) > 3 and sub_entry[3]:
                if isinstance(sub_entry[3], list):
                    # If it's a list, join it or take the first element
                    row_label = ' - '.join(sub_entry[3]) if len(sub_entry[3]) > 1 else sub_entry[3][0]
                else:
                    row_label = str(sub_entry[3])
                grid_rows.append(row_label)
            else:
                # Fallback row label
                grid_rows.append(f"Row {len(grid_rows) + 1}")

            # Collect question IDs and required status
            question_ids.append(sub_entry[0])
            required_status.append(sub_entry[2] == 1)

        # Determine the specific grid type based on analysis
        # Based on actual Google Forms data structure analysis:
        # - Multiple Choice Grid: sub_entry[11][0] = 0
        # - Checkbox Grid: sub_entry[11][0] = 1
        if len(grid_rows) > 1 and grid_columns and len(grid_columns) > 1:
            # Default to multiple choice grid
            question_type = "multiple_choice_grid"
            question_type_name = "Multiple Choice Grid"

            # Check the first sub_entry for grid type indicator
            # The indicator is in the last element of the additional data array
            first_sub_entry = sub_entries[0]
            if len(first_sub_entry) > 11 and first_sub_entry[11]:
                # Check if it's a list and has at least one element
                if isinstance(first_sub_entry[11], list) and len(first_sub_entry[11]) > 0:
                    grid_type_indicator = first_sub_entry[11][0]
                    if grid_type_indicator == 1:
                        question_type = "checkbox_grid"
                        question_type_name = "Checkbox Grid"

        # Create the grid question object
        question_obj = {
            "id": question_ids[0] if question_ids else first_sub_entry[0],  # Use first ID as primary
            "title": entry_name,
            "type": question_type,
            "type_id": entry_type_id,
            "type_name": question_type_name,
            "required": any(required_status),  # Required if any row is required
            "description": entry_name,
            "options": grid_columns,
            "is_open_ended": False,
            "grid_rows": grid_rows,
            "grid_columns": grid_columns,
            "grid_question_ids": question_ids,  # Store all question IDs for form submission
            "grid_required_status": required_status  # Store required status for each row
        }

        return question_obj

    def get_form_id(self) -> str:
        """
        Extract the form ID from the URL

        Returns:
            The form ID
        """
        # Try to extract the long unique ID from URLs like /forms/d/e/LONG_ID/viewform
        match = re.search(r'/forms/d/e/([^/]+)/', self.url)
        if match:
            return match.group(1)

        # Fallback for older or different URL structures if necessary (e.g. /forms/FORM_ID/...)
        if '/forms/' in self.url:
            parts = self.url.split('/forms/')
            if len(parts) > 1:
                # This part might still be too general, consider specific patterns if they exist
                form_id_candidate = parts[1].split('/')[0]
                # Avoid overly short or generic IDs like 'd' or 'e' from this fallback
                if len(form_id_candidate) > 5: # Arbitrary length to avoid 'd', 'e'
                    return form_id_candidate

        # Final fallback: replace slashes if no other pattern matches
        # This is less ideal as it might not be unique or clean
        return self.url.replace('/', '_').replace(':', '_')

    def get_form_title(self) -> str:
        """
        Get the form title

        Returns:
            The form title
        """
        return self.form_title

    def get_form_description(self) -> str:
        """
        Get the form description

        Returns:
            The form description
        """
        return self.form_description

    def get_questions(self, only_required: bool = False) -> List[Dict[str, Any]]:
        """
        Get all questions

        Args:
            only_required: If True, return only required questions

        Returns:
            List of question objects
        """
        if only_required:
            return [q for q in self.questions if q.get("required", False)]
        return self.questions

    def get_open_ended_questions(self, only_required: bool = False) -> List[Dict[str, Any]]:
        """
        Get all open-ended questions

        Args:
            only_required: If True, return only required questions

        Returns:
            List of open-ended question objects
        """
        questions = self.get_questions(only_required)
        return [q for q in questions if q.get("is_open_ended", False)]

    def get_multiple_choice_questions(self, only_required: bool = False) -> List[Dict[str, Any]]:
        """
        Get all multiple choice questions

        Args:
            only_required: If True, return only required questions

        Returns:
            List of multiple choice question objects
        """
        questions = self.get_questions(only_required)
        return [q for q in questions if q.get("type") in ["multiple_choice", "dropdown", "checkboxes"]]

    def display_form_structure(self) -> str:
        """
        Generate a text representation of the form structure

        Returns:
            Text representation of the form
        """
        output = f"Form: {self.form_title}\n"
        if self.form_description:
            output += f"Description: {self.form_description}\n"
        output += f"URL: {self.url}\n"
        output += f"Number of questions: {len(self.questions)}\n\n"

        for i, question in enumerate(self.questions, 1):
            output += f"Question {i}: {question['title']}\n"
            output += f"  Type: {question['type_name']}\n"
            output += f"  Required: {'Yes' if question['required'] else 'No'}\n"

            if question.get("description"):
                output += f"  Description: {question['description']}\n"

            if question.get("options"):
                output += "  Options:\n"
                for j, option in enumerate(question["options"], 1):
                    output += f"    {j}. {option}\n"

            output += "\n"

        return output
