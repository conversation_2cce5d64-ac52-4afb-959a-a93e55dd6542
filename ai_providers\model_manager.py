"""
AI Model Manager for Google Form AutoFill

This module provides a manager for fetching and caching available models from AI providers.
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ModelManager:
    """Manager for fetching and caching available models from AI providers"""
    
    def __init__(self, cache_dir: str = "models_cache", cache_ttl: int = 86400):
        """
        Initialize the model manager
        
        Args:
            cache_dir: Directory to store model cache files
            cache_ttl: Time-to-live for cache in seconds (default: 24 hours)
        """
        self.cache_dir = cache_dir
        self.cache_ttl = cache_ttl
        
        # Create cache directory if it doesn't exist
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            
    def get_models(self, provider_name: str, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get available models for a provider
        
        Args:
            provider_name: Name of the provider
            force_refresh: Whether to force a refresh of the cache
            
        Returns:
            List of model information dictionaries
        """
        cache_file = os.path.join(self.cache_dir, f"{provider_name.lower()}_models.json")
        
        # Check if cache exists and is valid
        if not force_refresh and os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                # Check if cache is still valid
                if time.time() - cache_data.get('timestamp', 0) < self.cache_ttl:
                    logger.info(f"Using cached models for {provider_name}")
                    return cache_data.get('models', [])
            except Exception as e:
                logger.warning(f"Error reading cache file: {str(e)}")
                
        # Cache is invalid or doesn't exist, return empty list
        # (The actual fetching is done by the provider and then saved to cache)
        return []
        
    def save_models(self, provider_name: str, models: List[Dict[str, Any]]) -> bool:
        """
        Save models to cache
        
        Args:
            provider_name: Name of the provider
            models: List of model information dictionaries
            
        Returns:
            True if successful, False otherwise
        """
        cache_file = os.path.join(self.cache_dir, f"{provider_name.lower()}_models.json")
        
        try:
            cache_data = {
                'timestamp': time.time(),
                'models': models
            }
            
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
            logger.info(f"Saved {len(models)} models to cache for {provider_name}")
            return True
        except Exception as e:
            logger.error(f"Error saving models to cache: {str(e)}")
            return False
            
    def clear_cache(self, provider_name: Optional[str] = None) -> bool:
        """
        Clear the model cache
        
        Args:
            provider_name: Name of the provider to clear cache for, or None to clear all
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if provider_name:
                # Clear cache for specific provider
                cache_file = os.path.join(self.cache_dir, f"{provider_name.lower()}_models.json")
                if os.path.exists(cache_file):
                    os.remove(cache_file)
                    logger.info(f"Cleared cache for {provider_name}")
            else:
                # Clear all caches
                for file in os.listdir(self.cache_dir):
                    if file.endswith("_models.json"):
                        os.remove(os.path.join(self.cache_dir, file))
                logger.info("Cleared all model caches")
                
            return True
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            return False
