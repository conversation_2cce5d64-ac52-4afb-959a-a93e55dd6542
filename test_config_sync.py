#!/usr/bin/env python3
"""
Test script to verify the configuration sync fixes
"""

import requests
import json
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_endpoints():
    """Test the new configuration API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Configuration Sync Fixes")
    print("=" * 50)
    
    # Test 1: Check if config endpoints exist
    print("\n1. Testing /api/config/providers endpoint...")
    try:
        response = requests.get(f"{base_url}/api/config/providers")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ /api/config/providers endpoint working")
                print(f"   Default provider: {data.get('default_provider')}")
                print(f"   Available providers: {list(data.get('providers', {}).keys())}")
            else:
                print("❌ /api/config/providers returned error:", data.get('error'))
        else:
            print(f"❌ /api/config/providers returned status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the web app is running on localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Error testing /api/config/providers: {e}")
        return False
    
    # Test 2: Check default config endpoint
    print("\n2. Testing /api/config/default endpoint...")
    try:
        response = requests.get(f"{base_url}/api/config/default")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ /api/config/default endpoint working")
                print(f"   Default provider: {data.get('default_provider')}")
                print(f"   Default model: {data.get('default_model')}")
                print(f"   API keys available: {data.get('api_keys_available')}")
            else:
                print("❌ /api/config/default returned error:", data.get('error'))
        else:
            print(f"❌ /api/config/default returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing /api/config/default: {e}")
        return False
    
    # Test 3: Check if generate_for_question endpoint exists
    print("\n3. Testing /api/generate_for_question endpoint (structure only)...")
    try:
        # We'll just test if the endpoint exists by sending an invalid request
        response = requests.post(f"{base_url}/api/generate_for_question", data={})
        if response.status_code == 400:  # Expected for missing parameters
            data = response.json()
            if 'Missing required parameters' in data.get('error', ''):
                print("✅ /api/generate_for_question endpoint exists and validates parameters")
            else:
                print("⚠️  /api/generate_for_question exists but unexpected error:", data.get('error'))
        else:
            print(f"⚠️  /api/generate_for_question returned unexpected status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing /api/generate_for_question: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ Configuration sync fixes appear to be working!")
    print("\n📋 What was fixed:")
    print("   • AI Provider selection now syncs with config.html settings")
    print("   • API Key field is truly optional when keys are configured")
    print("   • Default models are automatically selected")
    print("   • Better error messages for missing configuration")
    
    return True

def check_config_file():
    """Check if config.json exists and has the expected structure"""
    print("\n4. Checking config.json structure...")
    
    config_file = "config.json"
    if not os.path.exists(config_file):
        print("⚠️  config.json not found. It will be created when you access /config")
        return True
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Check for expected structure
        if 'ai_providers' in config:
            print("✅ config.json has ai_providers section")
            
            ai_providers = config['ai_providers']
            for provider_name, provider_config in ai_providers.items():
                print(f"   • {provider_name}: enabled={provider_config.get('enabled')}, "
                      f"default={provider_config.get('is_default')}, "
                      f"keys={len(provider_config.get('api_keys', []))}")
        else:
            print("⚠️  config.json missing ai_providers section")
            
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Configuration Sync Test")
    print("Make sure the web application is running (python run_web.py)")
    
    # Check config file first
    check_config_file()
    
    # Test the endpoints
    success = test_config_endpoints()
    
    if success:
        print("\n🎉 All tests passed! The configuration sync fixes are working.")
        print("\n📝 To test manually:")
        print("   1. Go to http://localhost:5000/config")
        print("   2. Set a default AI provider and model")
        print("   3. Add API keys for the provider")
        print("   4. Go to any form details page")
        print("   5. Open the 'Generate for This Question' modal")
        print("   6. Verify the AI provider and model are pre-selected")
        print("   7. Verify API key field shows 'Optional' if keys are configured")
    else:
        print("\n❌ Some tests failed. Please check the server logs.")
        sys.exit(1)
