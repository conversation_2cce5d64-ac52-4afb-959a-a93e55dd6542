"""
AI Provider Factory for Google Form AutoFill

This module provides a factory for creating AI provider instances.
"""

import os
import logging
from typing import Dict, Any, Optional, List, Union

from .base_provider import AIProvider

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProviderFactory:
    """Factory for creating AI provider instances"""
    
    _providers = {}  # Class-level registry of provider classes
    
    @classmethod
    def register_provider(cls, provider_name: str, provider_class):
        """
        Register a provider class with the factory
        
        Args:
            provider_name: Name of the provider
            provider_class: Provider class to register
        """
        cls._providers[provider_name.lower()] = provider_class
        logger.info(f"Registered provider: {provider_name}")
        
    @classmethod
    def create_provider(cls, provider_name: str, api_keys: Union[str, List[str]], 
                       model: str = None, key_strategy: str = "round_robin") -> Optional[AIProvider]:
        """
        Create a provider instance
        
        Args:
            provider_name: Name of the provider to create
            api_keys: API key(s) for the provider
            model: Model to use
            key_strategy: Key selection strategy
            
        Returns:
            Provider instance or None if provider not found
        """
        provider_class = cls._providers.get(provider_name.lower())
        
        if not provider_class:
            logger.error(f"Provider not found: {provider_name}")
            return None
            
        try:
            provider = provider_class(api_keys, model, key_strategy)
            logger.info(f"Created provider: {provider_name}")
            return provider
        except Exception as e:
            logger.error(f"Error creating provider {provider_name}: {str(e)}")
            return None
            
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """
        Get a list of available provider names
        
        Returns:
            List of provider names
        """
        return list(cls._providers.keys())
        
    @classmethod
    def get_provider_info(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all registered providers
        
        Returns:
            Dictionary with provider information
        """
        return {
            name: {
                "class": provider_class.__name__,
                "module": provider_class.__module__
            }
            for name, provider_class in cls._providers.items()
        }
