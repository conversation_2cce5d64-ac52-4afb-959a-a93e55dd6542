"""
Submission Scheduler Module for Google Form AutoFill

This module provides functionality for scheduling form submissions
at specific times or with specific intervals.
"""

import json
import os
import time
import threading
import heapq
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
from datetime import datetime, timedelta

from customer_request import CustomerRequest, CustomerRequestManager
from submission_queue import SubmissionQueue


class ScheduledSubmission:
    """Class representing a scheduled submission"""
    
    def __init__(self, request_id: str, scheduled_time: Union[int, float], 
                 recurrence: Optional[str] = None, interval: Optional[int] = None):
        """
        Initialize a scheduled submission
        
        Args:
            request_id: The ID of the customer request
            scheduled_time: Unix timestamp for when to schedule the submission
            recurrence: Optional recurrence pattern (daily, weekly, monthly, custom)
            interval: Optional interval for custom recurrence (in seconds)
        """
        self.id = f"{request_id}_{int(time.time())}"
        self.request_id = request_id
        self.scheduled_time = scheduled_time
        self.recurrence = recurrence
        self.interval = interval
        self.created_at = time.time()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the scheduled submission to a dictionary for storage"""
        return {
            "id": self.id,
            "request_id": self.request_id,
            "scheduled_time": self.scheduled_time,
            "recurrence": self.recurrence,
            "interval": self.interval,
            "created_at": self.created_at
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScheduledSubmission':
        """Create a ScheduledSubmission instance from a dictionary"""
        scheduled = cls(
            request_id=data.get("request_id", ""),
            scheduled_time=data.get("scheduled_time", 0),
            recurrence=data.get("recurrence"),
            interval=data.get("interval")
        )
        
        # Set additional attributes
        scheduled.id = data.get("id", scheduled.id)
        scheduled.created_at = data.get("created_at", scheduled.created_at)
        
        return scheduled
        
    def get_next_scheduled_time(self) -> Optional[float]:
        """
        Calculate the next scheduled time based on recurrence pattern
        
        Returns:
            Unix timestamp for the next scheduled time, or None if no recurrence
        """
        if not self.recurrence:
            return None
            
        now = datetime.now()
        current_time = datetime.fromtimestamp(self.scheduled_time)
        
        if self.recurrence == "daily":
            # Schedule for the same time tomorrow
            next_time = current_time + timedelta(days=1)
        elif self.recurrence == "weekly":
            # Schedule for the same time next week
            next_time = current_time + timedelta(weeks=1)
        elif self.recurrence == "monthly":
            # Schedule for the same day/time next month
            # This is a bit tricky due to varying month lengths
            month = current_time.month + 1
            year = current_time.year
            
            if month > 12:
                month = 1
                year += 1
                
            # Handle month length differences
            day = min(current_time.day, [31, 29 if year % 4 == 0 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month-1])
            
            next_time = current_time.replace(year=year, month=month, day=day)
        elif self.recurrence == "custom" and self.interval:
            # Schedule based on custom interval (in seconds)
            next_time = current_time + timedelta(seconds=self.interval)
        else:
            return None
            
        # If the calculated next time is in the past, keep adding intervals until it's in the future
        while next_time.timestamp() <= now.timestamp():
            if self.recurrence == "daily":
                next_time += timedelta(days=1)
            elif self.recurrence == "weekly":
                next_time += timedelta(weeks=1)
            elif self.recurrence == "monthly":
                month = next_time.month + 1
                year = next_time.year
                
                if month > 12:
                    month = 1
                    year += 1
                    
                day = min(next_time.day, [31, 29 if year % 4 == 0 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month-1])
                
                next_time = next_time.replace(year=year, month=month, day=day)
            elif self.recurrence == "custom" and self.interval:
                next_time += timedelta(seconds=self.interval)
            else:
                return None
                
        return next_time.timestamp()


class SubmissionScheduler:
    """Class for scheduling form submissions"""
    
    def __init__(self, storage_dir: str = "responses/submission_scheduler"):
        """
        Initialize the submission scheduler
        
        Args:
            storage_dir: Directory to store scheduler data
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
        self.request_manager = CustomerRequestManager()
        self.submission_queue = SubmissionQueue()
        
        self.schedule = []  # Priority queue [(scheduled_time, scheduled_id), ...]
        self.schedule_lock = threading.Lock()
        self.scheduler_thread = None
        self.running = False
        self._stop_scheduler = threading.Event()
        
        # Load schedule from storage
        self._load_schedule()
        
    def _get_schedule_filename(self) -> str:
        """Get the filename for the schedule data"""
        return os.path.join(self.storage_dir, "submission_schedule.json")
        
    def _get_scheduled_filename(self, scheduled_id: str) -> str:
        """Get the filename for a specific scheduled submission"""
        return os.path.join(self.storage_dir, f"{scheduled_id}.json")
        
    def _load_schedule(self) -> None:
        """Load the schedule from storage"""
        filename = self._get_schedule_filename()
        if not os.path.exists(filename):
            self.schedule = []
            return
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                schedule_data = json.load(f)
                
            # Convert to priority queue format
            self.schedule = []
            for item in schedule_data:
                scheduled_time = item.get("scheduled_time", 0)
                scheduled_id = item.get("id")
                
                if scheduled_id:
                    heapq.heappush(self.schedule, (scheduled_time, scheduled_id))
        except (json.JSONDecodeError, FileNotFoundError):
            self.schedule = []
            
    def _save_schedule(self) -> None:
        """Save the schedule to storage"""
        filename = self._get_schedule_filename()
        
        # Convert priority queue to list of dictionaries for storage
        schedule_data = []
        for scheduled_time, scheduled_id in self.schedule:
            # Load the full scheduled submission data
            scheduled = self._load_scheduled_submission(scheduled_id)
            if scheduled:
                schedule_data.append(scheduled.to_dict())
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(schedule_data, f, ensure_ascii=False, indent=2)
            
    def _save_scheduled_submission(self, scheduled: ScheduledSubmission) -> None:
        """Save a scheduled submission to storage"""
        filename = self._get_scheduled_filename(scheduled.id)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(scheduled.to_dict(), f, ensure_ascii=False, indent=2)
            
    def _load_scheduled_submission(self, scheduled_id: str) -> Optional[ScheduledSubmission]:
        """Load a scheduled submission from storage"""
        filename = self._get_scheduled_filename(scheduled_id)
        if not os.path.exists(filename):
            return None
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return ScheduledSubmission.from_dict(data)
        except (json.JSONDecodeError, FileNotFoundError):
            return None
            
    def _delete_scheduled_submission(self, scheduled_id: str) -> bool:
        """Delete a scheduled submission from storage"""
        filename = self._get_scheduled_filename(scheduled_id)
        if not os.path.exists(filename):
            return False
            
        try:
            os.remove(filename)
            return True
        except OSError:
            return False
            
    def schedule_submission(self, request_id: str, scheduled_time: Union[int, float, datetime],
                           recurrence: Optional[str] = None, 
                           interval: Optional[int] = None) -> Optional[str]:
        """
        Schedule a submission for a specific time
        
        Args:
            request_id: The ID of the customer request
            scheduled_time: When to schedule the submission (timestamp or datetime)
            recurrence: Optional recurrence pattern (daily, weekly, monthly, custom)
            interval: Optional interval for custom recurrence (in seconds)
            
        Returns:
            The ID of the scheduled submission, or None if failed
        """
        # Verify the request exists
        request = self.request_manager.get_request(request_id)
        if not request:
            return None
            
        # Convert datetime to timestamp if needed
        if isinstance(scheduled_time, datetime):
            scheduled_time = scheduled_time.timestamp()
            
        # Create scheduled submission
        scheduled = ScheduledSubmission(
            request_id=request_id,
            scheduled_time=scheduled_time,
            recurrence=recurrence,
            interval=interval
        )
        
        # Update request with scheduled time
        request.scheduled_time = scheduled_time
        self.request_manager.update_request(request)
        
        # Save scheduled submission
        self._save_scheduled_submission(scheduled)
        
        # Add to schedule queue
        with self.schedule_lock:
            heapq.heappush(self.schedule, (scheduled_time, scheduled.id))
            self._save_schedule()
            
        return scheduled.id
        
    def reschedule_submission(self, scheduled_id: str, 
                             new_time: Union[int, float, datetime]) -> bool:
        """
        Reschedule a submission to a new time
        
        Args:
            scheduled_id: The ID of the scheduled submission
            new_time: The new scheduled time (timestamp or datetime)
            
        Returns:
            True if successful, False otherwise
        """
        # Load the scheduled submission
        scheduled = self._load_scheduled_submission(scheduled_id)
        if not scheduled:
            return False
            
        # Convert datetime to timestamp if needed
        if isinstance(new_time, datetime):
            new_time = new_time.timestamp()
            
        # Update the scheduled time
        scheduled.scheduled_time = new_time
        self._save_scheduled_submission(scheduled)
        
        # Update the schedule queue
        with self.schedule_lock:
            # Remove the old entry
            self.schedule = [(t, s_id) for t, s_id in self.schedule if s_id != scheduled_id]
            heapq.heapify(self.schedule)
            
            # Add the new entry
            heapq.heappush(self.schedule, (new_time, scheduled_id))
            self._save_schedule()
            
        # Update the request with the new scheduled time
        request = self.request_manager.get_request(scheduled.request_id)
        if request:
            request.scheduled_time = new_time
            self.request_manager.update_request(request)
            
        return True
        
    def cancel_scheduled_submission(self, scheduled_id: str) -> bool:
        """
        Cancel a scheduled submission
        
        Args:
            scheduled_id: The ID of the scheduled submission
            
        Returns:
            True if successful, False otherwise
        """
        # Load the scheduled submission
        scheduled = self._load_scheduled_submission(scheduled_id)
        if not scheduled:
            return False
            
        # Remove from schedule queue
        with self.schedule_lock:
            self.schedule = [(t, s_id) for t, s_id in self.schedule if s_id != scheduled_id]
            heapq.heapify(self.schedule)
            self._save_schedule()
            
        # Delete the scheduled submission
        self._delete_scheduled_submission(scheduled_id)
        
        # Update the request to clear scheduled time
        request = self.request_manager.get_request(scheduled.request_id)
        if request:
            request.scheduled_time = None
            self.request_manager.update_request(request)
            
        return True
        
    def get_scheduled_submission(self, scheduled_id: str) -> Optional[Dict[str, Any]]:
        """
        Get details of a scheduled submission
        
        Args:
            scheduled_id: The ID of the scheduled submission
            
        Returns:
            Dictionary with scheduled submission details, or None if not found
        """
        scheduled = self._load_scheduled_submission(scheduled_id)
        if not scheduled:
            return None
            
        # Get request details
        request = self.request_manager.get_request(scheduled.request_id)
        if not request:
            return scheduled.to_dict()
            
        # Combine scheduled and request details
        result = scheduled.to_dict()
        result.update({
            "form_title": request.form_title,
            "form_url": request.form_url,
            "submission_count": request.submission_count,
            "customer_id": request.customer_id,
            "scheduled_datetime": datetime.fromtimestamp(scheduled.scheduled_time).strftime("%Y-%m-%d %H:%M:%S")
        })
        
        return result
        
    def get_all_scheduled_submissions(self) -> List[Dict[str, Any]]:
        """
        Get all scheduled submissions
        
        Returns:
            List of dictionaries with scheduled submission details
        """
        with self.schedule_lock:
            result = []
            
            for scheduled_time, scheduled_id in self.schedule:
                details = self.get_scheduled_submission(scheduled_id)
                if details:
                    result.append(details)
                    
            return result
            
    def get_upcoming_submissions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get upcoming scheduled submissions
        
        Args:
            limit: Maximum number of submissions to return
            
        Returns:
            List of dictionaries with scheduled submission details
        """
        with self.schedule_lock:
            # Sort by scheduled time
            sorted_schedule = sorted(self.schedule, key=lambda x: x[0])
            
            result = []
            for scheduled_time, scheduled_id in sorted_schedule[:limit]:
                details = self.get_scheduled_submission(scheduled_id)
                if details:
                    result.append(details)
                    
            return result
            
    def start_scheduler(self, check_interval: int = 60) -> bool:
        """
        Start the scheduler in a background thread
        
        Args:
            check_interval: How often to check for scheduled submissions (in seconds)
            
        Returns:
            True if scheduler started, False if already running
        """
        if self.running:
            return False
            
        self.running = True
        self._stop_scheduler.clear()
        
        self.scheduler_thread = threading.Thread(
            target=self._run_scheduler,
            args=(check_interval,),
            daemon=True
        )
        self.scheduler_thread.start()
        
        return True
        
    def stop_scheduler(self) -> bool:
        """
        Stop the scheduler
        
        Returns:
            True if scheduler was stopped, False if not running
        """
        if not self.running:
            return False
            
        self._stop_scheduler.set()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
            
        self.running = False
        return True
        
    def is_running(self) -> bool:
        """
        Check if the scheduler is running
        
        Returns:
            True if running, False otherwise
        """
        return self.running and self.scheduler_thread and self.scheduler_thread.is_alive()
        
    def _run_scheduler(self, check_interval: int) -> None:
        """
        Run the scheduler in a background thread
        
        Args:
            check_interval: How often to check for scheduled submissions (in seconds)
        """
        while not self._stop_scheduler.is_set():
            now = time.time()
            
            with self.schedule_lock:
                # Check if there are any scheduled submissions ready to process
                while self.schedule and self.schedule[0][0] <= now:
                    # Pop the next scheduled submission
                    scheduled_time, scheduled_id = heapq.heappop(self.schedule)
                    
                    # Load the scheduled submission
                    scheduled = self._load_scheduled_submission(scheduled_id)
                    if not scheduled:
                        continue
                        
                    # Add the request to the submission queue
                    self.submission_queue.add_to_queue(scheduled.request_id)
                    
                    # If this is a recurring submission, schedule the next occurrence
                    next_time = scheduled.get_next_scheduled_time()
                    if next_time:
                        # Update the scheduled time
                        scheduled.scheduled_time = next_time
                        self._save_scheduled_submission(scheduled)
                        
                        # Add back to the schedule queue with the new time
                        heapq.heappush(self.schedule, (next_time, scheduled_id))
                    else:
                        # Non-recurring submission, delete it
                        self._delete_scheduled_submission(scheduled_id)
                        
                # Save the updated schedule
                self._save_schedule()
                
            # Wait for the next check interval
            time.sleep(check_interval)
