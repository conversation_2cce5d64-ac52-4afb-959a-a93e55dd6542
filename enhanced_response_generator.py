"""
Enhanced Response Generator Module for Google Form AutoFill

This module extends the basic response generator with advanced capabilities:
- Support for customer-provided examples
- Style matching for responses
- Batch generation optimization
- Quality control mechanisms
- Feedback loop system
- Response diversity control
"""

import json
import random
import time
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

import gemini_client
from form_enhanced import EnhancedFormParser
from response_storage import FormResponseManager
from response_generator import ResponseGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedResponseGenerator(ResponseGenerator):
    """Enhanced class for generating responses with advanced capabilities"""

    def __init__(self, gemini: Optional[gemini_client.GeminiClient] = None):
        """
        Initialize the enhanced response generator

        Args:
            gemini: Optional GeminiClient instance
        """
        super().__init__(gemini)
        self.quality_threshold = 0.7  # Default quality threshold (0.0-1.0)
        self.diversity_factor = 0.8   # Default diversity factor (0.0-1.0)
        self.style_guidance = {}      # Style guidance for different question types
        self.feedback_history = {}    # Store feedback history for learning

    def generate_responses_with_examples(self,
                                        form_parser: EnhancedFormParser,
                                        examples: Dict[str, List[str]],
                                        sample_count: int = 5,
                                        quality_threshold: float = 0.7,
                                        diversity_factor: float = 0.8,
                                        style_guidance: Optional[Dict[str, str]] = None) -> str:
        """
        Generate responses using customer-provided examples with style matching

        Args:
            form_parser: EnhancedFormParser instance with loaded form
            examples: Dictionary mapping question IDs to example responses
            sample_count: Number of samples to generate per question
            quality_threshold: Minimum quality score for responses (0.0-1.0)
            diversity_factor: Factor controlling response diversity (0.0-1.0)
            style_guidance: Optional dictionary with style guidance for different question types

        Returns:
            Status message
        """
        if not form_parser.questions:
            return "Error: Form not loaded or contains no questions"

        if not self.gemini:
            return "Error: Gemini client not initialized"

        # Set quality and diversity parameters
        self.quality_threshold = quality_threshold
        self.diversity_factor = diversity_factor
        if style_guidance:
            self.style_guidance = style_guidance

        # Initialize form data in storage
        form_id = form_parser.get_form_id()
        form_title = form_parser.get_form_title()

        # Check if form data already exists
        existing_data = self.response_manager.load_form_data(form_id)
        if not existing_data:
            # Create new form data
            self.response_manager.initialize_form_data(form_id, form_title, form_parser.questions)

        # Initialize non-open-ended questions with equal weights
        self._initialize_non_open_ended_questions(form_parser, form_id)

        # Store examples in form data for future reference
        self._store_examples(form_id, examples)

        # Generate responses for open-ended questions
        return self._generate_with_examples(form_parser, form_id, examples, sample_count)

    def _store_examples(self, form_id: str, examples: Dict[str, List[str]]) -> None:
        """
        Store customer-provided examples in form data

        Args:
            form_id: Form ID
            examples: Dictionary mapping question IDs to example responses
        """
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data:
            logger.warning(f"Cannot store examples: Form data not found for form_id: {form_id}")
            return

        # Create examples section if it doesn't exist
        if "examples" not in form_data:
            form_data["examples"] = {}

        # Store examples for each question
        for question_id, question_examples in examples.items():
            form_data["examples"][question_id] = question_examples

        # Save updated form data
        self.response_manager.save_form_data(form_id, form_data)
        logger.info(f"Stored examples for {len(examples)} questions in form {form_id}")

    def _generate_with_examples(self,
                               form_parser: EnhancedFormParser,
                               form_id: str,
                               examples: Dict[str, List[str]],
                               sample_count: int) -> str:
        """
        Generate responses for open-ended questions using examples

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            examples: Dictionary mapping question IDs to example responses
            sample_count: Number of samples to generate per question

        Returns:
            Status message
        """
        questions = form_parser.get_questions()
        processed_count = 0
        skipped_count = 0
        failed_count = 0

        # Group questions by whether they have examples
        questions_with_examples = []
        questions_without_examples = []

        for question in questions:
            question_id = question["id"]
            
            # Skip questions with default values
            if "default_value" in question:
                continue
                
            # Skip non-open-ended questions
            if not question.get("is_open_ended", False):
                if question["type"] in ["multiple_choice", "dropdown", "checkboxes"]:
                    skipped_count += 1
                continue
                
            # Check if this question has examples
            if str(question_id) in examples and examples[str(question_id)]:
                questions_with_examples.append(question)
            else:
                questions_without_examples.append(question)

        # Process questions with examples first
        for question in questions_with_examples:
            question_id = question["id"]
            question_title = question["title"]
            question_examples = examples.get(str(question_id), [])
            
            try:
                # Extract style guidance for this question type
                question_type = question.get("type", "text")
                style_guide = self.style_guidance.get(question_type, "")
                
                # Generate responses with style matching
                responses, quality_scores = self._generate_style_matched_responses(
                    question_title, 
                    question_examples,
                    sample_count,
                    style_guide
                )
                
                # Filter responses by quality threshold
                filtered_responses = []
                for response, score in zip(responses, quality_scores):
                    if score >= self.quality_threshold:
                        filtered_responses.append((response, score))
                
                # If we don't have enough high-quality responses, add some lower quality ones
                if len(filtered_responses) < sample_count // 2:
                    # Sort remaining responses by quality
                    remaining = [(r, s) for r, s in zip(responses, quality_scores) 
                                if (r, s) not in filtered_responses]
                    remaining.sort(key=lambda x: x[1], reverse=True)
                    
                    # Add enough to reach at least half the requested count
                    needed = max(sample_count // 2 - len(filtered_responses), 0)
                    filtered_responses.extend(remaining[:needed])
                
                # Save responses with quality-based weights
                for response, score in filtered_responses:
                    # Convert quality score to weight (higher quality = higher weight)
                    weight = max(int(score * 10), 1)
                    self.response_manager.add_response(form_id, question_id, response, weight)
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Error generating responses for question '{question_title}': {e}")
                failed_count += 1

        # Process questions without examples using standard method
        if questions_without_examples:
            for question in questions_without_examples:
                question_id = question["id"]
                question_title = question["title"]
                
                try:
                    # Generate open-ended responses using standard method
                    prompt = gemini_client.create_open_ended_prompt(
                        question_title,
                        count=sample_count
                    )
                    
                    response_text = gemini_client.generate_with_retry(self.gemini, prompt)
                    responses = gemini_client.parse_open_ended_responses(response_text, sample_count)
                    
                    # Apply diversity filtering
                    responses = self._apply_diversity_filter(responses)
                    
                    # Save each response with equal weight
                    for response in responses:
                        self.response_manager.add_response(form_id, question_id, response, weight=1)
                    
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Error generating responses for question '{question_title}': {e}")
                    failed_count += 1

        result_msg = f"Generated responses for {processed_count} open-ended questions using example-based method"
        
        if failed_count > 0:
            result_msg += f"\nFailed to generate responses for {failed_count} questions"
            
        if skipped_count > 0:
            result_msg += f"\nSkipped {skipped_count} non-open-ended questions (use manual weight adjustment for these)"
            
        return result_msg

    def _generate_style_matched_responses(self, 
                                         question: str, 
                                         examples: List[str],
                                         count: int,
                                         style_guide: str = "") -> Tuple[List[str], List[float]]:
        """
        Generate responses that match the style of provided examples

        Args:
            question: The question text
            examples: List of example responses
            count: Number of responses to generate
            style_guide: Optional style guidance

        Returns:
            Tuple of (list of responses, list of quality scores)
        """
        # Create a prompt that emphasizes style matching
        prompt = self._create_style_matching_prompt(question, examples, count, style_guide)
        
        # Generate responses
        response_text = gemini_client.generate_with_retry(self.gemini, prompt, max_tokens=4096)
        
        # Parse responses and quality scores
        responses, quality_scores = self._parse_responses_with_quality(response_text, count)
        
        # Apply diversity filtering
        responses, quality_scores = self._apply_diversity_filter_with_quality(responses, quality_scores)
        
        return responses, quality_scores

    def _create_style_matching_prompt(self, 
                                     question: str, 
                                     examples: List[str],
                                     count: int,
                                     style_guide: str = "") -> str:
        """
        Create a prompt for generating style-matched responses

        Args:
            question: The question text
            examples: List of example responses
            count: Number of responses to generate
            style_guide: Optional style guidance

        Returns:
            Prompt for the Gemini model
        """
        prompt = f"Generate {count} different responses to the following question that match the style and content of the provided examples:\n\n"
        prompt += f"Question: {question}\n\n"
        
        prompt += "Example responses:\n"
        for i, example in enumerate(examples, 1):
            prompt += f"Example {i}: {example}\n"
        
        if style_guide:
            prompt += f"\nStyle guidance: {style_guide}\n"
        
        prompt += f"\nPlease provide {count} unique responses that:\n"
        prompt += "1. Match the tone, style, and level of detail of the examples\n"
        prompt += "2. Are diverse and not repetitive\n"
        prompt += "3. Are realistic and could be written by a real person\n"
        prompt += "4. Maintain similar length and complexity to the examples\n\n"
        
        prompt += "For each response, also provide a quality score between 0.0 and 1.0 that represents how well the response matches the style of the examples.\n\n"
        
        prompt += "Return your response as a JSON array where each element is an object with 'response' and 'quality' fields:\n"
        prompt += "```json\n"
        prompt += '[\n'
        prompt += '  {"response": "First response text", "quality": 0.95},\n'
        prompt += '  {"response": "Second response text", "quality": 0.87},\n'
        prompt += '  ...\n'
        prompt += ']\n'
        prompt += "```\n\n"
        
        return prompt

    def _parse_responses_with_quality(self, response_text: str, count: int) -> Tuple[List[str], List[float]]:
        """
        Parse responses and quality scores from Gemini's output

        Args:
            response_text: The text response from Gemini
            count: Expected number of responses

        Returns:
            Tuple of (list of responses, list of quality scores)
        """
        responses = []
        quality_scores = []
        
        try:
            # Try to extract JSON array from the response
            import re
            import json
            
            # Look for JSON-like structure between triple backticks or anywhere in the text
            json_pattern = r'```(?:json)?\s*(\[[\s\S]*?\])```|(\[[\s\S]*?\])'
            match = re.search(json_pattern, response_text)
            
            if match:
                json_str = match.group(1) or match.group(2)
                response_list = json.loads(json_str)
                
                if isinstance(response_list, list):
                    for item in response_list:
                        if isinstance(item, dict) and 'response' in item and 'quality' in item:
                            response = str(item['response']).strip()
                            quality = float(item['quality'])
                            
                            # Validate quality score
                            quality = max(0.0, min(1.0, quality))
                            
                            if response:
                                responses.append(response)
                                quality_scores.append(quality)
            
        except Exception as e:
            logger.error(f"Error parsing JSON responses with quality: {e}")
        
        # Fallback: if parsing failed, try to extract just the responses
        if not responses:
            parsed_responses = gemini_client.parse_open_ended_responses(response_text, count)
            responses = parsed_responses
            # Assign default quality scores
            quality_scores = [0.7] * len(responses)
        
        return responses[:count], quality_scores[:count]

    def _apply_diversity_filter(self, responses: List[str]) -> List[str]:
        """
        Filter responses to ensure diversity

        Args:
            responses: List of responses

        Returns:
            Filtered list of responses
        """
        if not responses or len(responses) <= 1:
            return responses
            
        filtered = [responses[0]]  # Always keep the first response
        
        for response in responses[1:]:
            # Check similarity with existing filtered responses
            if self._is_diverse_enough(response, filtered):
                filtered.append(response)
                
            # Stop if we've reached the desired diversity
            if len(filtered) >= int(len(responses) * self.diversity_factor):
                break
                
        return filtered

    def _apply_diversity_filter_with_quality(self, 
                                           responses: List[str], 
                                           quality_scores: List[float]) -> Tuple[List[str], List[float]]:
        """
        Filter responses to ensure diversity while preserving quality scores

        Args:
            responses: List of responses
            quality_scores: List of quality scores

        Returns:
            Tuple of (filtered responses, filtered quality scores)
        """
        if not responses or len(responses) <= 1:
            return responses, quality_scores
            
        # Create pairs of (response, quality)
        pairs = list(zip(responses, quality_scores))
        
        # Sort by quality (highest first)
        pairs.sort(key=lambda x: x[1], reverse=True)
        
        filtered_pairs = [pairs[0]]  # Always keep the highest quality response
        
        for response, quality in pairs[1:]:
            # Check similarity with existing filtered responses
            if self._is_diverse_enough(response, [p[0] for p in filtered_pairs]):
                filtered_pairs.append((response, quality))
                
            # Stop if we've reached the desired diversity
            if len(filtered_pairs) >= int(len(responses) * self.diversity_factor):
                break
                
        # Unzip the filtered pairs
        filtered_responses, filtered_quality = zip(*filtered_pairs) if filtered_pairs else ([], [])
        
        return list(filtered_responses), list(filtered_quality)

    def _is_diverse_enough(self, new_response: str, existing_responses: List[str]) -> bool:
        """
        Check if a new response is diverse enough compared to existing ones

        Args:
            new_response: New response to check
            existing_responses: List of existing responses

        Returns:
            True if the new response is diverse enough, False otherwise
        """
        # Simple implementation: check word overlap
        new_words = set(new_response.lower().split())
        
        for existing in existing_responses:
            existing_words = set(existing.lower().split())
            
            # Calculate Jaccard similarity
            if len(new_words) == 0 or len(existing_words) == 0:
                continue
                
            intersection = len(new_words.intersection(existing_words))
            union = len(new_words.union(existing_words))
            similarity = intersection / union
            
            # If too similar to any existing response, reject
            if similarity > 0.7:  # Threshold for similarity
                return False
                
        return True

    def batch_generate(self,
                      form_parser: EnhancedFormParser,
                      form_id: str,
                      examples: Dict[str, List[str]],
                      batch_size: int = 10,
                      total_count: int = 100,
                      progress_callback=None) -> str:
        """
        Generate responses in batches for efficiency

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            examples: Dictionary mapping question IDs to example responses
            batch_size: Number of responses to generate in each batch
            total_count: Total number of responses to generate
            progress_callback: Optional callback function for progress updates

        Returns:
            Status message
        """
        if not form_parser.questions:
            return "Error: Form not loaded or contains no questions"

        if not self.gemini:
            return "Error: Gemini client not initialized"

        # Initialize form data if needed
        existing_data = self.response_manager.load_form_data(form_id)
        if not existing_data:
            self.response_manager.initialize_form_data(
                form_id, 
                form_parser.get_form_title(), 
                form_parser.questions
            )

        # Store examples
        self._store_examples(form_id, examples)

        # Get open-ended questions
        open_ended_questions = [q for q in form_parser.get_questions() 
                              if q.get("is_open_ended", False) and "default_value" not in q]

        if not open_ended_questions:
            return "No open-ended questions found in the form"

        # Calculate number of batches
        num_batches = (total_count + batch_size - 1) // batch_size
        
        processed_count = 0
        total_responses_generated = 0
        
        for batch_idx in range(num_batches):
            batch_start = time.time()
            
            # Calculate current batch size (last batch may be smaller)
            current_batch_size = min(batch_size, total_count - processed_count)
            
            if current_batch_size <= 0:
                break
                
            logger.info(f"Processing batch {batch_idx + 1}/{num_batches} (size: {current_batch_size})")
            
            # Process each question in this batch
            for question in open_ended_questions:
                question_id = question["id"]
                question_title = question["title"]
                
                # Get examples for this question
                question_examples = examples.get(str(question_id), [])
                
                try:
                    # Generate responses for this question in the current batch
                    if question_examples:
                        # Use style-matched generation if examples are available
                        style_guide = self.style_guidance.get(question.get("type", "text"), "")
                        responses, quality_scores = self._generate_style_matched_responses(
                            question_title,
                            question_examples,
                            current_batch_size,
                            style_guide
                        )
                    else:
                        # Use standard generation if no examples are available
                        prompt = gemini_client.create_open_ended_prompt(
                            question_title,
                            count=current_batch_size
                        )
                        response_text = gemini_client.generate_with_retry(self.gemini, prompt)
                        responses = gemini_client.parse_open_ended_responses(response_text, current_batch_size)
                        quality_scores = [0.7] * len(responses)  # Default quality
                    
                    # Apply diversity filtering
                    if question_examples:
                        responses, quality_scores = self._apply_diversity_filter_with_quality(responses, quality_scores)
                    else:
                        responses = self._apply_diversity_filter(responses)
                        quality_scores = [0.7] * len(responses)  # Default quality
                    
                    # Save responses
                    for response, quality in zip(responses, quality_scores):
                        # Convert quality to weight
                        weight = max(int(quality * 10), 1)
                        self.response_manager.add_response(form_id, question_id, response, weight)
                    
                    total_responses_generated += len(responses)
                    
                except Exception as e:
                    logger.error(f"Error in batch generation for question '{question_title}': {e}")
            
            # Update progress
            processed_count += current_batch_size
            progress = processed_count / total_count
            
            if progress_callback:
                progress_callback(progress, batch_idx + 1, num_batches)
                
            # Log batch completion
            batch_time = time.time() - batch_start
            logger.info(f"Batch {batch_idx + 1}/{num_batches} completed in {batch_time:.2f}s")
            
            # Small delay between batches to avoid rate limiting
            if batch_idx < num_batches - 1:
                time.sleep(1)
        
        return f"Generated {total_responses_generated} responses in {num_batches} batches"

    def apply_feedback(self, form_id: str, feedback_data: Dict[str, Any]) -> str:
        """
        Apply customer feedback to improve response quality

        Args:
            form_id: Form ID
            feedback_data: Dictionary with feedback data

        Returns:
            Status message
        """
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data:
            return f"Error: Form data not found for form_id: {form_id}"
            
        if "feedback" not in form_data:
            form_data["feedback"] = []
            
        # Add timestamp to feedback
        feedback_data["timestamp"] = time.time()
        
        # Store feedback
        form_data["feedback"].append(feedback_data)
        self.response_manager.save_form_data(form_id, form_data)
        
        # Apply feedback to adjust weights
        if "response_ratings" in feedback_data:
            self._adjust_weights_from_feedback(form_id, feedback_data["response_ratings"])
            
        return f"Applied feedback to form {form_id}"

    def _adjust_weights_from_feedback(self, form_id: str, ratings: Dict[str, Dict[str, float]]) -> None:
        """
        Adjust response weights based on feedback ratings

        Args:
            form_id: Form ID
            ratings: Dictionary mapping question IDs to dictionaries of response indices and ratings
        """
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data or "responses" not in form_data:
            return
            
        for question_id, question_ratings in ratings.items():
            if question_id not in form_data["responses"]:
                continue
                
            responses = form_data["responses"][question_id]
            
            for response_idx_str, rating in question_ratings.items():
                try:
                    response_idx = int(response_idx_str)
                    if 0 <= response_idx < len(responses):
                        # Adjust weight based on rating (scale of 1-5)
                        current_weight = responses[response_idx].get("weight", 1)
                        
                        # Increase weight for high ratings, decrease for low ratings
                        if rating >= 4.0:  # High rating
                            new_weight = current_weight * 1.5
                        elif rating <= 2.0:  # Low rating
                            new_weight = current_weight * 0.5
                        else:  # Neutral rating
                            new_weight = current_weight
                            
                        # Ensure weight is at least 1
                        new_weight = max(int(new_weight), 1)
                        
                        # Update weight
                        responses[response_idx]["weight"] = new_weight
                except (ValueError, IndexError):
                    continue
                    
        # Save updated form data
        self.response_manager.save_form_data(form_id, form_data)
