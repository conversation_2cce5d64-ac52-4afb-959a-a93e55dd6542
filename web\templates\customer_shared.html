{% extends "base.html" %}

{% block title %}Shared Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    .shared-card {
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        background: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
    }

    .shared-card:hover {
        box-shadow: var(--shadow-lg);
        border-color: var(--border-light);
    }

    .shared-card .card-body {
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
    }

    .shared-card .card-header {
        background: var(--bg-tertiary) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color);
    }

    .option-distribution {
        margin-top: 1rem;
    }

    .option-bar {
        height: 2rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        background: var(--bg-tertiary);
        border-radius: var(--radius-sm);
    }

    .option-progress {
        height: 100%;
        background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
        text-align: right;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 0.5rem;
        border-radius: var(--radius-sm);
        font-weight: 500;
    }

    .option-label {
        margin-bottom: 0.25rem;
        display: flex;
        justify-content: space-between;
        color: var(--text-primary);
        font-weight: 500;
    }

    .example-response {
        background: var(--bg-tertiary);
        padding: 1rem;
        border-radius: var(--radius-sm);
        margin-bottom: 1rem;
        border-left: 4px solid var(--accent-primary);
        color: var(--text-primary);
    }

    .summary-box {
        background: var(--bg-card) !important;
        padding: 1.5rem;
        border-radius: var(--radius-md);
        margin-bottom: 2rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .summary-item {
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        color: var(--text-primary);
    }

    .summary-label {
        font-weight: 600;
        color: var(--text-primary);
    }

    .summary-value {
        text-align: right;
        color: var(--text-primary);
        font-weight: 500;
    }

    .shared-banner {
        background: var(--bg-tertiary);
        padding: 1.5rem;
        border-radius: var(--radius-md);
        margin-bottom: 2rem;
        text-align: center;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .shared-banner h4 {
        color: var(--text-primary) !important;
        margin-bottom: 0;
    }

    .text-response-config {
        color: var(--text-primary);
    }

    .text-response-config h6 {
        color: var(--text-primary) !important;
        margin-bottom: 0.75rem;
    }

    .text-response-config p {
        color: var(--text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="shared-banner">
            <h4 class="mb-0">
                <i class="fas fa-share-alt me-2"></i>Shared Form Configuration
            </h4>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>{{ form_data.form_title }}
                </h4>
            </div>
            <div class="card-body">
                {% if form_data.form_description %}
                <p>{{ form_data.form_description }}</p>
                {% endif %}
            </div>
        </div>

        <div class="summary-box">
            <h5><i class="fas fa-chart-pie me-2"></i>Configuration Summary</h5>
            <div class="summary-item">
                <div class="summary-label">Total Responses Needed:</div>
                <div class="summary-value">{{ form_data.customer_config.total_responses }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Questions Configured:</div>
                <div class="summary-value">{{ form_data.customer_config.questions|length }} / {{ form_data.questions|length }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Configuration Date:</div>
                <div class="summary-value">{{ form_data.customer_config.created_at|int|timestamp_to_date }}</div>
            </div>
        </div>

        <h4 class="mb-4">Question Configuration Details</h4>

        {% for question in form_data.questions %}
        {% set question_id = question.id|string %}
        {% set question_config = form_data.customer_config.questions.get(question_id, {}) %}

        <div class="card shared-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                        {% if question.required %}Required{% else %}Optional{% endif %}
                    </span>
                    {{ question.title }}
                    <span class="badge bg-info ms-2">{{ question.type_name }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if question.description %}
                <p class="text-muted mb-3">{{ question.description }}</p>
                {% endif %}

                {% if question_config %}
                    {% if question.type in ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating'] %}
                        <!-- Option-based questions -->
                        <div class="option-distribution">
                            <h6>Response Distribution</h6>

                            {% for option in question.options %}
                                {% set percentage = question_config.options.get(option, 0) %}
                                <div class="option-label">
                                    <span>{{ option }}</span>
                                    <span>{{ percentage }}%</span>
                                </div>
                                <div class="option-bar">
                                    <div class="option-progress" style="width: {{ percentage }}%">
                                        {% if percentage > 10 %}{{ percentage }}%{% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Text-based questions -->
                        <div class="text-response-config">
                            {% if question_config.description %}
                            <h6>Response Guidelines</h6>
                            <p>{{ question_config.description }}</p>
                            {% endif %}

                            {% if question_config.examples %}
                            <h6>Example Responses</h6>
                            {% for example in question_config.examples %}
                            <div class="example-response">
                                {{ example }}
                            </div>
                            {% endfor %}
                            {% endif %}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This question has not been configured.
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4 mb-5">
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="fas fa-home me-1"></i>Go to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add a filter to format timestamps
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleString();
    }
</script>
{% endblock %}
