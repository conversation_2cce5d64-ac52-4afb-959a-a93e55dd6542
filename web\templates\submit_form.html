{% extends "base.html" %}

{% block title %}Submit Form - Google Form AutoFill{% endblock %}

{% block content %}
<!-- Calculate max possible submissions at the beginning -->
{% set questions_with_no_dup = [] %}
{% for question in form_data.questions %}
    {% if question.get('no_duplication', False) %}
        {% set _ = questions_with_no_dup.append(question) %}
    {% endif %}
{% endfor %}

{% set max_possible_submissions = 10000 %}
{% set min_responses_count = 99999 %}

{% if questions_with_no_dup %}
    {% for question in questions_with_no_dup %}
        {% if question.required and "default_value" not in question %}
            {% set q_id = question.id|string %}
            {% set responses = form_data.responses.get(q_id, []) %}
            {% if responses|length > 0 %}
                {% set min_responses_count = [min_responses_count, responses|length]|min %}
            {% endif %}
        {% endif %}
    {% endfor %}
    
    {% if min_responses_count < 99999 %}
        {% set max_possible_submissions = min_responses_count %}
    {% endif %}
{% endif %}

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-paper-plane me-2"></i>Submit Form: {{ form_data.form_title }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p>
                            <strong>Form ID:</strong> {{ form_id }}<br>
                            <strong>Questions:</strong> {{ form_data.questions|length }}<br>
                            {% set total_responses = form_data.responses.values() | map('length') | sum %}
                            <strong>Total Responses Prepared:</strong> {{ total_responses }}<br>
                            {% if total_responses == 0 %}
                                <div class="alert alert-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> No responses have been generated yet. Please generate responses first.
                                </div>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i>View Form
                            </a>
                            <a href="{{ url_for('review_responses', form_id=form_id) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Review Responses
                            </a>
                            {% if total_responses == 0 %}
                                <a href="{{ url_for('generate_responses', form_id=form_id) }}" class="btn btn-success">
                                    <i class="fas fa-magic me-1"></i>Generate Responses
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Per-Question No Duplication Mode Info -->        
        {% if questions_with_no_dup %}
        <div class="card mb-4 border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-ban me-2"></i>Questions with No Duplication Mode
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> Some questions have No Duplication mode enabled.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-2"></i>What this means:</h6>
                        <ul>
                            <li>Each response for these questions will only be used once</li>
                            <li>Response weights are ignored for these questions</li>
                            <li>Maximum submissions may be limited by available unique responses</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-list me-2"></i>Questions with No Duplication:</h6>
                        {% for question in questions_with_no_dup %}
                            {% if question.required and "default_value" not in question %}
                                {% set q_id = question.id|string %}
                                {% set responses = form_data.responses.get(q_id, []) %}
                                {% if responses|length > 0 %}
                                    <div class="small mb-1">
                                        <span class="badge bg-warning me-1">
                                            <i class="fas fa-ban"></i>
                                        </span>
                                        <strong>{{ question.title[:30] }}{% if question.title|length > 30 %}...{% endif %}:</strong> 
                                        <span class="text-primary">{{ responses|length }} unique responses</span>
                                    </div>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                        
                        {% if max_possible_submissions < 10000 %}
                            <div class="mt-3 p-2 bg-light rounded">
                                <strong class="text-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    Maximum Possible Submissions: {{ max_possible_submissions }}
                                </strong>
                                <div class="small text-muted">
                                    Limited by questions with No Duplication mode
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('review_responses', form_id=form_id) }}" class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-cog me-1"></i>Manage No Duplication Settings
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Submission Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('submit_form', form_id=form_id) }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                {{ form.url.label(class="form-label") }}
                                {{ form.url(class="form-control") }}
                                {% if form.url.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.url.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    The URL of the Google Form to submit.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.count.label(class="form-label") }}
                                {% if max_possible_submissions < 10000 %}
                                    {{ form.count(class="form-control", max=max_possible_submissions, id="count-input") }}
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Limited to {{ max_possible_submissions }} due to No Duplication mode.
                                    </div>
                                {% else %}
                                    {{ form.count(class="form-control", id="count-input") }}
                                    <div class="form-text">
                                        Number of submissions to make (1-10,000).
                                    </div>
                                {% endif %}
                                {% if form.count.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.count.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="count-warning" class="alert alert-warning mt-2 d-none">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    <small>Count cannot exceed {{ max_possible_submissions }} due to No Duplication limitations.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.delay_min.label(class="form-label") }}
                                {{ form.delay_min(class="form-control") }}
                                {% if form.delay_min.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.delay_min.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Minimum delay between submissions (50-60,000ms).
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.delay_max.label(class="form-label") }}
                                {{ form.delay_max(class="form-control") }}
                                {% if form.delay_max.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.delay_max.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Maximum delay between submissions (50-60,000ms).
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.max_workers.label(class="form-label") }}
                                {{ form.max_workers(class="form-control") }}
                                {% if form.max_workers.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.max_workers.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Number of parallel threads for faster submission.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Performance Tips:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Higher thread count = faster submission (but may trigger rate limits)</li>
                                    <li>Lower delays = faster completion (but higher risk of being blocked)</li>
                                    <li>Recommended: 3-10 threads with 1000-3000ms delays for optimal balance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Submitting a form multiple times may be against the form owner's intentions. Use responsibly and ethically.
                    </div>
                    
                    <div class="d-grid">
                        {% if total_responses > 0 %}
                            {{ form.submit(class="btn btn-primary btn-lg", id="submit-btn", **{"data-max-submissions": max_possible_submissions}) }}
                        {% else %}
                            <button type="button" class="btn btn-secondary btn-lg" disabled>
                                <i class="fas fa-exclamation-triangle me-2"></i>Generate Responses First
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    const submitBtn = $('#submit-btn');
    const maxPossibleSubmissions = parseInt(submitBtn.data('max-submissions')) || 10000;
    const countInput = $('#count-input');
    const countWarning = $('#count-warning');
    
    // Validate count input
    function validateCount() {
        const value = parseInt(countInput.val());
        
        if (value > maxPossibleSubmissions) {
            countInput.addClass('is-invalid');
            countWarning.removeClass('d-none');
            submitBtn.prop('disabled', true);
            // Auto-correct to max value
            countInput.val(maxPossibleSubmissions);
            setTimeout(function() {
                countInput.removeClass('is-invalid');
                countWarning.addClass('d-none');
                submitBtn.prop('disabled', false);
            }, 2000);
        } else {
            countInput.removeClass('is-invalid');
            countWarning.addClass('d-none');
            submitBtn.prop('disabled', false);
        }
    }
    
    // Bind validation to input events
    countInput.on('input change blur', validateCount);
    
    // Initial validation
    validateCount();
});
</script>
{% endblock %}
