"""
Enhanced Google Form AutoFill Example

This example demonstrates how to use the enhanced Google Form AutoFill tool
to load a form, generate responses using Gemini, and submit the form.
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from form_enhanced import EnhancedFormParser
from gemini_client import GeminiClient
from response_generator import ResponseGenerator
from response_storage import FormResponseManager
from submission_manager import SubmissionManager


def load_config():
    """Load configuration from .env file"""
    # Look for .env file in parent directory
    env_path = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        print(f"Loaded configuration from {env_path}")
    else:
        print("No .env file found, using environment variables")

    # Get configuration values
    config = {
        "api_key": os.environ.get("GEMINI_API_KEY"),
        "form_url": os.environ.get("DEFAULT_FORM_URL", "https://docs.google.com/forms/d/e/your-form-id/viewform"),
        "sample_count": int(os.environ.get("DEFAULT_SAMPLE_COUNT", "3"))
    }

    return config


def main():
    """Main function"""
    # Load configuration from .env file
    config = load_config()

    # Get API key from configuration
    api_key = config["api_key"]
    if not api_key:
        print("Please set the GEMINI_API_KEY in .env file or environment variable")
        return

    # Form URL from configuration
    form_url = config["form_url"]

    # Step 1: Load the form
    print("Step 1: Loading form...")
    parser = EnhancedFormParser(form_url)
    if not parser.load_form():
        print("Failed to load form")
        return

    print(f"Form loaded: {parser.get_form_title()}")
    print(f"Found {len(parser.get_questions())} questions ({len(parser.get_questions(True))} required)")

    # Display form structure
    print("\nForm Structure:")
    print(parser.display_form_structure())

    # Step 2: Initialize Gemini client
    print("\nStep 2: Initializing Gemini client...")
    gemini = GeminiClient(api_key)

    # Step 3: Generate responses
    print("\nStep 3: Generating responses...")
    generator = ResponseGenerator(gemini)

    # Example responses for demonstration
    examples = {
        # Replace with actual question IDs from your form
        "1234567890": [
            "I strongly agree because it aligns with my values.",
            "I somewhat agree, though I have some reservations."
        ]
    }

    # Generate responses using Gemini-assisted method
    result = generator.generate_responses_for_form(
        parser,
        method="gemini_assisted",
        sample_count=config["sample_count"],
        examples=examples
    )

    print(result)

    # Step 4: Review responses
    print("\nStep 4: Reviewing responses...")
    form_id = parser.get_form_id()
    manager = FormResponseManager()
    form_data = manager.load_form_data(form_id)

    if not form_data:
        print(f"No data found for form ID: {form_id}")
        return

    # Display responses for each question
    for question in form_data.get('questions', []):
        question_id = question['id']
        question_title = question['title']

        responses = form_data.get('responses', {}).get(question_id, [])

        print(f"\n{'-'*50}")
        print(f"Question: {question_title}")
        print(f"Type: {question.get('type_name', 'Unknown')}")

        if responses:
            print(f"Responses ({len(responses)}):")
            for i, response in enumerate(responses, 1):
                print(f"  {i}. {response['text']} (weight: {response['weight']})")
        else:
            print("No responses prepared")

    # Step 5: Submit the form (commented out for safety)
    print("\nStep 5: Form submission (commented out for safety)")
    print("To submit the form, uncomment the following code:")
    print("""
    # Initialize submission manager
    submission_manager = SubmissionManager()

    # Submit the form 3 times
    result = submission_manager.batch_submit(
        form_id,
        form_url,
        count=3,
        delay_range=(1.5, 3.0),
        progress_callback=lambda p, s, f: print(f"Progress: {p*100:.1f}% | Successful: {s} | Failed: {f}")
    )

    # Display results
    print("\\nSubmission complete!")
    print(f"Total submissions: {result['total']}")
    print(f"Successful: {result['successful']}")
    print(f"Failed: {result['failed']}")
    print(f"Success rate: {result['success_rate']*100:.1f}%")
    """)


if __name__ == "__main__":
    main()
