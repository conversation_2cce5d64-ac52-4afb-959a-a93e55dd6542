"""
Prompt Utilities for Google Form AutoFill

This module provides utilities for creating prompts and parsing responses
for different AI providers.
"""

import json
import re
import random
from typing import List, Dict, Any, Optional, Union


def create_open_ended_prompt(question: str, count: int = 1, examples: Optional[List[str]] = None) -> str:
    """
    Create a prompt for generating responses to open-ended questions
    
    Args:
        question: The question to generate responses for
        count: Number of responses to generate
        examples: Optional list of example responses
        
    Returns:
        A prompt for the AI model
    """
    prompt = f"Generate {count} different realistic responses to the following question:\n\n"
    prompt += f"Question: {question}\n\n"
    
    if examples:
        prompt += "Here are some example responses for reference:\n"
        for i, example in enumerate(examples, 1):
            prompt += f"Example {i}: {example}\n"
        prompt += "\n"
        
    prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
    prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
    prompt += "Format example:\n"
    prompt += "```json\n"
    prompt += '["Response 1", "Response 2", "Response 3"]\n'
    prompt += "```\n\n"
    prompt += "Make sure each response is complete, meaningful, and different from the others."
    
    return prompt


def create_multiple_choice_prompt(question: str, options: List[str], count: int = 1) -> str:
    """
    Create a prompt for generating weightage for multiple choice options
    
    Args:
        question: The question to generate responses for
        options: List of available options
        count: Number of responses to distribute
        
    Returns:
        A prompt for the AI model
    """
    prompt = f"For the following multiple choice question, suggest a distribution of {count} responses:\n\n"
    prompt += f"Question: {question}\n"
    prompt += "Options:\n"
    
    for i, option in enumerate(options, 1):
        prompt += f"{i}. {option}\n"
        
    prompt += f"\nPlease distribute {count} responses among these options in a realistic way, "
    prompt += "showing how many responses should go to each option. Return the result as a JSON object "
    prompt += "with option text as keys and count as values."
    
    return prompt


def parse_open_ended_responses(response_text: str, count: int) -> List[str]:
    """
    Parse multiple responses from AI model's JSON output
    
    Args:
        response_text: The text response from the AI model
        count: Expected number of responses
        
    Returns:
        List of parsed responses
    """
    responses = []
    
    try:
        # First, try to extract JSON array from the response
        # Look for JSON-like structure between triple backticks or anywhere in the text
        json_pattern = r'```(?:json)?\s*(\[[\s\S]*?\])```|(\[[\s\S]*?\])'
        match = re.search(json_pattern, response_text)
        
        if match:
            json_str = match.group(1) or match.group(2)
            response_list = json.loads(json_str)
            
            if isinstance(response_list, list):
                responses = [str(item).strip() for item in response_list if str(item).strip()]
                
        # If JSON parsing failed, try alternative JSON patterns
        if not responses:
            # Try to find JSON array without backticks
            array_pattern = r'\[[\s\S]*?\]'
            matches = re.findall(array_pattern, response_text)
            
            for match in matches:
                try:
                    response_list = json.loads(match)
                    if isinstance(response_list, list):
                        responses = [str(item).strip() for item in response_list if str(item).strip()]
                        break
                except json.JSONDecodeError:
                    continue
                    
    except Exception as e:
        print(f"Error parsing JSON responses: {e}")
        
    # Fallback: try to parse old format if JSON parsing failed
    if not responses:
        pattern = r"(?:Response|Answer)\s*(\d+):\s*(.*?)(?=(?:Response|Answer)\s*\d+:|$)"
        matches = re.findall(pattern, response_text, re.DOTALL)
        
        for _, response in matches:
            responses.append(response.strip())
            
        # If pattern matching failed, try simple splitting
        if not responses:
            responses = [r.strip() for r in response_text.split("\n\n") if r.strip()]
            
    # Clean up responses and ensure we have the requested number
    cleaned_responses = []
    for response in responses:
        # Remove any unwanted characters or formatting
        cleaned = response.strip().strip('"').strip("'")
        if cleaned and len(cleaned) > 0:
            cleaned_responses.append(cleaned)
            
    # Ensure we have at least some responses
    if not cleaned_responses and response_text.strip():
        # Use the entire response as a single answer if parsing completely failed
        cleaned_responses = [response_text.strip()]
        
    # Return up to the requested count
    return cleaned_responses[:count] if cleaned_responses else []


def parse_multiple_choice_responses(response_text: str) -> Dict[str, int]:
    """
    Parse multiple choice response distribution from AI model's output
    
    Args:
        response_text: The text response from the AI model
        
    Returns:
        Dictionary mapping options to counts
    """
    # Try to extract JSON from the response
    try:
        # Find JSON-like structure in the text
        json_pattern = r'\{[^}]*\}'
        json_match = re.search(json_pattern, response_text)
        if json_match:
            return json.loads(json_match.group(0))
            
        # If no JSON found, try to parse structured text
        result = {}
        lines = response_text.strip().split('\n')
        for line in lines:
            if ':' in line:
                option, count_str = line.split(':', 1)
                count = int(re.search(r'\d+', count_str).group(0))
                result[option.strip()] = count
        return result
    except Exception:
        # If parsing fails, return empty dict
        return {}


def assign_weightage(responses: List[str], total_submissions: int = 100) -> Dict[str, int]:
    """
    Assign random weightage to responses that sum to total_submissions
    
    Args:
        responses: List of responses to assign weightage to
        total_submissions: Total number of submissions to distribute
        
    Returns:
        Dictionary mapping responses to their weightage
    """
    if not responses:
        return {}
        
    # Start with equal distribution
    base_weight = total_submissions // len(responses)
    weights = [base_weight] * len(responses)
    
    # Distribute remaining submissions randomly
    remaining = total_submissions - sum(weights)
    for i in range(remaining):
        weights[random.randint(0, len(responses) - 1)] += 1
        
    return {response: weight for response, weight in zip(responses, weights)}


def create_batch_prompt(questions: List[Dict[str, Any]], sample_count: int = 5) -> str:
    """
    Create a prompt for generating responses for multiple questions in a single request
    
    Args:
        questions: List of question objects
        sample_count: Number of responses to generate per question
        
    Returns:
        A prompt for the AI model
    """
    prompt = f"Generate responses for the following form questions. For each question, provide {sample_count} unique, thoughtful responses.\n\n"
    
    for i, question in enumerate(questions, 1):
        question_title = question.get("title", "")
        question_type = question.get("type", "")
        options = question.get("options", [])
        
        prompt += f"Question {i}: {question_title}\n"
        prompt += f"Type: {question_type}\n"
        
        if options:
            prompt += "Options:\n"
            for j, option in enumerate(options, 1):
                prompt += f"  {j}. {option}\n"
                
        prompt += "\n"
        
    prompt += f"\nFor each question, provide {sample_count} realistic responses that a real person might give.\n"
    prompt += "Format your response as a JSON object with question indices as keys and an array of responses as values:\n\n"
    prompt += "```json\n"
    prompt += "{\n"
    prompt += '  "1": ["Response 1", "Response 2", ...],\n'
    prompt += '  "2": ["Response 1", "Response 2", ...],\n'
    prompt += "  ...\n"
    prompt += "}\n"
    prompt += "```\n\n"
    prompt += "For multiple-choice questions, responses should only be one of the provided options."
    
    return prompt


def parse_batch_responses(response_text: str, questions: List[Dict[str, Any]]) -> Dict[str, List[str]]:
    """
    Parse responses for multiple questions from AI model's output
    
    Args:
        response_text: The text response from the AI model
        questions: List of question objects
        
    Returns:
        Dictionary mapping question IDs to lists of responses
    """
    # Extract JSON from the response
    try:
        # Find JSON-like structure between triple backticks or anywhere in the text
        json_pattern = r'```(?:json)?\s*({[\s\S]*?})```|({[\s\S]*})'
        match = re.search(json_pattern, response_text)
        
        if match:
            json_str = match.group(1) or match.group(2)
            response_dict = json.loads(json_str)
            
            # Map question indices to question IDs
            result = {}
            for i, question in enumerate(questions, 1):
                question_id = question["id"]
                if str(i) in response_dict:
                    result[question_id] = response_dict[str(i)]
                    
            return result
    except Exception as e:
        print(f"Error parsing batch responses: {e}")
        
    # Fallback: try to parse structured text
    result = {}
    current_question = None
    current_responses = []
    
    for line in response_text.split('\n'):
        line = line.strip()
        
        # Check for question marker
        question_match = re.match(r'Question\s+(\d+)', line)
        if question_match:
            # Save previous question's responses if any
            if current_question is not None and current_responses:
                question_id = questions[current_question - 1]["id"]
                result[question_id] = current_responses
                
            # Start new question
            current_question = int(question_match.group(1))
            current_responses = []
            continue
            
        # Check for response marker
        response_match = re.match(r'(?:Response|Answer)\s*\d+:\s*(.*)', line)
        if response_match and current_question is not None:
            response_text = response_match.group(1).strip()
            if response_text:
                current_responses.append(response_text)
                
    # Save last question's responses
    if current_question is not None and current_responses:
        question_id = questions[current_question - 1]["id"]
        result[question_id] = current_responses
        
    return result
