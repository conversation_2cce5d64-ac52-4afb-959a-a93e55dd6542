"""
Billing Manager Module for Google Form AutoFill

This module provides functionality for tracking and managing billing
based on the number of form submissions.
"""

import json
import os
import time
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

from customer_request import CustomerRequestManager


class BillingRecord:
    """Class representing a billing record"""
    
    def __init__(self, customer_id: str, request_id: Optional[str] = None,
                 submission_count: int = 0, amount: float = 0.0,
                 description: Optional[str] = None):
        """
        Initialize a billing record
        
        Args:
            customer_id: The ID of the customer
            request_id: Optional ID of the associated request
            submission_count: Number of submissions billed
            amount: Billing amount
            description: Optional description of the billing
        """
        self.id = str(uuid.uuid4())
        self.customer_id = customer_id
        self.request_id = request_id
        self.submission_count = submission_count
        self.amount = amount
        self.description = description
        self.created_at = time.time()
        self.status = "pending"  # pending, paid, cancelled
        self.paid_at = None
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the billing record to a dictionary for storage"""
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "request_id": self.request_id,
            "submission_count": self.submission_count,
            "amount": self.amount,
            "description": self.description,
            "created_at": self.created_at,
            "status": self.status,
            "paid_at": self.paid_at
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BillingRecord':
        """Create a BillingRecord instance from a dictionary"""
        record = cls(
            customer_id=data.get("customer_id", ""),
            request_id=data.get("request_id"),
            submission_count=data.get("submission_count", 0),
            amount=data.get("amount", 0.0),
            description=data.get("description")
        )
        
        # Set additional attributes
        record.id = data.get("id", record.id)
        record.created_at = data.get("created_at", record.created_at)
        record.status = data.get("status", record.status)
        record.paid_at = data.get("paid_at")
        
        return record


class BillingManager:
    """Class for managing billing records"""
    
    def __init__(self, storage_dir: str = "responses/billing", 
                 base_rate: float = 0.01):
        """
        Initialize the billing manager
        
        Args:
            storage_dir: Directory to store billing records
            base_rate: Base rate per submission (default: $0.01 per submission)
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
        self.base_rate = base_rate
        self.request_manager = CustomerRequestManager()
        
    def _get_billing_filename(self) -> str:
        """Get the filename for all billing records"""
        return os.path.join(self.storage_dir, "billing_records.json")
        
    def _get_customer_billing_filename(self, customer_id: str) -> str:
        """Get the filename for a customer's billing records"""
        return os.path.join(self.storage_dir, f"{customer_id}_billing.json")
        
    def _load_billing_records(self) -> List[Dict[str, Any]]:
        """Load all billing records from storage"""
        filename = self._get_billing_filename()
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _load_customer_billing_records(self, customer_id: str) -> List[Dict[str, Any]]:
        """Load billing records for a specific customer"""
        filename = self._get_customer_billing_filename(customer_id)
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _save_billing_records(self, records: List[Dict[str, Any]]) -> None:
        """Save all billing records to storage"""
        filename = self._get_billing_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
            
    def _save_customer_billing_records(self, customer_id: str, 
                                      records: List[Dict[str, Any]]) -> None:
        """Save billing records for a specific customer"""
        filename = self._get_customer_billing_filename(customer_id)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
            
    def calculate_amount(self, submission_count: int, 
                        custom_rate: Optional[float] = None) -> float:
        """
        Calculate billing amount based on submission count
        
        Args:
            submission_count: Number of submissions
            custom_rate: Optional custom rate per submission
            
        Returns:
            Calculated billing amount
        """
        rate = custom_rate if custom_rate is not None else self.base_rate
        
        # Apply volume discounts
        if submission_count >= 10000:
            # 20% discount for 10,000+ submissions
            rate *= 0.8
        elif submission_count >= 5000:
            # 15% discount for 5,000+ submissions
            rate *= 0.85
        elif submission_count >= 1000:
            # 10% discount for 1,000+ submissions
            rate *= 0.9
        elif submission_count >= 500:
            # 5% discount for 500+ submissions
            rate *= 0.95
            
        return round(submission_count * rate, 2)
        
    def create_billing_record(self, customer_id: str, request_id: Optional[str] = None,
                             submission_count: int = 0, custom_rate: Optional[float] = None,
                             description: Optional[str] = None) -> str:
        """
        Create a new billing record
        
        Args:
            customer_id: The ID of the customer
            request_id: Optional ID of the associated request
            submission_count: Number of submissions to bill
            custom_rate: Optional custom rate per submission
            description: Optional description of the billing
            
        Returns:
            The ID of the created billing record
        """
        # If request_id is provided, get submission count from the request
        if request_id and submission_count == 0:
            request = self.request_manager.get_request(request_id)
            if request and request.result:
                submission_count = request.result.get("successful", 0)
                
                # If no description is provided, use the form title
                if not description and request.form_title:
                    description = f"Submissions for {request.form_title}"
                    
        # Calculate amount
        amount = self.calculate_amount(submission_count, custom_rate)
        
        # Create billing record
        record = BillingRecord(
            customer_id=customer_id,
            request_id=request_id,
            submission_count=submission_count,
            amount=amount,
            description=description
        )
        
        # Save to global billing records
        all_records = self._load_billing_records()
        all_records.append(record.to_dict())
        self._save_billing_records(all_records)
        
        # Save to customer-specific billing records
        customer_records = self._load_customer_billing_records(customer_id)
        customer_records.append(record.to_dict())
        self._save_customer_billing_records(customer_id, customer_records)
        
        return record.id
        
    def get_billing_record(self, record_id: str) -> Optional[BillingRecord]:
        """
        Get a billing record by ID
        
        Args:
            record_id: The ID of the billing record
            
        Returns:
            The BillingRecord object, or None if not found
        """
        all_records = self._load_billing_records()
        for record_data in all_records:
            if record_data.get("id") == record_id:
                return BillingRecord.from_dict(record_data)
        return None
        
    def update_billing_record(self, record: BillingRecord) -> bool:
        """
        Update an existing billing record
        
        Args:
            record: The BillingRecord object to update
            
        Returns:
            True if successful, False otherwise
        """
        # Update in global billing records
        all_records = self._load_billing_records()
        updated_global = False
        
        for i, record_data in enumerate(all_records):
            if record_data.get("id") == record.id:
                all_records[i] = record.to_dict()
                updated_global = True
                break
                
        if updated_global:
            self._save_billing_records(all_records)
            
        # Update in customer-specific billing records
        customer_records = self._load_customer_billing_records(record.customer_id)
        updated_customer = False
        
        for i, record_data in enumerate(customer_records):
            if record_data.get("id") == record.id:
                customer_records[i] = record.to_dict()
                updated_customer = True
                break
                
        if updated_customer:
            self._save_customer_billing_records(record.customer_id, customer_records)
            
        return updated_global and updated_customer
        
    def mark_as_paid(self, record_id: str) -> bool:
        """
        Mark a billing record as paid
        
        Args:
            record_id: The ID of the billing record
            
        Returns:
            True if successful, False otherwise
        """
        record = self.get_billing_record(record_id)
        if not record:
            return False
            
        record.status = "paid"
        record.paid_at = time.time()
        
        return self.update_billing_record(record)
        
    def mark_as_cancelled(self, record_id: str) -> bool:
        """
        Mark a billing record as cancelled
        
        Args:
            record_id: The ID of the billing record
            
        Returns:
            True if successful, False otherwise
        """
        record = self.get_billing_record(record_id)
        if not record:
            return False
            
        record.status = "cancelled"
        
        return self.update_billing_record(record)
        
    def get_customer_billing_records(self, customer_id: str, 
                                    status: Optional[str] = None) -> List[BillingRecord]:
        """
        Get billing records for a specific customer
        
        Args:
            customer_id: The ID of the customer
            status: Optional filter by status (pending, paid, cancelled)
            
        Returns:
            List of BillingRecord objects
        """
        records = self._load_customer_billing_records(customer_id)
        
        # Filter by status if provided
        if status:
            records = [r for r in records if r.get("status") == status]
            
        # Convert to BillingRecord objects
        return [BillingRecord.from_dict(r) for r in records]
        
    def get_all_billing_records(self, status: Optional[str] = None,
                               start_date: Optional[Union[datetime, float]] = None,
                               end_date: Optional[Union[datetime, float]] = None) -> List[BillingRecord]:
        """
        Get all billing records with optional filters
        
        Args:
            status: Optional filter by status (pending, paid, cancelled)
            start_date: Optional filter by start date (inclusive)
            end_date: Optional filter by end date (inclusive)
            
        Returns:
            List of BillingRecord objects
        """
        records = self._load_billing_records()
        
        # Convert datetime to timestamp if needed
        if isinstance(start_date, datetime):
            start_date = start_date.timestamp()
        if isinstance(end_date, datetime):
            end_date = end_date.timestamp()
            
        # Apply filters
        if status:
            records = [r for r in records if r.get("status") == status]
            
        if start_date:
            records = [r for r in records if r.get("created_at", 0) >= start_date]
            
        if end_date:
            records = [r for r in records if r.get("created_at", 0) <= end_date]
            
        # Convert to BillingRecord objects
        return [BillingRecord.from_dict(r) for r in records]
        
    def get_billing_summary(self, customer_id: Optional[str] = None,
                           period: str = "all") -> Dict[str, Any]:
        """
        Get billing summary statistics
        
        Args:
            customer_id: Optional filter by customer ID
            period: Time period for summary (all, month, week, day)
            
        Returns:
            Dictionary with billing summary statistics
        """
        # Determine date range based on period
        end_date = datetime.now()
        
        if period == "month":
            start_date = end_date - timedelta(days=30)
        elif period == "week":
            start_date = end_date - timedelta(days=7)
        elif period == "day":
            start_date = end_date - timedelta(days=1)
        else:
            start_date = None
            
        # Convert to timestamps
        start_timestamp = start_date.timestamp() if start_date else None
        end_timestamp = end_date.timestamp()
        
        # Get records based on filters
        if customer_id:
            records = self.get_customer_billing_records(customer_id)
        else:
            records = self.get_all_billing_records()
            
        # Filter by date range if applicable
        if start_timestamp:
            records = [r for r in records if r.created_at >= start_timestamp]
        if end_timestamp:
            records = [r for r in records if r.created_at <= end_timestamp]
            
        # Calculate summary statistics
        total_records = len(records)
        total_submissions = sum(r.submission_count for r in records)
        total_amount = sum(r.amount for r in records)
        
        pending_amount = sum(r.amount for r in records if r.status == "pending")
        paid_amount = sum(r.amount for r in records if r.status == "paid")
        cancelled_amount = sum(r.amount for r in records if r.status == "cancelled")
        
        pending_count = sum(1 for r in records if r.status == "pending")
        paid_count = sum(1 for r in records if r.status == "paid")
        cancelled_count = sum(1 for r in records if r.status == "cancelled")
        
        return {
            "total_records": total_records,
            "total_submissions": total_submissions,
            "total_amount": total_amount,
            "pending_amount": pending_amount,
            "paid_amount": paid_amount,
            "cancelled_amount": cancelled_amount,
            "pending_count": pending_count,
            "paid_count": paid_count,
            "cancelled_count": cancelled_count,
            "period": period,
            "customer_id": customer_id
        }
        
    def generate_invoice(self, customer_id: str, 
                        record_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate an invoice for a customer
        
        Args:
            customer_id: The ID of the customer
            record_ids: Optional list of specific billing record IDs to include
            
        Returns:
            Dictionary with invoice data
        """
        # Get customer billing records
        if record_ids:
            # Get specific records
            records = []
            for record_id in record_ids:
                record = self.get_billing_record(record_id)
                if record and record.customer_id == customer_id:
                    records.append(record)
        else:
            # Get all pending records for the customer
            records = self.get_customer_billing_records(customer_id, status="pending")
            
        if not records:
            return {
                "customer_id": customer_id,
                "invoice_id": None,
                "created_at": time.time(),
                "total_amount": 0,
                "total_submissions": 0,
                "items": [],
                "error": "No pending billing records found"
            }
            
        # Generate invoice data
        invoice_id = f"INV-{customer_id[:8]}-{int(time.time())}"
        total_amount = sum(r.amount for r in records)
        total_submissions = sum(r.submission_count for r in records)
        
        items = []
        for record in records:
            items.append({
                "record_id": record.id,
                "description": record.description or f"Form submissions ({record.submission_count})",
                "submission_count": record.submission_count,
                "amount": record.amount,
                "created_at": record.created_at,
                "created_date": datetime.fromtimestamp(record.created_at).strftime("%Y-%m-%d")
            })
            
        return {
            "customer_id": customer_id,
            "invoice_id": invoice_id,
            "created_at": time.time(),
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "due_date": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
            "total_amount": total_amount,
            "total_submissions": total_submissions,
            "items": items
        }
