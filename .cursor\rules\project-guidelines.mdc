---
description:
globs:
alwaysApply: false
---
# Google Form AutoFill - Project Guidelines and Development Rules

## Project Overview
This is a modern, dark-themed Flask web application for automating Google Forms with AI-powered response generation using Google's Gemini AI. The project features sophisticated batch operations, modal management, and a comprehensive dark UI.

## Architecture Patterns

### Core Structure
- **Backend**: Flask application with modular design
- **Frontend**: Modern dark theme with Bootstrap 5 and custom CSS
- **AI Integration**: Google Gemini API for intelligent response generation
- **Architecture**: Object-Oriented Programming (OOP) and Model-View-Controller (MVC) patterns

### Key Files Navigation
- **Main Application**: [run_web.py](mdc:run_web.py) - Flask web application launcher
- **Enhanced Features**: [enhanced_main.py](mdc:enhanced_main.py) - Advanced application entry point
- **Web Interface**: [web/routes.py](mdc:web/routes.py) - Web routes and controllers
- **API Endpoints**: [web/api.py](mdc:web/api.py) - REST API with batch operations
- **AI Integration**: [gemini_client.py](mdc:gemini_client.py) - Google Gemini AI client
- **Response Management**: [response_storage.py](mdc:response_storage.py) - Data management with batch operations
- **Submission Logic**: [submission_manager.py](mdc:submission_manager.py) - Form submission handling

### Templates and UI
- **Base Template**: [web/templates/base.html](mdc:web/templates/base.html) - Modern dark theme foundation
- **Response Review**: [web/templates/review_responses.html](mdc:web/templates/review_responses.html) - Enhanced with batch operations
- **Main Stylesheet**: [web/static/css/style.css](mdc:web/static/css/style.css) - Dark theme with modal fixes
- **JavaScript**: [web/static/js/main.js](mdc:web/static/js/main.js) - Enhanced features and modal management

## Critical CSS and Modal Management

### ⚠️ CRITICAL: Modal Flashing Fix
**NEVER re-enable transform hover effects** - These cause modal flashing when cursor moves:
```css
/* ❌ NEVER USE - Causes modal flashing */
.card:hover { transform: translateY(-2px); }
.btn:hover { transform: translateY(-1px); }
.badge:hover { transform: translateY(-1px); }

/* ✅ ALWAYS USE - Fixed version */
.card:hover { transform: none !important; }
.btn:hover { transform: none !important; }
.badge:hover { transform: none !important; }
```

### Text Visibility Rules
- Use `text-white` for important text in dark themes
- Avoid `text-muted` for critical UI elements
- Ensure high contrast for accessibility
- Test text visibility in different lighting conditions

### CSS Architecture Guidelines
- Use CSS custom properties (variables) defined in `:root`
- Follow BEM methodology for class naming
- Implement mobile-first responsive design
- Maintain consistent spacing using CSS variables
- Use semantic color variables instead of hardcoded colors

## Development Workflow

### File Organization
- Follow the established folder structure in [folder_structure.md](mdc:folder_structure.md)
- Place new features in appropriate directories
- Update documentation when adding new components
- Use meaningful file and function names

### Code Standards
- **Python**: Follow PEP 8 standards
- **JavaScript**: Use modern ES6+ features
- **CSS**: Use CSS custom properties and modern features
- **HTML**: Semantic markup with accessibility considerations

### Testing and Debugging
- Create test files in appropriate directories
- Use PowerShell for testing on Windows
- Remove test files after debugging
- Document any critical fixes in [folder_structure.md](mdc:folder_structure.md)

## UI/UX Best Practices

### Dark Theme Guidelines
- Primary background: `var(--bg-primary)` (#1a1a1a)
- Card background: `var(--bg-card)` (#2a2a2a)
- Text primary: `var(--text-primary)` (#ffffff)
- Accent primary: `var(--accent-primary)` (#6c5ce7)

### Modal Management
- Use `AppUtils.safelyShowModal()` and `AppUtils.safelyHideModal()`
- Implement proper modal instance disposal
- Add backdrop cleanup with delays
- Use namespaced event handlers to prevent conflicts
- Always test modal behavior with cursor movement

### Button and Interactive Elements
- Use gradient backgrounds for primary actions
- Implement proper loading states
- Provide visual feedback for user actions
- Ensure touch-friendly sizing on mobile devices
- Maintain consistent spacing and typography

## API and Backend Patterns

### Batch Operations
- Implement efficient batch processing for multiple items
- Use proper error handling with detailed messages
- Provide progress feedback for long-running operations
- Support cancellation for user-initiated operations

### Response Generation
- Use JSON format for Gemini AI responses
- Implement robust parsing with fallback mechanisms
- Optimize API usage to reduce costs
- Cache responses when appropriate

### Error Handling
- Provide user-friendly error messages
- Log detailed errors for debugging
- Implement graceful degradation
- Return appropriate HTTP status codes

## Recent Critical Fixes (Reference Only)

### Modal Flashing Fix (2024-12-19)
- **Issue**: Transform hover effects causing layout reflows
- **Solution**: Disabled all transform hover effects with `transform: none !important`
- **Files**: [web/static/css/style.css](mdc:web/static/css/style.css)

### Text Visibility Fix (2024-12-19)
- **Issue**: Selected count text was black/gray and hard to see
- **Solution**: Changed from `text-muted` to `text-white`
- **Files**: [web/templates/review_responses.html](mdc:web/templates/review_responses.html)

### Cancel Submission Enhancement (2024-12-19)
- **Issue**: Cancel button wasn't stopping submissions
- **Solution**: Implemented proper thread communication and cancellation flags
- **Files**: Multiple files including API and submission manager

## Performance Considerations
- Minimize DOM manipulations
- Use CSS animations efficiently
- Implement proper event handler cleanup
- Optimize image loading and caching
- Monitor API usage and costs

## Security Guidelines
- Validate all user inputs
- Sanitize data before database operations
- Use environment variables for sensitive configuration
- Implement proper authentication if needed
- Follow OWASP security guidelines

## Documentation Requirements
- Update [folder_structure.md](mdc:folder_structure.md) for significant changes
- Document new API endpoints
- Maintain code comments for complex logic
- Keep README files current
- Record performance optimizations and their impact
