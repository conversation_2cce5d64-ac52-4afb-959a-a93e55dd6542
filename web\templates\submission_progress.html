{% extends "base.html" %}

{% block title %}Submission Progress - Google Form AutoFill{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4 border border-primary bg-dark">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="ti ti-send me-2"></i>Form Submission Progress
                </h4>
            </div>
            <div class="card-body bg-dark">
                <div class="row">
                    <div class="col-md-8">
                        <p class="text-light mb-0">
                            <strong><i class="ti ti-id me-1"></i>Form ID:</strong> <span id="form-id" class="text-info">{{ form_id }}</span><br>
                            <strong><i class="ti ti-world-www me-1"></i>Form URL:</strong> <span id="form-url" class="text-info">{{ form_url }}</span><br>
                            <strong><i class="ti ti-list-numbers me-1"></i>Submissions:</strong> <span id="submission-count" class="text-info">{{ count }}</span><br>
                            <strong><i class="ti ti-clock me-1"></i>Delay:</strong> <span class="text-info">{{ delay_min }}-{{ delay_max }} milliseconds</span><br>
                            <strong><i class="ti ti-cpu me-1"></i>Threads:</strong> <span class="text-info">{{ max_workers }} parallel workers</span><br>
                            <strong><i class="ti ti-player-play me-1"></i>Started:</strong> <span id="start-time" class="text-info">--:--:--</span><br>
                            <strong><i class="ti ti-hourglass me-1"></i>Elapsed:</strong> <span id="elapsed-time" class="text-info">00:00:00</span>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button id="cancel-btn" class="btn btn-danger me-2">
                            <i class="ti ti-square-x me-1"></i>Cancel Submission
                        </button>
                        <button id="history-btn" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#historyModal">
                            <i class="ti ti-history me-1"></i>History
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4 border border-info bg-dark">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="ti ti-chart-line me-2"></i>Progress</h5>
            </div>
            <div class="card-body bg-dark">
                <div class="progress mb-4" style="height: 35px; border: 2px solid #6c757d; background-color: #495057;">
                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
                         role="progressbar" style="width: 0%;">
                        <span style="line-height: 31px; font-weight: bold; color: white;">0%</span>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="card border border-success shadow-sm bg-success text-white">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">Successful</h6>
                                        <h2 id="successful-count" class="mb-0 fw-bold">0</h2>
                                    </div>
                                    <div class="fs-1 opacity-50">
                                        <i class="ti ti-circle-check"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border border-danger shadow-sm bg-danger text-white">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">Failed</h6>
                                        <h2 id="failed-count" class="mb-0 fw-bold">0</h2>
                                    </div>
                                    <div class="fs-1 opacity-50">
                                        <i class="ti ti-circle-x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border border-primary shadow-sm bg-primary text-white">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-1">Remaining</h6>
                                        <h2 id="remaining-count" class="mb-0 fw-bold">{{ count }}</h2>
                                    </div>
                                    <div class="fs-1 opacity-50">
                                        <i class="ti ti-clock-pause"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="results-card" class="card d-none border border-success bg-dark">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="ti ti-circle-check me-2"></i>Submission Results</h5>
            </div>
            <div class="card-body bg-dark">
                <div class="row">
                    <div class="col-md-4">
                        <div class="table-responsive">
                            <table class="table table-bordered table-dark">
                                <tbody>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-sum me-1"></i>Total Submissions</th>
                                        <td class="fw-bold bg-dark text-light" id="result-total">0</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-circle-check me-1"></i>Successful</th>
                                        <td class="fw-bold text-success bg-dark" id="result-successful">0</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-circle-x me-1"></i>Failed</th>
                                        <td class="fw-bold text-danger bg-dark" id="result-failed">0</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-percentage me-1"></i>Success Rate</th>
                                        <td class="fw-bold text-info bg-dark" id="result-rate">0%</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-clock me-1"></i>Total Time</th>
                                        <td class="fw-bold text-warning bg-dark" id="result-duration">00:00:00</td>
                                    </tr>
                                    <tr>
                                        <th class="bg-secondary text-white"><i class="ti ti-gauge me-1"></i>Average Speed</th>
                                        <td class="fw-bold text-primary bg-dark" id="result-speed">-- per min</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <!-- Chart Dashboard -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="position-relative bg-dark border border-secondary rounded p-3 mb-3 chart-container" style="height: 350px;">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="ti ti-chart-pie text-info me-2"></i>
                                        <h6 class="text-light mb-0">Distribution Chart</h6>
                                    </div>
                                    <div class="chart-wrapper" style="position: relative; height: 280px; width: 100%;">
                                        <canvas id="results-pie-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="position-relative bg-dark border border-secondary rounded p-3 mb-3 chart-container" style="height: 350px;">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="ti ti-chart-bar text-success me-2"></i>
                                        <h6 class="text-light mb-0">Comparison Chart</h6>
                                    </div>
                                    <div class="chart-wrapper" style="position: relative; height: 280px; width: 100%;">
                                        <canvas id="results-bar-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="position-relative bg-dark border border-secondary rounded p-3 chart-container" style="height: 280px;">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="ti ti-chart-line text-primary me-2"></i>
                                        <h6 class="text-light mb-0">Progress Timeline</h6>
                                    </div>
                                    <div class="chart-wrapper" style="position: relative; height: 210px; width: 100%;">
                                        <canvas id="time-progress-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
                        <i class="ti ti-home me-1"></i>Return to Home
                    </a>
                    <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-info me-2">
                        <i class="ti ti-eye me-1"></i>View Form Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- History Modal -->
<div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content bg-dark">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="historyModalLabel">
                    <i class="ti ti-history me-2"></i>Submission History
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body bg-dark">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-secondary text-white border-secondary"><i class="ti ti-search"></i></span>
                            <input type="text" class="form-control bg-dark text-light border-secondary" id="history-search" placeholder="Search by form title...">
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-warning me-2" id="clear-history-btn">
                            <i class="ti ti-trash me-1"></i>Clear All History
                        </button>
                        <button class="btn btn-secondary" id="refresh-history-btn">
                            <i class="ti ti-refresh me-1"></i>Refresh
                        </button>
                    </div>
                </div>
                
                <!-- History Statistics -->
                <div class="row mb-4" id="history-stats" style="display: none;">
                    <div class="col-md-3">
                        <div class="card border border-primary bg-primary text-white">
                            <div class="card-body text-center py-2">
                                <div class="d-flex align-items-center justify-content-center mb-1">
                                    <i class="ti ti-sum me-1"></i>
                                    <h6 class="mb-0">Total Submissions</h6>
                                </div>
                                <h4 id="stats-total">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border border-success bg-success text-white">
                            <div class="card-body text-center py-2">
                                <div class="d-flex align-items-center justify-content-center mb-1">
                                    <i class="ti ti-circle-check me-1"></i>
                                    <h6 class="mb-0">Successful</h6>
                                </div>
                                <h4 id="stats-successful">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border border-danger bg-danger text-white">
                            <div class="card-body text-center py-2">
                                <div class="d-flex align-items-center justify-content-center mb-1">
                                    <i class="ti ti-circle-x me-1"></i>
                                    <h6 class="mb-0">Failed</h6>
                                </div>
                                <h4 id="stats-failed">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border border-info bg-info text-white">
                            <div class="card-body text-center py-2">
                                <div class="d-flex align-items-center justify-content-center mb-1">
                                    <i class="ti ti-percentage me-1"></i>
                                    <h6 class="mb-0">Success Rate</h6>
                                </div>
                                <h4 id="stats-rate">0%</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- History Table -->
                <div class="table-responsive">
                    <table class="table table-bordered table-hover table-dark" id="history-table">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="ti ti-calendar me-1"></i>Date/Time</th>
                                <th><i class="ti ti-forms me-1"></i>Form Title</th>
                                <th><i class="ti ti-settings me-1"></i>Parameters</th>
                                <th><i class="ti ti-chart-bar me-1"></i>Results</th>
                                <th><i class="ti ti-badge me-1"></i>Status</th>
                                <th><i class="ti ti-tools me-1"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="history-tbody">
                            <tr>
                                <td colspan="6" class="text-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    Loading history...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Simplified and improved styles for dark theme */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: none; /* Remove transitions for performance */
}

.table-hover tbody tr:hover {
    background-color: rgba(108, 117, 125, 0.2) !important;
}

/* Minimal button hover effects */
.btn:hover {
    transform: none; /* Remove transform for performance */
}

/* Remove expensive animations */
.progress-bar-animated {
    animation: none !important; /* Disable striped animation for performance */
}

/* Better contrast for dark theme progress cards */
.progress {
    background-color: #495057 !important;
    border: 2px solid #6c757d !important;
}

/* Dark theme text colors */
.text-light {
    color: #f8f9fa !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Chart container styling for dark theme */
.chart-container {
    background: linear-gradient(135deg, #2d3436 0%, #36393b 100%);
    border-radius: 8px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
    overflow: hidden;
}

/* Chart wrapper to prevent infinite stretching */
.chart-wrapper {
    position: relative;
    overflow: hidden;
}

.chart-wrapper canvas {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
}

/* Dark theme form controls */
.form-control.bg-dark {
    background-color: #2d3436 !important;
    border-color: #495057 !important;
    color: #f8f9fa !important;
}

.form-control.bg-dark:focus {
    background-color: #343a40 !important;
    border-color: #17a2b8 !important;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    color: #f8f9fa !important;
}

.form-control.bg-dark::placeholder {
    color: #adb5bd !important;
}

/* Enhanced table styling for dark theme */
.table-dark {
    --bs-table-color: #f8f9fa;
    --bs-table-bg: #2d3436;
    --bs-table-border-color: #495057;
    --bs-table-striped-bg: #343a40;
    --bs-table-striped-color: #f8f9fa;
    --bs-table-active-bg: #495057;
    --bs-table-active-color: #f8f9fa;
    --bs-table-hover-bg: rgba(108, 117, 125, 0.2);
    --bs-table-hover-color: #f8f9fa;
}
</style>
{% endblock %}

{% block scripts %}
<!-- Include Chart.js for reliable charting -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

<script>
    $(document).ready(function() {
        // Template variables passed from Flask (fixed for proper JS parsing)
        const TEMPLATE_VARS = {
            formId: $('#form-id').text(),
            formUrl: $('#form-url').text(),
            count: parseInt($('#submission-count').text()),
            delayMin: {{ delay_min|default(1000) }},
            delayMax: {{ delay_max|default(3000) }},
            maxWorkers: {{ max_workers|default(5) }}
        };
        
        let resultsCharts = {
            pie: null,
            bar: null,
            timeline: null
        };
        let isRunning = false;
        let historyData = [];
        let updateInterval = null;
        let startTime = null;
        let timeTrackingData = [];
        
        // Optimized variables for performance
        let lastProgressUpdate = { successful: 0, failed: 0, progress: 0 };
        
        // Time formatting utilities
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        

        
        function updateElapsedTime() {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                $('#elapsed-time').text(formatTime(elapsed));
            }
        }
        
        // Start submission
        function startSubmission() {
            startTime = Date.now();
            $('#start-time').text(new Date().toLocaleTimeString());
            
            // Start time tracking
            const timeUpdateInterval = setInterval(function() {
                if (!isRunning) {
                    clearInterval(timeUpdateInterval);
                    return;
                }
                updateElapsedTime();
            }, 1000);
            
            $.ajax({
                url: '/api/submit/start',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    form_id: TEMPLATE_VARS.formId,
                    form_url: TEMPLATE_VARS.formUrl,
                    count: TEMPLATE_VARS.count,
                    delay_min: TEMPLATE_VARS.delayMin,
                    delay_max: TEMPLATE_VARS.delayMax,
                    max_workers: TEMPLATE_VARS.maxWorkers
                }),
                success: function(response) {
                    isRunning = true;
                    startProgressUpdates();
                },
                error: function(xhr) {
                    alert('Error: ' + (xhr.responseJSON ? xhr.responseJSON.error : 'Unknown error'));
                }
            });
        }
        
        // Optimized progress updates with interval
        function startProgressUpdates() {
            updateInterval = setInterval(updateProgress, 2000); // Reduced frequency to 2 seconds
            updateProgress(); // Initial call
        }
        
        function stopProgressUpdates() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }
        
        // Optimized update progress function
        function updateProgress() {
            if (!isRunning) {
                stopProgressUpdates();
                return;
            }
            
            $.ajax({
                url: '/api/submit/progress',
                type: 'GET',
                success: function(data) {
                    // Only update DOM if values changed (performance optimization)
                    if (data.progress !== lastProgressUpdate.progress) {
                        const progress = Math.round(data.progress * 100);
                        $('#progress-bar').css('width', progress + '%');
                        $('#progress-bar span').text(progress + '%');
                        lastProgressUpdate.progress = data.progress;
                    }
                    
                    // Update counters only if changed
                    if (data.successful !== lastProgressUpdate.successful) {
                        $('#successful-count').text(data.successful);
                        lastProgressUpdate.successful = data.successful;
                    }
                    
                    if (data.failed !== lastProgressUpdate.failed) {
                        $('#failed-count').text(data.failed);
                        lastProgressUpdate.failed = data.failed;
                    }
                    
                    $('#remaining-count').text(TEMPLATE_VARS.count - data.successful - data.failed);
                    
                    // Track progress over time for timeline chart
                    if (startTime) {
                        const elapsed = Math.floor((Date.now() - startTime) / 1000);
                        timeTrackingData.push({
                            time: elapsed,
                            successful: data.successful,
                            failed: data.failed,
                            total: data.successful + data.failed
                        });
                    }
                    
                    // Check if complete
                    if (data.is_complete) {
                        isRunning = false;
                        stopProgressUpdates();
                        showResults(data.result);
                    } else if (!data.is_running) {
                        isRunning = false;
                        stopProgressUpdates();
                        
                        // Always show results when stopped, even if cancelled
                        var finalResult = {
                            'total': data.successful + data.failed,
                            'successful': data.successful,
                            'failed': data.failed,
                            'success_rate': (data.successful + data.failed) > 0 ? data.successful / (data.successful + data.failed) : 0,
                            'cancelled': data.result ? data.result.cancelled : true
                        };
                        
                        // Show results immediately
                        showResults(finalResult);
                        
                        // Update progress bar to show final state
                        var finalProgress = finalResult.total > 0 ? 100 : Math.round(data.progress * 100);
                        $('#progress-bar').css('width', finalProgress + '%');
                        $('#progress-bar span').text(finalProgress + '%');
                        
                        // Update remaining count
                        $('#remaining-count').text(Math.max(0, TEMPLATE_VARS.count - finalResult.total));
                    }
                },
                error: function() {
                    // Keep trying but less frequently on error
                    console.log('Error updating progress, retrying...');
                }
            });
        }
        
        // Chart.js implementation with dark theme support
        function showResults(result) {
            const endTime = Date.now();
            const totalDuration = startTime ? Math.floor((endTime - startTime) / 1000) : 0;
            const submissionsPerMinute = totalDuration > 0 ? Math.round((result.total / totalDuration) * 60) : 0;
            
            // Update result table
            $('#result-total').text(result.total);
            $('#result-successful').text(result.successful);
            $('#result-failed').text(result.failed);
            $('#result-rate').text(Math.round(result.success_rate * 100) + '%');
            $('#result-duration').text(formatTime(totalDuration));
            $('#result-speed').text(submissionsPerMinute + ' per min');
            
            // Update card header if cancelled
            if (result.cancelled) {
                $('#results-card .card-header').removeClass('bg-success border-success').addClass('bg-warning border-warning');
                $('#results-card .card-header h5').html('<i class="ti ti-exclamation-triangle me-2"></i>Submission Results (Cancelled)');
            }
            
            // Create Chart.js visualizations
            if (typeof Chart !== 'undefined' && result.total > 0) {
                try {
                    createPieChart(result);
                    createBarChart(result);
                    createTimelineChart(result, totalDuration);
                } catch (error) {
                    console.error('Error creating charts:', error);
                }
            }
            
            // Show results card with smooth fade in
            $('#results-card').removeClass('d-none').hide().fadeIn(500);
            
            // Disable cancel button
            $('#cancel-btn').prop('disabled', true);
        }
        
        // Create donut chart using Chart.js
        function createPieChart(result) {
            const ctx = document.getElementById('results-pie-chart');
            if (!ctx) return;
            
            // Destroy existing chart
            if (resultsCharts.pie) {
                resultsCharts.pie.destroy();
            }
            
            resultsCharts.pie = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Successful', 'Failed'],
                    datasets: [{
                        data: [result.successful, result.failed],
                        backgroundColor: ['#28a745', '#dc3545'],
                        borderColor: ['#ffffff', '#ffffff'],
                        borderWidth: 2,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Results Distribution',
                            color: '#f8f9fa',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            position: 'bottom',
                            labels: { color: '#f8f9fa' }
                        }
                    },
                    elements: {
                        arc: {
                            borderRadius: 8
                        }
                    }
                }
            });
        }
        
        // Create bar chart using Chart.js
        function createBarChart(result) {
            const ctx = document.getElementById('results-bar-chart');
            if (!ctx) return;
            
            // Destroy existing chart
            if (resultsCharts.bar) {
                resultsCharts.bar.destroy();
            }
            
            resultsCharts.bar = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Successful', 'Failed'],
                    datasets: [{
                        label: 'Count',
                        data: [result.successful, result.failed],
                        backgroundColor: ['#28a745', '#dc3545'],
                        borderColor: ['#ffffff', '#ffffff'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Results Comparison',
                            color: '#f8f9fa',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: false
                        }
                    },
                    elements: {
                        bar: {
                            borderRadius: 4
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#f8f9fa' },
                            grid: { color: '#495057' }
                        },
                        y: {
                            ticks: { color: '#f8f9fa' },
                            grid: { color: '#495057' },
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Create timeline chart using Chart.js
        function createTimelineChart(result, totalDuration) {
            const ctx = document.getElementById('time-progress-chart');
            if (!ctx || timeTrackingData.length === 0) return;
            
            // Destroy existing chart
            if (resultsCharts.timeline) {
                resultsCharts.timeline.destroy();
            }
            
            resultsCharts.timeline = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeTrackingData.map(d => d.time),
                    datasets: [{
                        label: 'Total Submissions',
                        data: timeTrackingData.map(d => d.total),
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#17a2b8',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Submission Progress Over Time',
                            color: '#f8f9fa',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            labels: { color: '#f8f9fa' }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (seconds)',
                                color: '#f8f9fa'
                            },
                            ticks: { color: '#f8f9fa' },
                            grid: { color: '#495057' }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Submissions',
                                color: '#f8f9fa'
                            },
                            ticks: { color: '#f8f9fa' },
                            grid: { color: '#495057' },
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Cancel submission
        $('#cancel-btn').click(function() {
            if (!isRunning) return;
            
            if (confirm('Are you sure you want to cancel the submission?')) {
                const button = $(this);
                const originalText = button.html();
                button.html('<i class="ti ti-spinner ti-spin me-1"></i>Cancelling...');
                button.prop('disabled', true);
                
                $.ajax({
                    url: '/api/submit/cancel',
                    type: 'POST',
                    success: function() {
                        isRunning = false;
                        stopProgressUpdates();
                        button.html('<i class="ti ti-times-circle me-1"></i>Cancelled');
                        
                        // Force one final progress update to get the latest status
                        setTimeout(function() {
                            updateProgress();
                        }, 500);
                    },
                    error: function() {
                        button.html(originalText);
                        button.prop('disabled', false);
                        alert('Failed to cancel submission. Please try again.');
                    }
                });
            }
        });
        
        // History Modal Functions (simplified for performance)
        function loadHistory() {
            $.ajax({
                url: '/api/history',
                type: 'GET',
                success: function(data) {
                    historyData = data;
                    displayHistory(data);
                    loadHistoryStatistics();
                },
                error: function() {
                    $('#history-tbody').html('<tr><td colspan="6" class="text-center text-danger">Failed to load history</td></tr>');
                }
            });
        }
        
        function loadHistoryStatistics() {
            $.ajax({
                url: '/api/history/statistics',
                type: 'GET',
                success: function(stats) {
                    $('#stats-total').text(stats.total_submissions);
                    $('#stats-successful').text(stats.total_successful);
                    $('#stats-failed').text(stats.total_failed);
                    $('#stats-rate').text(Math.round(stats.average_success_rate * 100) + '%');
                    $('#history-stats').show();
                },
                error: function() {
                    $('#history-stats').hide();
                }
            });
        }
        
        function displayHistory(data) {
            const tbody = $('#history-tbody');
            tbody.empty();
            
            if (data.length === 0) {
                tbody.html('<tr><td colspan="6" class="text-center">No submission history found</td></tr>');
                return;
            }
            
            data.forEach(function(record) {
                const params = record.parameters;
                const result = record.result;
                
                const statusBadge = record.status === 'completed' ? 
                    '<span class="badge bg-success">Completed</span>' : 
                    '<span class="badge bg-danger">Failed</span>';
                
                const successRate = result.total > 0 ? Math.round(result.success_rate * 100) : 0;
                
                const row = `
                    <tr>
                        <td>${record.datetime}</td>
                        <td>
                            <div class="fw-bold text-light">${record.form_title}</div>
                            <small class="text-muted">${record.form_id}</small>
                        </td>
                        <td>
                            <small class="text-light">
                                Count: ${params.count}<br>
                                Delay: ${params.delay_min}-${params.delay_max}ms<br>
                                Workers: ${params.max_workers}
                            </small>
                        </td>
                        <td>
                            <small>
                                Success: <span class="text-success fw-bold">${result.successful}</span><br>
                                Failed: <span class="text-danger fw-bold">${result.failed}</span><br>
                                Rate: <span class="text-info fw-bold">${successRate}%</span>
                            </small>
                        </td>
                        <td>${statusBadge}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-primary repeat-btn" data-record-id="${record.id}">
                                    <i class="ti ti-repeat"></i>
                                </button>
                                <button type="button" class="btn btn-danger delete-btn" data-record-id="${record.id}">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
        
        // Search history (debounced for performance)
        let searchTimeout;
        $('#history-search').on('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val().toLowerCase();
            
            searchTimeout = setTimeout(function() {
                const filteredData = historyData.filter(record => 
                    record.form_title.toLowerCase().includes(searchTerm) ||
                    record.form_id.toLowerCase().includes(searchTerm)
                );
                displayHistory(filteredData);
            }, 300); // Debounce for 300ms
        });
        
        // Refresh history
        $('#refresh-history-btn').click(function() {
            loadHistory();
        });
        
        // Clear all history
        $('#clear-history-btn').click(function() {
            if (confirm('Are you sure you want to clear all submission history?')) {
                $.ajax({
                    url: '/api/history/clear',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({}),
                    success: function() {
                        loadHistory();
                        alert('History cleared successfully.');
                    },
                    error: function() {
                        alert('Failed to clear history.');
                    }
                });
            }
        });
        
        // Repeat submission from history
        $(document).on('click', '.repeat-btn', function() {
            const recordId = $(this).data('record-id');
            const button = $(this);
            const originalHtml = button.html();
            
            button.html('<i class="ti ti-spinner ti-spin"></i>');
            button.prop('disabled', true);
            
            $.ajax({
                url: `/api/history/${recordId}/repeat`,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        window.location.href = response.redirect_url;
                    } else {
                        alert('Failed to repeat submission: ' + (response.error || 'Unknown error'));
                        button.html(originalHtml);
                        button.prop('disabled', false);
                    }
                },
                error: function(xhr) {
                    alert('Failed to repeat submission: ' + (xhr.responseJSON ? xhr.responseJSON.error : 'Unknown error'));
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }
            });
        });
        
        // Delete history record
        $(document).on('click', '.delete-btn', function() {
            const recordId = $(this).data('record-id');
            
            if (confirm('Are you sure you want to delete this history record?')) {
                $.ajax({
                    url: `/api/history/${recordId}`,
                    type: 'DELETE',
                    success: function() {
                        loadHistory();
                        alert('History record deleted successfully.');
                    },
                    error: function() {
                        alert('Failed to delete history record.');
                    }
                });
            }
        });
        
        // Load history when modal is shown
        $('#historyModal').on('show.bs.modal', function() {
            loadHistory();
        });
        
        // Cleanup on page unload
        $(window).on('beforeunload', function() {
            stopProgressUpdates();
            if (resultsCharts.pie) resultsCharts.pie.destroy();
            if (resultsCharts.bar) resultsCharts.bar.destroy();
            if (resultsCharts.timeline) resultsCharts.timeline.destroy();
        });
        
        // Start submission on page load
        startSubmission();
    });
</script>
{% endblock %}
