# Google Form AutoFill Web Interface

This directory contains the web interface for the Google Form AutoFill tool. The web interface provides a user-friendly way to interact with the tool, allowing you to load forms, generate responses, review and edit responses, and submit forms.

## Features

- **Form Loading & Display**: Load Google Forms and view their structure
- **Response Generation**: Generate responses using different methods (manual, Gemini-assisted, fully automated)
- **Response Management**: Review, add, edit, and delete responses
- **Form Submission**: Submit forms with configurable parameters and track progress
- **Configuration**: Manage API keys and settings

## Directory Structure

- `app.py`: Main Flask application
- `routes.py`: Route definitions for web pages
- `api.py`: API endpoints for AJAX requests
- `forms.py`: Form definitions using Flask-WTF
- `utils.py`: Utility functions for the web interface
- `static/`: Static assets (CSS, JavaScript, images)
- `templates/`: HTML templates

## Running the Web Interface

1. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Run the web application:
   ```
   python run_web.py
   ```

3. Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

## Configuration

You can configure the web interface by setting environment variables or creating a `.env` file in the project root directory:

```
# Flask configuration
FLASK_ENV=development
FLASK_SECRET_KEY=your-secret-key

# Gemini API key
GEMINI_API_KEY=your-api-key-here

# Server configuration
PORT=5000
```

## API Endpoints

The web interface provides the following API endpoints:

- `GET /api/forms`: Get list of saved forms
- `GET /api/form/<form_id>`: Get form data
- `GET /api/form/<form_id>/questions`: Get form questions
- `GET /api/form/<form_id>/responses`: Get form responses
- `POST /api/form/<form_id>/response/<question_id>`: Add a response
- `PUT /api/form/<form_id>/response/<question_id>/<response_index>`: Update a response
- `DELETE /api/form/<form_id>/response/<question_id>/<response_index>`: Delete a response
- `POST /api/submit/start`: Start form submission
- `GET /api/submit/progress`: Get submission progress
- `POST /api/submit/cancel`: Cancel form submission
