"""
AI Provider Manager for Google Form AutoFill

This module provides a manager for handling multiple AI providers.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union

from .base_provider import AIProvider
from .provider_factory import ProviderFactory
from .model_manager import ModelManager

# Import all providers to register them with the factory
from .gemini_provider import GeminiProvider
from .openrouter_provider import OpenRouterProvider

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProviderManager:
    """Manager for handling multiple AI providers"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the provider manager
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.providers: Dict[str, AIProvider] = {}
        self.model_manager = ModelManager()
        self.default_provider = None
        
        # Load providers from configuration
        self._load_providers()
        
    def _load_providers(self) -> None:
        """Load providers from configuration file"""
        try:
            # Load configuration
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
            else:
                logger.warning(f"Configuration file {self.config_file} not found")
                config = {}
                
            # Get AI provider configuration
            ai_providers = config.get("ai_providers", {})
            
            # Initialize each provider
            for provider_name, provider_config in ai_providers.items():
                if not provider_config.get("enabled", False):
                    logger.info(f"Provider {provider_name} is disabled")
                    continue
                    
                # Get API keys
                api_keys = provider_config.get("api_keys", [])
                if not api_keys:
                    logger.warning(f"No API keys found for provider {provider_name}")
                    continue
                    
                # Get default model
                default_model = provider_config.get("default_model")
                
                # Get key strategy
                key_strategy = provider_config.get("key_strategy", "round_robin")
                
                # Create provider
                provider = ProviderFactory.create_provider(
                    provider_name,
                    api_keys,
                    default_model,
                    key_strategy
                )
                
                if provider:
                    self.providers[provider_name] = provider
                    logger.info(f"Loaded provider: {provider_name}")
                    
                    # Set as default if specified
                    if provider_config.get("is_default", False):
                        self.default_provider = provider_name
                        logger.info(f"Set {provider_name} as default provider")
                        
            # If no default provider set but we have providers, set the first one as default
            if not self.default_provider and self.providers:
                self.default_provider = next(iter(self.providers))
                logger.info(f"Set {self.default_provider} as default provider (first available)")
                
        except Exception as e:
            logger.error(f"Error loading providers: {str(e)}")
            
    def get_provider(self, provider_name: Optional[str] = None) -> Optional[AIProvider]:
        """
        Get a provider instance
        
        Args:
            provider_name: Name of the provider to get, or None to get the default
            
        Returns:
            Provider instance or None if not found
        """
        if not provider_name:
            provider_name = self.default_provider
            
        if not provider_name:
            logger.error("No default provider available")
            return None
            
        provider = self.providers.get(provider_name.lower())
        
        if not provider:
            logger.error(f"Provider not found: {provider_name}")
            return None
            
        return provider
        
    def get_available_providers(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about available providers
        
        Returns:
            Dictionary mapping provider names to information
        """
        result = {}
        
        for name, provider in self.providers.items():
            result[name] = {
                "name": name,
                "is_default": name == self.default_provider,
                "current_model": provider.get_current_model()
            }
            
        return result
        
    def get_available_models(self, provider_name: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get available models for providers
        
        Args:
            provider_name: Name of the provider to get models for, or None for all providers
            
        Returns:
            Dictionary mapping provider names to lists of model information
        """
        result = {}
        
        if provider_name:
            # Get models for specific provider
            provider = self.get_provider(provider_name)
            if provider:
                result[provider_name] = provider.get_available_models()
        else:
            # Get models for all providers
            for name, provider in self.providers.items():
                result[name] = provider.get_available_models()
                
        return result
        
    def set_provider_model(self, provider_name: str, model: str) -> bool:
        """
        Set the model for a provider
        
        Args:
            provider_name: Name of the provider
            model: Model identifier
            
        Returns:
            True if successful, False otherwise
        """
        provider = self.get_provider(provider_name)
        
        if not provider:
            return False
            
        return provider.set_model(model)
        
    def generate_text(self, prompt: str, provider_name: Optional[str] = None, 
                     model: Optional[str] = None, **kwargs) -> str:
        """
        Generate text using a provider
        
        Args:
            prompt: The prompt to send to the model
            provider_name: Name of the provider to use, or None to use the default
            model: Model to use, or None to use the provider's default
            **kwargs: Additional parameters to pass to the provider
            
        Returns:
            Generated text
            
        Raises:
            Exception: If provider not found or generation fails
        """
        provider = self.get_provider(provider_name)
        
        if not provider:
            raise Exception(f"Provider not found: {provider_name or 'default'}")
            
        # Set model if specified
        if model:
            provider.set_model(model)
            
        # Generate text
        return provider.generate_text(prompt, **kwargs)
        
    def batch_generate(self, prompts: List[str], provider_name: Optional[str] = None,
                      model: Optional[str] = None, **kwargs) -> List[str]:
        """
        Generate responses for multiple prompts
        
        Args:
            prompts: List of prompts to send to the model
            provider_name: Name of the provider to use, or None to use the default
            model: Model to use, or None to use the provider's default
            **kwargs: Additional parameters to pass to the provider
            
        Returns:
            List of generated responses
            
        Raises:
            Exception: If provider not found or generation fails
        """
        provider = self.get_provider(provider_name)
        
        if not provider:
            raise Exception(f"Provider not found: {provider_name or 'default'}")
            
        # Set model if specified
        if model:
            provider.set_model(model)
            
        # Generate responses
        return provider.batch_generate(prompts, **kwargs)
