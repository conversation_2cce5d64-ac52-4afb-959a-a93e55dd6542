"""
Enhanced Main Module for Google Form AutoFill

This module provides a command-line interface for the enhanced Google Form AutoFill tool.
"""

import argparse
import json
import os
import sys
import time
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dotenv import load_dotenv

from form_enhanced import EnhancedFormParser
from gemini_client import GeminiClient
from response_generator import ResponseGenerator
from response_storage import FormResponseManager
from submission_manager import SubmissionManager


def display_progress(progress: float, successful: int, failed: int):
    """
    Display progress bar for form submissions

    Args:
        progress: Progress as a float between 0 and 1
        successful: Number of successful submissions
        failed: Number of failed submissions
    """
    bar_length = 40
    filled_length = int(bar_length * progress)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)

    sys.stdout.write(f'\rProgress: [{bar}] {progress*100:.1f}% | Successful: {successful} | Failed: {failed}')
    sys.stdout.flush()

    if progress >= 1:
        print()


def load_form(url: str) -> Optional[EnhancedFormParser]:
    """
    Load a form from a URL

    Args:
        url: The URL of the form

    Returns:
        EnhancedFormParser instance if successful, None otherwise
    """
    print(f"Loading form from {url}...")
    parser = EnhancedFormParser(url)

    if parser.load_form():
        print(f"Form loaded: {parser.get_form_title()}")
        print(f"Found {len(parser.get_questions())} questions ({len(parser.get_questions(True))} required)")
        return parser
    else:
        print("Failed to load form")
        return None


def display_form(parser: EnhancedFormParser):
    """
    Display form structure

    Args:
        parser: EnhancedFormParser instance
    """
    print("\n" + "="*50)
    print(parser.display_form_structure())
    print("="*50 + "\n")


def initialize_gemini(api_key: Optional[Union[str, List[str]]] = None, key_strategy: str = "round_robin") -> Optional[GeminiClient]:
    """
    Initialize Gemini client with support for multiple API keys

    Args:
        api_key: Gemini API key or list of keys (if None, tries to load from .env file or environment)
        key_strategy: Strategy for key selection when multiple keys are provided
                     ("round_robin", "random", or "least_used")

    Returns:
        GeminiClient instance if successful, None otherwise
    """
    # Try to get API key from arguments, .env file, or environment variables
    if not api_key:
        # Load environment variables from .env file if it exists
        env_path = Path('.env')
        if env_path.exists():
            load_dotenv(dotenv_path=env_path)
            print("Loaded configuration from .env file")

        # Check for multiple API keys (GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.)
        api_keys = []
        i = 1
        while True:
            key = os.environ.get(f"GEMINI_API_KEY_{i}")
            if key:
                api_keys.append(key)
                i += 1
            else:
                break

        # If no numbered keys found, try the default key
        if not api_keys:
            default_key = os.environ.get("GEMINI_API_KEY")
            if default_key:
                api_keys.append(default_key)

        # Use the list of keys if multiple were found, otherwise use the single key
        if len(api_keys) > 1:
            api_key = api_keys
            print(f"Found {len(api_keys)} Gemini API keys")
        elif len(api_keys) == 1:
            api_key = api_keys[0]
            print("Found a single Gemini API key")
        else:
            api_key = None

    if not api_key:
        print("No Gemini API keys provided. Please provide API keys with --api-key, set them in a .env file, or set the GEMINI_API_KEY environment variables.")
        return None

    try:
        client = GeminiClient(api_key, key_strategy=key_strategy)
        print("Gemini client initialized successfully")
        return client
    except Exception as e:
        print(f"Failed to initialize Gemini client: {e}")
        return None


def generate_responses(parser: EnhancedFormParser, gemini: Optional[GeminiClient],
                      method: str, sample_count: int, examples_file: Optional[str] = None):
    """
    Generate responses for a form

    Args:
        parser: EnhancedFormParser instance
        gemini: GeminiClient instance (can be None for manual method)
        method: Generation method ("manual", "gemini_assisted", or "fully_automated")
        sample_count: Number of samples to generate per question
        examples_file: Optional path to JSON file with example responses
    """
    # Load examples if provided
    examples = None
    if examples_file:
        try:
            with open(examples_file, 'r', encoding='utf-8') as f:
                examples = json.load(f)
            print(f"Loaded examples from {examples_file}")
        except Exception as e:
            print(f"Failed to load examples: {e}")

    # Initialize generator
    generator = ResponseGenerator(gemini)

    # Generate responses
    print(f"Generating responses using {method} method...")
    result = generator.generate_responses_for_form(parser, method, sample_count, examples)

    print(result)


def review_responses(form_id: str):
    """
    Review generated responses for a form

    Args:
        form_id: The ID of the form
    """
    manager = FormResponseManager()
    form_data = manager.load_form_data(form_id)

    if not form_data:
        print(f"No data found for form ID: {form_id}")
        return

    print(f"\nForm: {form_data.get('form_title', 'Untitled Form')}")
    print(f"Form ID: {form_id}")
    print(f"Questions: {len(form_data.get('questions', []))}")
    print(f"Responses prepared: {len(form_data.get('responses', {}))}")

    # Display responses for each question
    for question in form_data.get('questions', []):
        question_id = question['id']
        question_title = question['title']

        responses = form_data.get('responses', {}).get(question_id, [])

        print(f"\n{'-'*50}")
        print(f"Question: {question_title}")
        print(f"Type: {question.get('type_name', 'Unknown')}")
        print(f"Required: {'Yes' if question.get('required', False) else 'No'}")

        if responses:
            print(f"Responses ({len(responses)}):")
            for i, response in enumerate(responses, 1):
                print(f"  {i}. {response['text']} (weight: {response['weight']})")
        else:
            print("No responses prepared")

    print(f"\n{'-'*50}")


def edit_response(form_id: str, question_index: int, response_index: int, new_text: str, new_weight: Optional[int] = None):
    """
    Edit a response

    Args:
        form_id: The ID of the form
        question_index: The index of the question (1-based)
        response_index: The index of the response (1-based)
        new_text: The new response text
        new_weight: The new weight (if None, keeps existing weight)
    """
    manager = FormResponseManager()
    form_data = manager.load_form_data(form_id)

    if not form_data:
        print(f"No data found for form ID: {form_id}")
        return

    # Get question ID from index
    if question_index < 1 or question_index > len(form_data.get('questions', [])):
        print(f"Invalid question index: {question_index}")
        return

    question_id = form_data['questions'][question_index - 1]['id']

    # Edit response
    success = manager.update_response(form_id, question_id, response_index - 1, new_text, new_weight)

    if success:
        print(f"Response updated successfully")
    else:
        print(f"Failed to update response")


def delete_response(form_id: str, question_index: int, response_index: int):
    """
    Delete a response

    Args:
        form_id: The ID of the form
        question_index: The index of the question (1-based)
        response_index: The index of the response (1-based)
    """
    manager = FormResponseManager()
    form_data = manager.load_form_data(form_id)

    if not form_data:
        print(f"No data found for form ID: {form_id}")
        return

    # Get question ID from index
    if question_index < 1 or question_index > len(form_data.get('questions', [])):
        print(f"Invalid question index: {question_index}")
        return

    question_id = form_data['questions'][question_index - 1]['id']

    # Delete response
    success = manager.delete_response(form_id, question_id, response_index - 1)

    if success:
        print(f"Response deleted successfully")
    else:
        print(f"Failed to delete response")


def add_response(form_id: str, question_index: int, text: str, weight: int = 1):
    """
    Add a new response

    Args:
        form_id: The ID of the form
        question_index: The index of the question (1-based)
        text: The response text
        weight: The weight of the response
    """
    manager = FormResponseManager()
    form_data = manager.load_form_data(form_id)

    if not form_data:
        print(f"No data found for form ID: {form_id}")
        return

    # Get question ID from index
    if question_index < 1 or question_index > len(form_data.get('questions', [])):
        print(f"Invalid question index: {question_index}")
        return

    question_id = form_data['questions'][question_index - 1]['id']

    # Add response
    success = manager.add_response(form_id, question_id, text, weight)

    if success:
        print(f"Response added successfully")
    else:
        print(f"Failed to add response")


def submit_form(form_id: str, form_url: str, count: int, delay_min: float = 1.0, delay_max: float = 3.0):
    """
    Submit form responses

    Args:
        form_id: The ID of the form
        form_url: The URL of the form
        count: Number of submissions to make
        delay_min: Minimum delay between submissions in seconds
        delay_max: Maximum delay between submissions in seconds
    """
    manager = SubmissionManager()

    print(f"Preparing to submit {count} responses to form...")
    print(f"Delay between submissions: {delay_min}-{delay_max} seconds")

    # Confirm submission
    confirm = input("Continue with submission? (y/n): ")
    if confirm.lower() != 'y':
        print("Submission cancelled")
        return

    # Submit forms
    print(f"Starting submission process...")
    result = manager.batch_submit(form_id, form_url, count, (delay_min, delay_max), display_progress)

    # Display results
    print("\nSubmission complete!")
    print(f"Total submissions: {result['total']}")
    print(f"Successful: {result['successful']}")
    print(f"Failed: {result['failed']}")
    print(f"Success rate: {result['success_rate']*100:.1f}%")


def load_default_config():
    """
    Load default configuration from .env file

    Returns:
        Dictionary with default configuration values
    """
    # Load environment variables from .env file if it exists
    env_path = Path('.env')
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)

    # Get default values from environment variables

    # Load API keys using the utility function
    api_key = load_api_keys()

    config = {
        "api_key": api_key,
        "key_strategy": os.environ.get("GEMINI_KEY_STRATEGY", "round_robin"),
        "form_url": os.environ.get("DEFAULT_FORM_URL"),
        "submission_count": int(os.environ.get("DEFAULT_SUBMISSION_COUNT", "5")),
        "min_delay": float(os.environ.get("MIN_SUBMISSION_DELAY", "1.0")),
        "max_delay": float(os.environ.get("MAX_SUBMISSION_DELAY", "3.0")),
        "sample_count": int(os.environ.get("DEFAULT_SAMPLE_COUNT", "5")),
        "generation_method": os.environ.get("DEFAULT_GENERATION_METHOD", "gemini_assisted")
    }

    return config


def load_api_keys() -> Optional[Union[str, List[str]]]:
    """
    Load API keys from environment variables.

    Returns:
        A single API key (str), a list of API keys, or None if no keys are found.
    """
    api_keys = []
    i = 1
    while True:
        key = os.environ.get(f"GEMINI_API_KEY_{i}")
        if key:
            api_keys.append(key)
            i += 1
        else:
            break

    # If no numbered keys found, try the default key
    if not api_keys:
        default_key = os.environ.get("GEMINI_API_KEY")
        if default_key:
            api_keys.append(default_key)

    # Use the list of keys if multiple were found, otherwise use the single key
    api_key = api_keys if len(api_keys) > 1 else (api_keys[0] if api_keys else None)

    config = {
        "api_key": api_key,
        "key_strategy": os.environ.get("GEMINI_KEY_STRATEGY", "round_robin"),
        "form_url": os.environ.get("DEFAULT_FORM_URL"),
        "submission_count": int(os.environ.get("DEFAULT_SUBMISSION_COUNT", "5")),
        "min_delay": float(os.environ.get("MIN_SUBMISSION_DELAY", "1.0")),
        "max_delay": float(os.environ.get("MAX_SUBMISSION_DELAY", "3.0")),
        "sample_count": int(os.environ.get("DEFAULT_SAMPLE_COUNT", "5")),
        "generation_method": os.environ.get("DEFAULT_GENERATION_METHOD", "gemini_assisted")
    }

    return config


def main():
    """Main function"""
    # Load default configuration from .env file
    config = load_default_config()

    parser = argparse.ArgumentParser(description="Enhanced Google Form AutoFill and Submit")

    # Main arguments
    parser.add_argument("--url", help="Google Form URL")
    parser.add_argument("--api-key", help="Gemini API key or comma-separated list of keys")
    parser.add_argument("--key-strategy", choices=["round_robin", "random", "least_used"],
                      default=config["key_strategy"], help="Strategy for API key selection when multiple keys are provided")

    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Load form command
    load_parser = subparsers.add_parser("load", help="Load and display form structure")
    load_parser.add_argument("url", nargs="?", default=config["form_url"], help="Google Form URL")

    # Generate responses command
    gen_parser = subparsers.add_parser("generate", help="Generate responses for a form")
    gen_parser.add_argument("url", nargs="?", default=config["form_url"], help="Google Form URL")
    gen_parser.add_argument("--method", choices=["manual", "gemini_assisted", "fully_automated"],
                          default=config["generation_method"], help="Response generation method")
    gen_parser.add_argument("--samples", type=int, default=config["sample_count"],
                          help="Number of samples to generate per question")
    gen_parser.add_argument("--examples", help="Path to JSON file with example responses")
    gen_parser.add_argument("--api-key", default=config["api_key"], help="Gemini API key or comma-separated list of keys")
    gen_parser.add_argument("--key-strategy", choices=["round_robin", "random", "least_used"],
                          default=config["key_strategy"], help="Strategy for API key selection when multiple keys are provided")

    # Review responses command
    review_parser = subparsers.add_parser("review", help="Review generated responses")
    review_parser.add_argument("form_id", help="Form ID")

    # Edit response command
    edit_parser = subparsers.add_parser("edit", help="Edit a response")
    edit_parser.add_argument("form_id", help="Form ID")
    edit_parser.add_argument("question", type=int, help="Question index (1-based)")
    edit_parser.add_argument("response", type=int, help="Response index (1-based)")
    edit_parser.add_argument("text", help="New response text")
    edit_parser.add_argument("--weight", type=int, help="New weight")

    # Delete response command
    delete_parser = subparsers.add_parser("delete", help="Delete a response")
    delete_parser.add_argument("form_id", help="Form ID")
    delete_parser.add_argument("question", type=int, help="Question index (1-based)")
    delete_parser.add_argument("response", type=int, help="Response index (1-based)")

    # Add response command
    add_parser = subparsers.add_parser("add", help="Add a new response")
    add_parser.add_argument("form_id", help="Form ID")
    add_parser.add_argument("question", type=int, help="Question index (1-based)")
    add_parser.add_argument("text", help="Response text")
    add_parser.add_argument("--weight", type=int, default=1, help="Response weight")

    # Submit form command
    submit_parser = subparsers.add_parser("submit", help="Submit form responses")
    submit_parser.add_argument("form_id", help="Form ID")
    submit_parser.add_argument("url", nargs="?", default=config["form_url"], help="Google Form URL")
    submit_parser.add_argument("count", type=int, nargs="?", default=config["submission_count"],
                             help="Number of submissions to make")
    submit_parser.add_argument("--delay-min", type=float, default=config["min_delay"],
                             help="Minimum delay between submissions in seconds")
    submit_parser.add_argument("--delay-max", type=float, default=config["max_delay"],
                             help="Maximum delay between submissions in seconds")

    args = parser.parse_args()

    # Process commands
    if args.command == "load":
        if not args.url:
            print("Error: No form URL provided. Please specify a URL or set DEFAULT_FORM_URL in .env file.")
            return
        parser = load_form(args.url)
        if parser:
            display_form(parser)

    elif args.command == "generate":
        if not args.url:
            print("Error: No form URL provided. Please specify a URL or set DEFAULT_FORM_URL in .env file.")
            return
        parser = load_form(args.url)
        if parser:
            # Process API key input (handle comma-separated list)
            api_key = args.api_key
            if isinstance(api_key, str) and ',' in api_key:
                api_key = [key.strip() for key in api_key.split(',') if key.strip()]

            gemini = initialize_gemini(api_key, args.key_strategy)
            if args.method != "manual" and not gemini:
                print(f"Cannot use {args.method} method without Gemini API key")
                return
            generate_responses(parser, gemini, args.method, args.samples, args.examples)

    elif args.command == "review":
        review_responses(args.form_id)

    elif args.command == "edit":
        edit_response(args.form_id, args.question, args.response, args.text, args.weight)

    elif args.command == "delete":
        delete_response(args.form_id, args.question, args.response)

    elif args.command == "add":
        add_response(args.form_id, args.question, args.text, args.weight)

    elif args.command == "submit":
        if not args.url:
            print("Error: No form URL provided. Please specify a URL or set DEFAULT_FORM_URL in .env file.")
            return
        submit_form(args.form_id, args.url, args.count, args.delay_min, args.delay_max)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
