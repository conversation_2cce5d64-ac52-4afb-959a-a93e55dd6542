"""
API Module for Google Form AutoFill Web Interface

This module defines the API endpoints for the web interface.
"""

import json
import threading
from flask import jsonify, request, session

from form_enhanced import EnhancedFormParser
from response_storage import FormResponseManager
from submission_manager import SubmissionManager
from submission_history import SubmissionHistory

# Global variables for tracking submission progress
submission_thread = None
submission_manager = None
submission_progress = {
    'progress': 0,
    'total': 0,
    'successful': 0,
    'failed': 0,
    'is_running': False,
    'is_complete': False,
    'result': None
}


def register_api_routes(app):
    """Register API routes with the Flask application"""

    @app.route('/api/forms', methods=['GET'])
    def api_get_forms():
        """Get list of saved forms"""
        manager = FormResponseManager()
        forms = []

        for form_id in manager.storage.list_saved_forms():
            form_data = manager.load_form_data(form_id)
            if form_data:
                forms.append({
                    'id': form_id,
                    'title': form_data.get('form_title', 'Untitled Form'),
                    'question_count': len(form_data.get('questions', [])),
                    'response_count': len(form_data.get('responses', {}))
                })

        return jsonify(forms)

    @app.route('/api/form/<form_id>', methods=['GET', 'DELETE'])
    def api_form_operations(form_id):
        """Get or delete form data"""
        manager = FormResponseManager()

        if request.method == 'GET':
            form_data = manager.load_form_data(form_id)

            if not form_data:
                return jsonify({'error': 'Form not found'}), 404

            return jsonify(form_data)

        elif request.method == 'DELETE':
            # Delete the form
            success = manager.delete_form(form_id)

            if not success:
                return jsonify({'error': 'Failed to delete form'}), 500

            return jsonify({'success': True})

    @app.route('/api/form/<form_id>/questions', methods=['GET'])
    def api_get_questions(form_id):
        """Get form questions"""
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        return jsonify(form_data.get('questions', []))

    @app.route('/api/form/<form_id>/responses', methods=['GET'])
    def api_get_responses(form_id):
        """Get form responses"""
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        return jsonify(form_data.get('responses', {}))

    @app.route('/api/form/<form_id>/response/<question_id>', methods=['POST'])
    def api_add_response(form_id, question_id):
        """Add a response"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        text = data.get('text')
        weight = data.get('weight', 1)

        if not text:
            return jsonify({'error': 'No response text provided'}), 400

        manager = FormResponseManager()
        success = manager.add_response(form_id, question_id, text, weight)

        if not success:
            return jsonify({'error': 'Failed to add response'}), 500

        return jsonify({'success': True})

    @app.route('/api/form/<form_id>/response/<question_id>/<int:response_index>', methods=['PUT'])
    def api_update_response(form_id, question_id, response_index):
        """Update a response"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        text = data.get('text')
        weight = data.get('weight')

        if not text:
            return jsonify({'error': 'No response text provided'}), 400

        manager = FormResponseManager()
        success = manager.update_response(form_id, question_id, response_index, text, weight)

        if not success:
            return jsonify({'error': 'Failed to update response'}), 500

        return jsonify({'success': True})

    @app.route('/api/form/<form_id>/response/<question_id>/<int:response_index>', methods=['DELETE'])
    def api_delete_response(form_id, question_id, response_index):
        """Delete a response"""
        manager = FormResponseManager()
        success = manager.delete_response(form_id, question_id, response_index)

        if not success:
            return jsonify({'error': 'Failed to delete response'}), 500

        return jsonify({'success': True})

    @app.route('/api/batch_delete_responses', methods=['POST'])
    def api_batch_delete_responses():
        """Batch delete responses"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        question_id = data.get('question_id')
        response_indices = data.get('response_indices', [])

        if not all([form_id, question_id]) or not response_indices:
            return jsonify({'error': 'Missing required parameters'}), 400

        if not isinstance(response_indices, list):
            return jsonify({'error': 'response_indices must be a list'}), 400

        manager = FormResponseManager()
        success = manager.batch_delete_responses(form_id, question_id, response_indices)

        if not success:
            return jsonify({'error': 'Failed to delete responses'}), 500

        return jsonify({
            'success': True,
            'message': f'Successfully deleted {len(response_indices)} responses'
        })

    @app.route('/api/batch_update_weight', methods=['POST'])
    def api_batch_update_weight():
        """Batch update response weights"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        question_id = data.get('question_id')
        response_indices = data.get('response_indices', [])
        weight = data.get('weight')

        if not all([form_id, question_id, weight is not None]) or not response_indices:
            return jsonify({'error': 'Missing required parameters'}), 400

        if not isinstance(response_indices, list):
            return jsonify({'error': 'response_indices must be a list'}), 400

        try:
            weight = int(weight)
            if weight < 1:
                return jsonify({'error': 'Weight must be at least 1'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Weight must be a valid integer'}), 400

        manager = FormResponseManager()
        success = manager.batch_update_weight(form_id, question_id, response_indices, weight)

        if not success:
            return jsonify({'error': 'Failed to update response weights'}), 500

        return jsonify({
            'success': True,
            'message': f'Successfully updated weight to {weight} for {len(response_indices)} responses'
        })

    @app.route('/api/update_no_duplication', methods=['POST'])
    def api_update_no_duplication():
        """Update no duplication setting for a form (legacy - kept for compatibility)"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        no_duplication = data.get('no_duplication')

        if not form_id or no_duplication is None:
            return jsonify({'error': 'Missing required parameters'}), 400

        if not isinstance(no_duplication, bool):
            return jsonify({'error': 'no_duplication must be a boolean'}), 400

        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Update the no duplication setting (legacy - global form level)
        form_data['no_duplication'] = no_duplication
        manager.save_form_data(form_id, form_data)

        return jsonify({
            'success': True,
            'message': f'No duplication mode {"enabled" if no_duplication else "disabled"}'
        })

    @app.route('/api/update_question_no_duplication', methods=['POST'])
    def api_update_question_no_duplication():
        """Update no duplication setting for a specific question"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        question_id = data.get('question_id')
        no_duplication = data.get('no_duplication')

        if not all([form_id, question_id]) or no_duplication is None:
            return jsonify({'error': 'Missing required parameters'}), 400

        if not isinstance(no_duplication, bool):
            return jsonify({'error': 'no_duplication must be a boolean'}), 400

        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Find the question and update its no_duplication setting
        question_found = False
        for question in form_data.get('questions', []):
            if str(question['id']) == str(question_id):
                question['no_duplication'] = no_duplication
                question_found = True
                break

        if not question_found:
            return jsonify({'error': 'Question not found'}), 404

        manager.save_form_data(form_id, form_data)

        return jsonify({
            'success': True,
            'message': f'No duplication mode {"enabled" if no_duplication else "disabled"} for question'
        })

    @app.route('/api/submit/start', methods=['POST'])
    def api_start_submission():
        """Start form submission"""
        global submission_thread, submission_progress

        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        form_url = data.get('form_url')
        count = data.get('count')
        delay_min = data.get('delay_min')  # Now in milliseconds
        delay_max = data.get('delay_max')  # Now in milliseconds
        max_workers = data.get('max_workers', 5)

        if not all([form_id, form_url, count, delay_min, delay_max]):
            return jsonify({'error': 'Missing required parameters'}), 400

        # Reset progress
        submission_progress = {
            'progress': 0,
            'total': count,
            'successful': 0,
            'failed': 0,
            'is_running': True,
            'is_complete': False,
            'result': None
        }

        # Start submission in a separate thread
        submission_thread = threading.Thread(
            target=submit_form_thread,
            args=(form_id, form_url, count, (delay_min, delay_max), max_workers)
        )
        submission_thread.daemon = True
        submission_thread.start()

        return jsonify({'success': True})

    @app.route('/api/submit/progress', methods=['GET'])
    def api_get_submission_progress():
        """Get submission progress"""
        global submission_progress
        return jsonify(submission_progress)

    @app.route('/api/submit/cancel', methods=['POST'])
    def api_cancel_submission():
        """Cancel form submission"""
        global submission_progress, submission_manager

        submission_progress['is_running'] = False
        submission_progress['is_complete'] = True

        # Cancel the submission manager if it exists
        if submission_manager:
            submission_manager.cancel()

        # Set cancellation result
        submission_progress['result'] = {
            'total': submission_progress['successful'] + submission_progress['failed'],
            'successful': submission_progress['successful'],
            'failed': submission_progress['failed'],
            'success_rate': submission_progress['successful'] / max(1, submission_progress['successful'] + submission_progress['failed']),
            'cancelled': True
        }

        return jsonify({'success': True})

    @app.route('/api/generate_for_question', methods=['POST'])
    def api_generate_for_question():
        """Generate responses for a single question using AI or manual methods"""
        try:
            # Get form data
            form_id = request.form.get('form_id')
            question_id = request.form.get('question_id')
            method = request.form.get('method')
            provider_name = request.form.get('provider', 'gemini')
            model = request.form.get('model', '')
            api_key = request.form.get('api_key', '')
            prompt = request.form.get('prompt', '')
            count = request.form.get('count', '5')

            if not all([form_id, question_id, method]):
                return jsonify({'error': 'Missing required parameters: form_id, question_id, method'}), 400

            try:
                count = int(count)
            except ValueError:
                count = 5

            # Initialize manager
            manager = FormResponseManager()

            # Get form data
            form_data = manager.load_form_data(form_id)
            if not form_data:
                return jsonify({'error': 'Form not found'}), 404

            # Find the question
            question = None
            for q in form_data.get('questions', []):
                if q['id'] == question_id:
                    question = q
                    break

            if not question:
                return jsonify({'error': 'Question not found'}), 404

            # Handle different methods
            if method in ['ai_assisted', 'fully_automated']:
                # Check if API key is needed
                from config_manager import ConfigManager
                config_manager = ConfigManager()
                config = config_manager.get_config()
                ai_providers = config.get('ai_providers', {})
                provider_config = ai_providers.get(provider_name, {})
                configured_keys = provider_config.get('api_keys', [])

                # If no configured keys and no provided key, return error
                if not configured_keys and not api_key:
                    return jsonify({'error': f'API key is required. No {provider_name} API keys are configured in settings.'}), 400

                # Import provider manager
                from ai_providers.provider_manager import ProviderManager
                from ai_providers.provider_factory import ProviderFactory

                # Get or create provider
                if api_key:
                    # Use provided API key
                    provider = ProviderFactory.create_provider(provider_name, api_key, model)
                else:
                    # Use configured provider
                    provider_manager = ProviderManager()
                    provider = provider_manager.get_provider(provider_name)
                    if model:
                        provider.set_model(model)

                if not provider:
                    return jsonify({'error': f'Failed to initialize {provider_name} provider'}), 500

                # Generate AI responses
                if not question.get('is_open_ended', False):
                    return jsonify({'error': 'AI generation is only supported for open-ended questions'}), 400

                # Create prompt
                if prompt:
                    full_prompt = f"Question: {question['title']}\n\nInstructions: {prompt}\n\nGenerate {count} diverse and realistic responses:"
                else:
                    full_prompt = f"Question: {question['title']}\n\nGenerate {count} diverse and realistic responses:"

                # Generate responses
                response_text = provider.generate_text(full_prompt, max_tokens=1000)

                # Parse responses (simple line-based parsing)
                responses = []
                lines = response_text.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    # Remove numbering and bullet points
                    line = line.lstrip('**********.-• ')
                    if line and len(line) > 3:  # Filter out very short responses
                        responses.append(line)

                # Limit to requested count
                responses = responses[:count]

                if not responses:
                    return jsonify({'error': 'No valid responses generated'}), 500

                # Save responses
                for response in responses:
                    weight = 1 if method == 'ai_assisted' else 2  # Higher weight for fully automated
                    manager.add_response(form_id, question_id, response, weight)

                return jsonify({
                    'success': True,
                    'message': f'Generated {len(responses)} AI responses for the question',
                    'responses_count': len(responses)
                })

            elif method.startswith('manual_'):
                # Handle manual methods
                if method == 'manual_text':
                    responses_text = request.form.get('responses', '')
                    add_weights = request.form.get('add_weights') == 'on'

                    if not responses_text:
                        return jsonify({'error': 'No responses provided'}), 400

                    responses = []
                    for line in responses_text.strip().split('\n'):
                        line = line.strip()
                        if line:
                            if add_weights and '[' in line and ']' in line:
                                # Parse weight from format "Response [weight]"
                                try:
                                    weight_start = line.rfind('[')
                                    weight_end = line.rfind(']')
                                    weight = int(line[weight_start+1:weight_end])
                                    text = line[:weight_start].strip()
                                    responses.append((text, weight))
                                except (ValueError, IndexError):
                                    responses.append((line, 1))
                            else:
                                responses.append((line, 1))

                    # Save responses
                    for text, weight in responses:
                        manager.add_response(form_id, question_id, text, weight)

                    return jsonify({
                        'success': True,
                        'message': f'Added {len(responses)} manual responses',
                        'responses_count': len(responses)
                    })

                else:
                    # Handle other manual methods (options, linear scale, etc.)
                    # This would need more specific implementation based on the method
                    return jsonify({'error': f'Manual method {method} not yet implemented'}), 400

            else:
                return jsonify({'error': f'Unknown method: {method}'}), 400

        except Exception as e:
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'Error generating responses: {str(e)}'}), 500

    @app.route('/api/generate_weighted_responses', methods=['POST'])
    def api_generate_weighted_responses():
        """Generate weighted responses for multiple choice questions"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        form_id = data.get('form_id')
        question_id = data.get('question_id')
        total_responses = data.get('total_responses')
        option_weights = data.get('option_weights', [])

        if not all([form_id, question_id, total_responses]) or not option_weights:
            return jsonify({'error': 'Missing required parameters'}), 400

        try:
            total_responses = int(total_responses)
            if total_responses < 1:
                return jsonify({'error': 'Total responses must be at least 1'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Total responses must be a valid integer'}), 400

        # Validate option weights
        total_weight = sum(option.get('weight', 0) for option in option_weights)
        if total_weight != 100:
            return jsonify({'error': 'Total weight must equal 100%'}), 400

        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Clear existing responses for this question
        if str(question_id) in form_data.get('responses', {}):
            form_data['responses'][str(question_id)] = []
            manager.save_form_data(form_id, form_data)

        # Generate responses based on weights
        responses_added = 0
        for option in option_weights:
            option_text = option.get('text')
            option_count = option.get('count', 0)

            if option_text and option_count > 0:
                # Add the response with the specified count as weight
                manager.add_response(form_id, question_id, option_text, option_count)
                responses_added += 1

        if responses_added == 0:
            return jsonify({'error': 'No responses were generated'}), 400

        return jsonify({
            'success': True,
            'message': f'Successfully generated weighted responses for {responses_added} options',
            'total_responses': total_responses
        })

    # Submission History API endpoints
    @app.route('/api/history', methods=['GET'])
    def api_get_history():
        """Get submission history"""
        limit = request.args.get('limit', type=int)
        form_id = request.args.get('form_id')

        history_manager = SubmissionHistory()
        history = history_manager.get_history(limit=limit, form_id=form_id)

        return jsonify(history)

    @app.route('/api/history/<record_id>', methods=['GET'])
    def api_get_history_record(record_id):
        """Get a specific history record"""
        history_manager = SubmissionHistory()
        record = history_manager.get_record(record_id)

        if not record:
            return jsonify({'error': 'Record not found'}), 404

        return jsonify(record)

    @app.route('/api/history/<record_id>', methods=['DELETE'])
    def api_delete_history_record(record_id):
        """Delete a history record"""
        history_manager = SubmissionHistory()
        success = history_manager.delete_record(record_id)

        if not success:
            return jsonify({'error': 'Record not found or failed to delete'}), 404

        return jsonify({'success': True})

    @app.route('/api/history/clear', methods=['POST'])
    def api_clear_history():
        """Clear submission history"""
        data = request.json or {}
        form_id = data.get('form_id')

        history_manager = SubmissionHistory()
        success = history_manager.clear_history(form_id=form_id)

        return jsonify({'success': success})

    @app.route('/api/history/statistics', methods=['GET'])
    def api_get_history_statistics():
        """Get submission history statistics"""
        history_manager = SubmissionHistory()
        stats = history_manager.get_statistics()

        return jsonify(stats)

    # Configuration API endpoints
    @app.route('/api/config/providers', methods=['GET'])
    def api_get_provider_config():
        """Get current AI provider configuration"""
        try:
            from config_manager import ConfigManager
            config_manager = ConfigManager()
            config = config_manager.get_config()

            ai_providers = config.get('ai_providers', {})

            # Find default provider
            default_provider = 'gemini'
            for provider_name, provider_config in ai_providers.items():
                if provider_config.get('is_default', False):
                    default_provider = provider_name
                    break

            # Get provider configurations
            providers = {}
            for provider_name, provider_config in ai_providers.items():
                providers[provider_name] = {
                    'enabled': provider_config.get('enabled', False),
                    'is_default': provider_config.get('is_default', False),
                    'default_model': provider_config.get('default_model', ''),
                    'has_api_keys': len(provider_config.get('api_keys', [])) > 0,
                    'key_strategy': provider_config.get('key_strategy', 'round_robin')
                }

            return jsonify({
                'success': True,
                'default_provider': default_provider,
                'providers': providers
            })
        except Exception as e:
            return jsonify({'error': f'Error getting provider configuration: {str(e)}'}), 500

    @app.route('/api/config/default', methods=['GET'])
    def api_get_default_config():
        """Get default AI configuration for forms"""
        try:
            from config_manager import ConfigManager
            config_manager = ConfigManager()
            config = config_manager.get_config()

            ai_providers = config.get('ai_providers', {})

            # Find default provider
            default_provider = 'gemini'
            default_model = ''
            api_keys_available = False

            for provider_name, provider_config in ai_providers.items():
                if provider_config.get('is_default', False):
                    default_provider = provider_name
                    default_model = provider_config.get('default_model', '')
                    api_keys_available = len(provider_config.get('api_keys', [])) > 0
                    break

            return jsonify({
                'success': True,
                'default_provider': default_provider,
                'default_model': default_model,
                'api_keys_available': api_keys_available
            })
        except Exception as e:
            return jsonify({'error': f'Error getting default configuration: {str(e)}'}), 500

    # Customer Interface API endpoints
    @app.route('/api/customer/config/<form_id>', methods=['GET'])
    def api_get_customer_config(form_id):
        """Get customer configuration for a form"""
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        customer_config = form_data.get('customer_config', {})
        return jsonify(customer_config)

    @app.route('/api/customer/config/<form_id>', methods=['POST'])
    def api_save_customer_config(form_id):
        """Save customer configuration for a form"""
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Update the customer configuration
        form_data['customer_config'] = data
        manager.save_form_data(form_id, form_data)

        return jsonify({'success': True, 'message': 'Configuration saved successfully'})

    @app.route('/api/customer/shared/<share_id>', methods=['GET'])
    def api_get_shared_config(share_id):
        """Get a shared customer configuration"""
        manager = FormResponseManager()
        found_form = None

        for form_id in manager.storage.list_saved_forms():
            form_data = manager.load_form_data(form_id)
            if form_data and 'customer_config' in form_data and form_data['customer_config'].get('share_id') == share_id:
                found_form = form_data
                break

        if not found_form:
            return jsonify({'error': 'Shared configuration not found'}), 404

        # Return the form data and customer configuration
        return jsonify({
            'form_title': found_form.get('form_title'),
            'form_description': found_form.get('form_description'),
            'questions': found_form.get('questions'),
            'customer_config': found_form.get('customer_config')
        })

    @app.route('/api/history/<record_id>/repeat', methods=['POST'])
    def api_repeat_submission(record_id):
        """Repeat a submission from history"""
        history_manager = SubmissionHistory()
        record = history_manager.get_record(record_id)

        if not record:
            return jsonify({'error': 'History record not found'}), 404

        # Extract parameters from history record
        form_id = record.get('form_id')
        form_url = record.get('form_url')
        parameters = record.get('parameters', {})

        count = parameters.get('count')
        delay_min = parameters.get('delay_min')
        delay_max = parameters.get('delay_max')
        max_workers = parameters.get('max_workers', 5)

        if not all([form_id, form_url, count, delay_min, delay_max]):
            return jsonify({'error': 'Invalid history record parameters'}), 400

        # Check if form still exists
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)
        if not form_data:
            return jsonify({'error': 'Form no longer exists'}), 404

        # Store submission parameters in session for the progress page
        session['submission_form_id'] = form_id
        session['submission_form_url'] = form_url
        session['submission_count'] = count
        session['submission_delay_min'] = delay_min
        session['submission_delay_max'] = delay_max
        session['submission_max_workers'] = max_workers

        return jsonify({
            'success': True,
            'redirect_url': '/submission_progress',
            'parameters': parameters
        })


def update_progress(progress, successful, failed):
    """Update submission progress"""
    global submission_progress

    submission_progress['progress'] = progress
    submission_progress['successful'] = successful
    submission_progress['failed'] = failed


def submit_form_thread(form_id, form_url, count, delay_range, max_workers):
    """Submit form in a separate thread"""
    global submission_progress, submission_manager

    # Create new submission manager instance for this thread
    manager = SubmissionManager()

    # Get form data for history recording
    form_manager = FormResponseManager()
    form_data = form_manager.load_form_data(form_id)
    form_title = form_data.get('form_title', 'Unknown Form') if form_data else 'Unknown Form'

    try:
        # Submit forms with multithreading support
        result = manager.batch_submit(form_id, form_url, count, delay_range, max_workers, update_progress)

        # Update progress
        submission_progress['is_complete'] = True
        submission_progress['is_running'] = False
        submission_progress['result'] = result

        # Save to history after completion
        history_manager = SubmissionHistory()
        history_manager.add_submission_record(
            form_id=form_id,
            form_url=form_url,
            form_title=form_title,
            count=count,
            delay_min=delay_range[0],
            delay_max=delay_range[1],
            max_workers=max_workers,
            result=result
        )

    except Exception as e:
        result = {
            'error': str(e),
            'total': count,
            'successful': submission_progress['successful'],
            'failed': submission_progress['failed']
        }

        submission_progress['is_complete'] = True
        submission_progress['is_running'] = False
        submission_progress['result'] = result

        # Save failed submission to history as well
        history_manager = SubmissionHistory()
        history_manager.add_submission_record(
            form_id=form_id,
            form_url=form_url,
            form_title=form_title,
            count=count,
            delay_min=delay_range[0],
            delay_max=delay_range[1],
            max_workers=max_workers,
            result=result
        )
