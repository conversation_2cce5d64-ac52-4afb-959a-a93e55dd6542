"""
Base AI Provider Module for Google Form AutoFill

This module defines the base abstract class for AI providers.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union


class AIProvider(ABC):
    """Abstract base class for AI providers"""

    @abstractmethod
    def __init__(self, api_keys: Union[str, List[str]], model: str = None, key_strategy: str = "round_robin"):
        """
        Initialize the AI provider
        
        Args:
            api_keys: The API key(s) for the provider (single key or list of keys)
            model: The default model to use
            key_strategy: Strategy for key selection when multiple keys are provided
                         ("round_robin", "random", or "least_used")
        """
        pass
    
    @abstractmethod
    def generate_text(self, prompt: str, max_tokens: int = 1024,
                     temperature: float = 0.7, top_p: float = 0.95, top_k: int = 40) -> str:
        """
        Generate text using the provider's API
        
        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0-1.0)
            top_p: Controls diversity via nucleus sampling
            top_k: Controls diversity via vocabulary restriction
            
        Returns:
            Generated text from the model
        """
        pass
    
    @abstractmethod
    def batch_generate(self, prompts: List[str], max_tokens: int = 1024) -> List[str]:
        """
        Generate responses for multiple prompts
        
        Args:
            prompts: List of prompts to send to the model
            max_tokens: Maximum number of tokens to generate per prompt
            
        Returns:
            List of generated responses
        """
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get a list of available models from the provider
        
        Returns:
            List of model information dictionaries
        """
        pass
    
    @abstractmethod
    def set_model(self, model: str) -> bool:
        """
        Set the current model
        
        Args:
            model: The model identifier to use
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    def get_current_model(self) -> str:
        """
        Get the current model identifier
        
        Returns:
            The current model identifier
        """
        pass
