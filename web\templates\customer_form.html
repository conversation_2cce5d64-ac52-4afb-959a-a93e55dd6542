{% extends "base.html" %}

{% block title %}Customer Form Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    .question-card {
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        background: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
    }

    .question-card:hover {
        box-shadow: var(--shadow-lg);
        border-color: var(--border-light);
    }

    .question-card .card-body {
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
    }

    .question-card .card-header {
        background: var(--bg-tertiary) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color);
    }

    .slider-container {
        padding: 1rem 0;
    }

    .slider-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .percentage-display {
        font-weight: 600;
        min-width: 3rem;
        text-align: right;
        color: var(--text-primary);
        background: var(--accent-primary);
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-size: 0.875rem;
    }

    .option-row {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .option-label {
        flex: 1;
        margin-right: 1rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .option-slider {
        flex: 2;
    }

    .form-range {
        background: var(--bg-tertiary);
    }

    .form-range::-webkit-slider-thumb {
        background: var(--accent-primary);
    }

    .form-range::-moz-range-thumb {
        background: var(--accent-primary);
        border: none;
    }

    .example-textarea {
        margin-bottom: 1rem;
    }

    .total-percentage {
        font-weight: 600;
        font-size: 1.1rem;
        margin-top: 1rem;
        padding: 0.75rem;
        border-radius: var(--radius-sm);
        text-align: center;
        border: 2px solid;
    }

    .percentage-valid {
        background: rgba(0, 184, 148, 0.1);
        color: var(--accent-success);
        border-color: var(--accent-success);
    }

    .percentage-invalid {
        background: rgba(225, 112, 85, 0.1);
        color: var(--accent-danger);
        border-color: var(--accent-danger);
    }

    .form-section-title {
        margin: 2rem 0 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .text-response-config {
        color: var(--text-primary);
    }

    .text-response-config h6 {
        color: var(--text-primary) !important;
        margin-bottom: 0.75rem;
    }

    .text-response-config p {
        color: var(--text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>Customer Form Configuration
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ form_data.form_title }}</h5>
                        {% if form_data.form_description %}
                        <p>{{ form_data.form_description }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Form Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Instructions:</strong> Configure how you want the responses to be generated for this form. For multiple-choice questions, set the percentage distribution for each option. For text questions, provide example responses.
        </div>

        <form method="POST" id="customerConfigForm" class="needs-validation" novalidate>
            {{ form.csrf_token }}

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>General Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="total_responses" class="form-label">{{ form.total_responses.label }}</label>
                                {{ form.total_responses(class="form-control", id="total_responses", min=1, max=10000) }}
                                <div class="form-text">Specify how many total form submissions you need.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-section-title">Question Configuration</h4>

            {% for question in form_data.questions %}
            <div class="card question-card" id="question-{{ question.id }}">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                            {% if question.required %}Required{% else %}Optional{% endif %}
                        </span>
                        {{ question.title }}
                        <span class="badge bg-info ms-2">{{ question.type_name }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if question.description %}
                    <p class="text-muted mb-3">{{ question.description }}</p>
                    {% endif %}

                    {% if question.type in ['multiple_choice', 'dropdown', 'linear_scale', 'rating'] %}
                        <!-- Option-based questions (multiple choice, dropdown, linear scale, rating) -->
                        <div class="option-distribution">
                            <h6>Response Distribution</h6>
                            <p class="text-muted">Set the percentage of responses for each option. The total must equal 100%.</p>

                            <div class="option-sliders" data-question-id="{{ question.id }}">
                                {% for option in question.options %}
                                {% set question_id = question.id|string %}
                                {% set question_config = form_data.customer_config.questions.get(question_id, {}) if form_data.customer_config else {} %}
                                {% set option_percentage = question_config.options.get(option, 100 // question.options|length) if question_config.get('options') else 100 // question.options|length %}
                                <div class="option-row">
                                    <div class="option-label">{{ option }}</div>
                                    <div class="option-slider">
                                        <input type="range" class="form-range percentage-slider"
                                               name="question_{{ question.id }}_option_{{ option|replace(' ', '_') }}"
                                               min="0" max="100" value="{{ option_percentage }}"
                                               data-question-id="{{ question.id }}">
                                    </div>
                                    <div class="percentage-display">{{ option_percentage }}%</div>
                                </div>
                                {% endfor %}
                            </div>

                            <div class="total-percentage percentage-valid" id="total-percentage-{{ question.id }}">
                                Total: 100%
                            </div>
                        </div>
                    {% elif question.type == 'multiple_choice_grid' %}
                        <!-- Multiple Choice Grid questions -->
                        <div class="grid-config">
                            <h6>Multiple Choice Grid Configuration</h6>
                            <p class="text-muted">Configure combinations of row-column selections with different weights. Each row can have only one selection.</p>

                            <!-- Display available grid options -->
                            {% if question.grid_rows and question.grid_columns %}
                            <div class="mb-3">
                                <h6>Available Grid Options:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Row</th>
                                                {% for column in question.grid_columns %}
                                                <th class="text-center">{{ column }}</th>
                                                {% endfor %}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for row in question.grid_rows %}
                                            <tr>
                                                <td><strong>{{ row }}</strong></td>
                                                {% for column in question.grid_columns %}
                                                <td class="text-center">
                                                    <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                </td>
                                                {% endfor %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This question type requires advanced configuration. Please use the Wizard Interface for detailed setup.
                                <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-magic me-1"></i>Use Wizard
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    {% elif question.type == 'checkbox_grid' %}
                        <!-- Checkbox Grid questions -->
                        <div class="grid-config">
                            <h6>Checkbox Grid Configuration</h6>
                            <p class="text-muted">Configure combinations of row-column selections with different weights. Multiple selections per row are allowed.</p>

                            <!-- Display available grid options -->
                            {% if question.grid_rows and question.grid_columns %}
                            <div class="mb-3">
                                <h6>Available Grid Options:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Row</th>
                                                {% for column in question.grid_columns %}
                                                <th class="text-center">{{ column }}</th>
                                                {% endfor %}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for row in question.grid_rows %}
                                            <tr>
                                                <td><strong>{{ row }}</strong></td>
                                                {% for column in question.grid_columns %}
                                                <td class="text-center">
                                                    <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                </td>
                                                {% endfor %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This question type requires advanced configuration. Please use the Wizard Interface for detailed setup.
                                <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-magic me-1"></i>Use Wizard
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    {% elif question.type == 'checkboxes' %}
                        <!-- Checkbox questions -->
                        <div class="checkbox-config">
                            <h6>Checkbox Configuration</h6>
                            <p class="text-muted">Configure combinations of multiple selections with different weights.</p>

                            <!-- Display available options -->
                            <div class="mb-3">
                                <h6>Available Options:</h6>
                                <div class="row">
                                    {% for option in question.options %}
                                    <div class="col-md-6 mb-2">
                                        <span class="badge bg-light text-dark border">{{ option }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This question type requires advanced configuration. Please use the Wizard Interface for detailed setup.
                                <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-magic me-1"></i>Use Wizard
                                </a>
                            </div>
                        </div>
                    {% elif question.type == 'date' %}
                        <!-- Date questions -->
                        <div class="date-config">
                            <h6>Date Configuration</h6>
                            <p class="text-muted">Configure date ranges for random date generation.</p>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This question type requires advanced configuration. Please use the Wizard Interface for detailed setup.
                                <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-magic me-1"></i>Use Wizard
                                </a>
                            </div>
                        </div>
                    {% elif question.type == 'time' %}
                        <!-- Time questions -->
                        <div class="time-config">
                            <h6>Time Configuration</h6>
                            <p class="text-muted">Configure time ranges for random time generation.</p>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> This question type requires advanced configuration. Please use the Wizard Interface for detailed setup.
                                <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-magic me-1"></i>Use Wizard
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <!-- Text-based questions (short answer, paragraph, etc.) -->
                        <div class="text-response-config">
                            {% set question_id = question.id|string %}
                            {% set question_config = form_data.customer_config.questions.get(question_id, {}) if form_data.customer_config else {} %}

                            <h6>Response Guidelines</h6>

                            <div class="mb-3">
                                <label for="question_{{ question.id }}_description" class="form-label">Description/Requirements</label>
                                <textarea class="form-control" id="question_{{ question.id }}_description"
                                          name="question_{{ question.id }}_description" rows="2"
                                          placeholder="Describe what kind of responses you want for this question">{{ question_config.get('description', '') }}</textarea>
                                <div class="form-text">Provide guidelines for generating responses to this question.</div>
                            </div>

                            <h6>Example Responses</h6>
                            <p class="text-muted">Provide up to 3 example responses to guide the response generation.</p>

                            {% for i in range(1, 4) %}
                            {% set example_value = question_config.get('examples', [])[i-1] if question_config.get('examples') and (i-1) < question_config.get('examples')|length else '' %}
                            <div class="mb-3 example-textarea">
                                <label for="question_{{ question.id }}_example_{{ i }}" class="form-label">Example {{ i }}</label>
                                <textarea class="form-control" id="question_{{ question.id }}_example_{{ i }}"
                                          name="question_{{ question.id }}_example_{{ i }}" rows="2"
                                          placeholder="Example response {{ i }}">{{ example_value }}</textarea>
                            </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4 mb-5">
                <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-secondary me-md-2">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
                <button type="button" id="exportTextBtn" class="btn btn-info me-md-2">
                    <i class="fas fa-file-export me-1"></i>Export as Text
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>{{ form.submit.label }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize percentage sliders
        initializePercentageSliders();

        // Form validation
        validateForm();

        // Export to text functionality
        $('#exportTextBtn').on('click', function() {
            exportFormAsText();
        });

        // Function to export form data as text
        function exportFormAsText() {
            // Get form title
            const formTitle = $('h5:first').text().trim();

            // Get total responses
            const totalResponses = $('#total_responses').val();

            // Start building the text
            let fullText = `📋 Form: ${formTitle}\n🔢 Responses: ${totalResponses}\n\n`;

            // Process each question
            $('.question-card').each(function() {
                const questionTitle = $(this).find('.card-header h5').text().trim();
                const questionType = $(this).find('.badge.bg-info').text().trim();

                // Add question info
                fullText += `❓ Question: ${questionTitle.replace(/Required|Optional/g, '').trim()}\n`;

                // Process based on question type
                if (['Multiple Choice', 'Dropdown', 'Checkboxes', 'Linear Scale', 'Rating'].includes(questionType)) {
                    // For option-based questions, get percentages
                    fullText += '✅ Options: ';
                    const options = [];
                    $(this).find('.option-row').each(function() {
                        const optionText = $(this).find('.option-label').text().trim();
                        const percentage = $(this).find('.percentage-display').text().trim();
                        options.push(`${optionText}(${percentage})`);
                    });
                    fullText += options.join(', ') + '\n\n-------------------\n';
                } else {
                    // For text-based questions, get examples
                    const description = $(this).find('textarea[id$="_description"]').val();
                    if (description) {
                        fullText += `📝 Description: ${description}\n`;
                    }

                    // Get examples
                    const examples = [];
                    $(this).find('textarea[id$="_example_1"], textarea[id$="_example_2"], textarea[id$="_example_3"]').each(function() {
                        const example = $(this).val().trim();
                        if (example) {
                            examples.push(example);
                        }
                    });

                    if (examples.length > 0) {
                        fullText += `💬 Examples: ${examples.join(' | ')}\n\n-------------------\n`;
                    }
                }

                // We've already added separators in each condition
            });

            // Split text into chunks of 500 characters or less
            const messages = splitTextIntoMessages(fullText, 500);

            // Create a modal to display the text
            let messagesHtml = '';
            messages.forEach((message, index) => {
                messagesHtml += `
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <span><strong>📩 Message ${index + 1} of ${messages.length}</strong> (${message.length} characters)</span>
                            <button type="button" class="btn btn-sm btn-primary copy-message-btn" data-message-index="${index}">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <div class="card-body">
                            <textarea class="form-control message-textarea" style="font-size: 16px; line-height: 1.5;" rows="6" readonly>${message}</textarea>
                        </div>
                    </div>
                `;
            });

            const modal = `
                <div class="modal fade" id="exportTextModal" tabindex="-1" aria-labelledby="exportTextModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exportTextModalLabel">Export as Text (${messages.length} messages)</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <p><i class="fas fa-info-circle me-2"></i><strong>Shopee Chat Format</strong></p>
                                    <p>💬 Your text has been split into ${messages.length} messages to fit within Shopee's 500 character limit.</p>
                                    <p>📲 For mobile: Copy each message separately and send them in order.</p>
                                </div>
                                ${messagesHtml}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="copyAllBtn">
                                    <i class="fas fa-copy me-1"></i>Copy All Messages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove any existing modal
            $('#exportTextModal').remove();

            // Add the modal to the page
            $('body').append(modal);

            // Show the modal
            const modalElement = new bootstrap.Modal(document.getElementById('exportTextModal'));
            modalElement.show();

            // Add copy functionality for individual messages
            $('.copy-message-btn').on('click', function() {
                const index = $(this).data('message-index');
                const textarea = $(this).closest('.card').find('textarea')[0];
                textarea.select();
                document.execCommand('copy');

                // Show success message
                $(this).html('<i class="fas fa-check"></i> Copied!');
                setTimeout(() => {
                    $(this).html('<i class="fas fa-copy"></i> Copy');
                }, 2000);
            });

            // Add copy all functionality
            $('#copyAllBtn').on('click', function() {
                // Create a temporary textarea with all messages
                const allText = messages.join('\n\n--- NEXT MESSAGE ---\n\n');
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = allText;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Show success message
                $(this).html('<i class="fas fa-check me-1"></i>All Copied!');
                setTimeout(() => {
                    $(this).html('<i class="fas fa-copy me-1"></i>Copy All Messages');
                }, 2000);
            });
        }

        // Function to split text into messages of max length
        function splitTextIntoMessages(text, maxLength) {
            // If text is already short enough, return it as a single message
            if (text.length <= maxLength) {
                return [text];
            }

            const messages = [];
            let remainingText = text;

            while (remainingText.length > 0) {
                if (remainingText.length <= maxLength) {
                    // Add the remaining text as the last message
                    messages.push(remainingText);
                    break;
                }

                // Find a good breaking point (end of a line or paragraph)
                let breakPoint = findBreakPoint(remainingText, maxLength);

                // Extract the current message
                const currentMessage = remainingText.substring(0, breakPoint).trim();
                messages.push(currentMessage);

                // Update the remaining text
                remainingText = remainingText.substring(breakPoint).trim();
            }

            return messages;
        }

        // Function to find a good breaking point in text
        function findBreakPoint(text, maxLength) {
            // If text is shorter than maxLength, return its length
            if (text.length <= maxLength) {
                return text.length;
            }

            // Try to break at the separator line
            const separatorBreak = text.lastIndexOf('-------------------', maxLength);
            if (separatorBreak > maxLength / 2) {
                return separatorBreak + SEPARATOR_LENGTH; // Include the separator
            }

            // Try to break at a double newline (paragraph break)
            const paragraphBreak = text.lastIndexOf('\n\n', maxLength);
            if (paragraphBreak > maxLength / 2) {
                return paragraphBreak + 2; // Include the newlines
            }

            // Try to break at a single newline
            const lineBreak = text.lastIndexOf('\n', maxLength);
            if (lineBreak > maxLength / 2) {
                return lineBreak + 1; // Include the newline
            }

            // Try to break at a space
            const spaceBreak = text.lastIndexOf(' ', maxLength);
            if (spaceBreak > 0) {
                return spaceBreak + 1; // Include the space
            }

            // If no good breaking point, just break at maxLength
            return maxLength;
        }

        // Function to initialize percentage sliders
        function initializePercentageSliders() {
            $('.option-sliders').each(function() {
                const questionId = $(this).data('question-id');
                updateTotalPercentage(questionId);

                // Add event listeners to sliders
                $(this).find('.percentage-slider').on('input', function() {
                    const value = $(this).val();
                    $(this).closest('.option-row').find('.percentage-display').text(value + '%');
                    updateTotalPercentage(questionId);
                });
            });
        }

        // Function to update total percentage
        function updateTotalPercentage(questionId) {
            const sliders = $(`.percentage-slider[data-question-id="${questionId}"]`);
            let total = 0;

            sliders.each(function() {
                total += parseInt($(this).val());
            });

            const totalElement = $(`#total-percentage-${questionId}`);
            totalElement.text(`Total: ${total}%`);

            if (total === 100) {
                totalElement.removeClass('percentage-invalid').addClass('percentage-valid');
            } else {
                totalElement.removeClass('percentage-valid').addClass('percentage-invalid');
            }

            return total === 100;
        }

        // Function to validate form before submission
        function validateForm() {
            $('#customerConfigForm').on('submit', function(e) {
                let isValid = true;

                // Validate total responses
                const totalResponses = $('#total_responses').val();
                if (!totalResponses || totalResponses < 1) {
                    isValid = false;
                    $('#total_responses').addClass('is-invalid');
                } else {
                    $('#total_responses').removeClass('is-invalid');
                }

                // Validate percentage distributions
                $('.option-sliders').each(function() {
                    const questionId = $(this).data('question-id');
                    if (!updateTotalPercentage(questionId)) {
                        isValid = false;
                        $(`#question-${questionId}`).addClass('border-danger');
                    } else {
                        $(`#question-${questionId}`).removeClass('border-danger');
                    }
                });

                if (!isValid) {
                    e.preventDefault();

                    // Scroll to the first invalid element
                    const firstInvalid = $('.is-invalid, .border-danger').first();
                    if (firstInvalid.length) {
                        $('html, body').animate({
                            scrollTop: firstInvalid.offset().top - 100
                        }, 500);
                    }

                    // Show error message
                    showAlert('Please fix the errors before submitting.', 'danger');
                }
            });
        }

        // Function to show alert
        function showAlert(message, type) {
            const alert = `
                <div class="alert alert-${type} alert-dismissible fade show">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Insert alert after the first card
            $('.card:first').after(alert);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }
    });
</script>
{% endblock %}
