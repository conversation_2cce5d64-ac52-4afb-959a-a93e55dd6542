"""
Run Script for Google Form AutoFill Web Interface

This script runs the web interface for the Google Form AutoFill tool.
"""

import os
from web.app import app

if __name__ == '__main__':
    print("Starting Google Form AutoFill Web Interface...")
    # Run the application in debug mode if not in production
    debug = os.environ.get('FLASK_ENV', 'development') == 'development'
    print(f"Debug mode: {debug}")
    app.run(debug=debug, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
