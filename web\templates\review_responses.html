{% extends "base.html" %}

{% block title %}Review Responses - Google Form AutoFill{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Review Responses for {{ form_data.form_title }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p>
                            <strong>Form ID:</strong> {{ form_id }}<br>
                            <strong>Questions:</strong> {{ form_data.questions|length }}<br>
                            <strong>Responses Prepared:</strong> {{ form_data.responses|length }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="btn-group">
                            <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i>View Form
                            </a>
                            <a href="{{ url_for('generate_responses', form_id=form_id) }}" class="btn btn-success">
                                <i class="fas fa-magic me-1"></i>Generate More
                            </a>
                            <a href="{{ url_for('submit_form', form_id=form_id) }}" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Submit Form
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Responses by Question</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="responsesAccordion">
                    {% for question in form_data.questions %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ loop.index }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}">
                                <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                                    {% if question.required %}Required{% else %}Optional{% endif %}
                                </span>
                                {{ question.title }}
                                <span class="badge bg-info ms-2">{{ question.type_name }}</span>

                                {% set q_id = question.id %}
                                {% set responses = form_data.responses.get(q_id, []) %}
                                {% if not responses and q_id is not none %}
                                    {% set responses = form_data.responses.get((q_id|string), []) %}
                                {% endif %}
                                <span class="badge bg-success ms-2">{{ responses|length }} Responses</span>
                                {% if question.no_duplication %}
                                <span class="badge bg-warning ms-2" title="No Duplication Mode">
                                    <i class="fas fa-ban"></i> No Dup
                                </span>
                                {% endif %}
                            </button>
                        </h2>
                        <div id="collapse{{ loop.index }}" class="accordion-collapse collapse" data-bs-parent="#responsesAccordion">
                            <div class="accordion-body">
                                {% set question_index = loop.index %}
                                {% set q_id = question.id %}
                                {% set responses = form_data.responses.get(q_id, []) %}
                                {% if not responses and q_id is not none %}
                                    {% set responses = form_data.responses.get((q_id|string), []) %}
                                {% endif %}

                                <div class="mb-3 d-flex justify-content-between align-items-center">
                                    <div>
                                        <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addResponseModal{{ loop.index }}">
                                            <i class="fas fa-plus me-1"></i>Add Response
                                        </button>
                                        
                                        <!-- No Duplication Toggle for this question -->
                                        <div class="form-check form-switch d-inline-block ms-3">
                                            <input class="form-check-input question-no-duplication-toggle" 
                                                   type="checkbox" 
                                                   id="noDuplicationQuestion{{ question_index }}"
                                                   data-form-id="{{ form_id }}"
                                                   data-question-id="{{ question.id|string }}"
                                                   {% if question.no_duplication %}checked{% endif %}>
                                            <label class="form-check-label" for="noDuplicationQuestion{{ question_index }}">
                                                <i class="fas fa-ban me-1"></i>No Duplication
                                            </label>
                                        </div>
                                        <small class="text-muted ms-2">
                                            <i class="fas fa-info-circle" title="When enabled, each response for this question will only be used once"></i>
                                        </small>
                                    </div>

                                    {% if responses %}
                                    <!-- Batch Operations -->
                                    <div class="batch-operations d-none" id="batchOps{{ question_index }}">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-warning btn-sm batch-edit-weight" data-question="{{ question_index }}">
                                                <i class="fas fa-weight-hanging me-1"></i>Batch Edit Weight
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm batch-delete" data-question="{{ question_index }}">
                                                <i class="fas fa-trash me-1"></i>Batch Delete
                                            </button>
                                            <button type="button" class="btn btn-secondary btn-sm clear-selection" data-question="{{ question_index }}">
                                                <i class="fas fa-times me-1"></i>Clear Selection
                                            </button>
                                        </div>
                                        <span class="ms-2 text-white">
                                            <span class="selected-count" data-question="{{ question_index }}">0</span> selected
                                        </span>
                                    </div>
                                    {% endif %}
                                </div>

                                {% if responses %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="responseTable{{ question_index }}">
                                        <thead>
                                            <tr>
                                                <th width="50">
                                                    <div class="form-check">
                                                        <input class="form-check-input select-all" type="checkbox" id="selectAll{{ question_index }}" data-question="{{ question_index }}">
                                                        <label class="form-check-label" for="selectAll{{ question_index }}">
                                                            All
                                                        </label>
                                                    </div>
                                                </th>
                                                <th>#</th>
                                                <th>Response</th>
                                                <th id="weightHeader{{ question_index }}">Weight 
                                                    <small class="text-muted no-duplication-indicator d-none">(Ignored in No Duplication Mode)</small>
                                                </th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for response in responses %}
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input response-checkbox" type="checkbox"
                                                               id="response{{ question_index }}_{{ loop.index0 }}"
                                                               data-question="{{ question_index }}"
                                                               data-response-index="{{ loop.index0 }}"
                                                               data-question-id="{{ question.id|string }}">
                                                    </div>
                                                </td>
                                                <td>{{ loop.index }}</td>
                                                <td class="response-text">{{ response.text }}</td>
                                                <td class="response-weight">{{ response.weight }}</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="{{ url_for('edit_response', form_id=form_id, question_index=question_index, response_index=loop.index) }}" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteResponseModal{{ question_index }}_{{ loop.index }}">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Batch Delete Modal -->
                                <div class="modal fade" id="batchDeleteModal{{ question_index }}" tabindex="-1" data-bs-backdrop="static">
                                    <div class="modal-dialog modal-dialog-scrollable modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger text-white">
                                                <h5 class="modal-title">Batch Delete Responses</h5>
                                                <button type="button" class="btn-close btn-close-white modal-close-btn" data-question="{{ question_index }}"></button>
                                            </div>
                                            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                                                <p class="fw-bold text-danger">Are you sure you want to delete the selected responses?</p>
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <strong>Warning:</strong> This action cannot be undone.
                                                </div>
                                                <div class="mt-3">
                                                    <h6 class="text-muted">Selected Responses:</h6>
                                                    <div id="selectedResponsesList{{ question_index }}" class="mt-2" style="max-height: 40vh; overflow-y: auto;">
                                                        <!-- Selected responses will be listed here -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer bg-light sticky-bottom">
                                                <button type="button" class="btn btn-secondary modal-cancel-btn" data-question="{{ question_index }}">
                                                    <i class="fas fa-times me-1"></i>Cancel
                                                </button>
                                                <button type="button" class="btn btn-danger confirm-batch-delete" data-question="{{ question_index }}" data-question-id="{{ question.id|string }}">
                                                    <i class="fas fa-trash me-1"></i>Delete Selected
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Batch Edit Weight Modal -->
                                <div class="modal fade" id="batchEditWeightModal{{ question_index }}" tabindex="-1" data-bs-backdrop="static">
                                    <div class="modal-dialog modal-dialog-scrollable modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-warning text-dark">
                                                <h5 class="modal-title">Batch Edit Weight</h5>
                                                <button type="button" class="btn-close modal-close-btn" data-question="{{ question_index }}"></button>
                                            </div>
                                            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                                                <p class="fw-bold">Set new weight for all selected responses:</p>
                                                <div class="mb-3">
                                                    <label for="batchWeight{{ question_index }}" class="form-label">New Weight</label>
                                                    <input type="number" class="form-control" id="batchWeight{{ question_index }}" value="1" min="1" max="100">
                                                    <div class="form-text">Higher weight means these responses will be selected more often.</div>
                                                </div>
                                                <div class="mt-3">
                                                    <h6 class="text-muted">Selected Responses:</h6>
                                                    <div id="selectedResponsesListWeight{{ question_index }}" class="mt-2" style="max-height: 40vh; overflow-y: auto;">
                                                        <!-- Selected responses will be listed here -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer bg-light sticky-bottom">
                                                <button type="button" class="btn btn-secondary modal-cancel-btn" data-question="{{ question_index }}">
                                                    <i class="fas fa-times me-1"></i>Cancel
                                                </button>
                                                <button type="button" class="btn btn-warning confirm-batch-weight" data-question="{{ question_index }}" data-question-id="{{ question.id|string }}">
                                                    <i class="fas fa-save me-1"></i>Update Weight
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>No responses have been generated for this question yet.
                                </div>
                                {% endif %}

                                <!-- Add Response Modal -->
                                <div class="modal fade" id="addResponseModal{{ loop.index }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Add Response</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="addResponseForm{{ loop.index }}" method="POST" action="{{ url_for('api_add_response', form_id=form_id, question_id=question.id|string) }}">
                                                    <div class="mb-3">
                                                        <label for="responseText{{ loop.index }}" class="form-label">Response Text</label>
                                                        <textarea id="responseText{{ loop.index }}" name="text" class="form-control" rows="3" required></textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="responseWeight{{ loop.index }}" class="form-label">Weight</label>
                                                        <input type="number" id="responseWeight{{ loop.index }}" name="weight" class="form-control" value="1" min="1" required>
                                                        <div class="form-text">Higher weight means this response will be selected more often.</div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-success add-response-btn" data-form-id="{{ loop.index }}">Add Response</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Response Modals -->
                                {% for response in responses %}
                                <div class="modal fade" id="deleteResponseModal{{ question_index }}_{{ loop.index }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Confirm Deletion</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this response?</p>
                                                <p><strong>Response:</strong> {{ response.text }}</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="{{ url_for('api_delete_response', form_id=form_id, question_id=question.id|string, response_index=loop.index0) }}" class="btn btn-danger delete-response-btn" data-question-id="{{ question.id|string }}" data-response-index="{{ loop.index0 }}">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize modal management
        AppUtils.safelyManageModals();

        // Prevent multiple event bindings
        const pageInitialized = 'reviewResponsesPageInitialized';
        if (window[pageInitialized]) {
            return;
        }
        window[pageInitialized] = true;

        // Modal cleanup
        $(document).off('hidden.bs.modal.reviewResponses').on('hidden.bs.modal.reviewResponses', '.modal', function() {
            const modal = $(this);
            const modalId = modal.attr('id');

            // Reset modal state when hidden
            if (modalId && modalId.startsWith('batchDeleteModal')) {
                modal.find('[id^="selectedResponsesList"]').empty();
            } else if (modalId && modalId.startsWith('batchEditWeightModal')) {
                modal.find('[id^="selectedResponsesListWeight"]').empty();
                modal.find('input[type="number"]').val(1);
            }

            // Ensure backdrop is removed
            setTimeout(() => {
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open').css('padding-right', '');
            }, 100);
        });

        // Modal close handlers
        $(document).off('click', '.modal-close-btn, .modal-cancel-btn').on('click', '.modal-close-btn, .modal-cancel-btn', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const button = $(this);
            const questionIndex = button.data('question');

            if (!questionIndex) {
                return;
            }

            // Hide modals
            const deleteModal = $(`#batchDeleteModal${questionIndex}`);
            const weightModal = $(`#batchEditWeightModal${questionIndex}`);

            if (deleteModal.hasClass('show')) {
                AppUtils.safelyHideModal(`batchDeleteModal${questionIndex}`);
            }
            if (weightModal.hasClass('show')) {
                AppUtils.safelyHideModal(`batchEditWeightModal${questionIndex}`);
            }
        });

        // Check if any checkboxes are selected and show/hide batch operations
        function updateBatchOperations(questionIndex) {
            const checkboxes = $(`.response-checkbox[data-question="${questionIndex}"]`);
            const checkedBoxes = checkboxes.filter(':checked');
            const batchOps = $(`#batchOps${questionIndex}`);
            const selectedCount = $(`span.selected-count[data-question="${questionIndex}"]`);

            if (checkedBoxes.length > 0) {
                batchOps.removeClass('d-none');
                selectedCount.text(checkedBoxes.length);
            } else {
                batchOps.addClass('d-none');
                selectedCount.text('0');
            }
        }

        // Select all functionality
        $('.select-all').off('change.reviewResponses').on('change.reviewResponses', function() {
            const questionIndex = $(this).data('question');
            const isChecked = $(this).is(':checked');

            $(`.response-checkbox[data-question="${questionIndex}"]`).prop('checked', isChecked);
            updateBatchOperations(questionIndex);
        });

        // Individual checkbox change
        $('.response-checkbox').off('change.reviewResponses').on('change.reviewResponses', function() {
            const questionIndex = $(this).data('question');

            const allCheckboxes = $(`.response-checkbox[data-question="${questionIndex}"]`);
            const checkedBoxes = allCheckboxes.filter(':checked');
            const selectAllBox = $(`.select-all[data-question="${questionIndex}"]`);

            // Update select all checkbox state
            if (checkedBoxes.length === allCheckboxes.length) {
                selectAllBox.prop('checked', true).prop('indeterminate', false);
            } else if (checkedBoxes.length > 0) {
                selectAllBox.prop('checked', false).prop('indeterminate', true);
            } else {
                selectAllBox.prop('checked', false).prop('indeterminate', false);
            }

            updateBatchOperations(questionIndex);
        });

        // Clear selection
        $('.clear-selection').off('click.reviewResponses').on('click.reviewResponses', function() {
            const questionIndex = $(this).data('question');

            $(`.response-checkbox[data-question="${questionIndex}"]`).prop('checked', false);
            $(`.select-all[data-question="${questionIndex}"]`).prop('checked', false).prop('indeterminate', false);
            updateBatchOperations(questionIndex);
        });

        // Show selected responses in delete modal
        $('.batch-delete').off('click.reviewResponses').on('click.reviewResponses', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const questionIndex = $(this).data('question');
            const checkedBoxes = $(`.response-checkbox[data-question="${questionIndex}"]:checked`);

            if (checkedBoxes.length === 0) {
                alert('Please select at least one response to delete.');
                return;
            }

            const responsesList = $(`#selectedResponsesList${questionIndex}`);
            responsesList.empty();

            // Create a scrollable list with better formatting
            const container = $('<div class="responses-preview"></div>');

            checkedBoxes.each(function(index) {
                const row = $(this).closest('tr');
                const text = row.find('.response-text').text();
                const weight = row.find('.response-weight').text();

                const item = $(`
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <span class="badge bg-secondary me-2">#${index + 1}</span>
                                    <strong>Response:</strong> ${text}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Weight: ${weight}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
                container.append(item);
            });

            responsesList.append(container);

            // Show modal with delay
            setTimeout(() => {
                AppUtils.safelyShowModal(`batchDeleteModal${questionIndex}`);
            }, 50);
        });

        // Show selected responses in weight edit modal
        $('.batch-edit-weight').off('click.reviewResponses').on('click.reviewResponses', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const questionIndex = $(this).data('question');
            const checkedBoxes = $(`.response-checkbox[data-question="${questionIndex}"]:checked`);

            if (checkedBoxes.length === 0) {
                alert('Please select at least one response to edit.');
                return;
            }

            const responsesList = $(`#selectedResponsesListWeight${questionIndex}`);
            responsesList.empty();

            // Create a scrollable list with better formatting
            const container = $('<div class="responses-preview"></div>');

            checkedBoxes.each(function(index) {
                const row = $(this).closest('tr');
                const text = row.find('.response-text').text();
                const weight = row.find('.response-weight').text();

                const item = $(`
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <span class="badge bg-secondary me-2">#${index + 1}</span>
                                    <strong>Response:</strong> ${text}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Current Weight: ${weight}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
                container.append(item);
            });

            responsesList.append(container);

            // Show modal with delay
            setTimeout(() => {
                AppUtils.safelyShowModal(`batchEditWeightModal${questionIndex}`);
            }, 50);
        });

        // Confirm batch delete
        $('.confirm-batch-delete').off('click.reviewResponses').on('click.reviewResponses', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const questionIndex = $(this).data('question');
            const questionId = $(this).data('question-id');
            const checkedBoxes = $(`.response-checkbox[data-question="${questionIndex}"]:checked`);

            if (checkedBoxes.length === 0) {
                alert('No responses selected.');
                return;
            }

            const responseIndices = [];
            checkedBoxes.each(function() {
                responseIndices.push($(this).data('response-index'));
            });

            const button = $(this);
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin me-1"></i>Deleting...');
            button.prop('disabled', true);

            // Disable all modal interaction
            const modal = $(`#batchDeleteModal${questionIndex}`);
            modal.find('.modal-close-btn, .modal-cancel-btn').prop('disabled', true);

            $.ajax({
                url: '/api/batch_delete_responses',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    form_id: '{{ form_id }}',
                    question_id: questionId,
                    response_indices: responseIndices
                }),
                success: function(response) {
                    // Close modal immediately
                    AppUtils.safelyHideModal(`batchDeleteModal${questionIndex}`);

                    // Reload page after a short delay
                    setTimeout(function() {
                        location.reload();
                    }, 200);
                },
                error: function(xhr) {
                    button.html(originalText);
                    button.prop('disabled', false);
                    modal.find('.modal-close-btn, .modal-cancel-btn').prop('disabled', false);

                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Unknown error occurred';
                    alert('Error: ' + errorMsg);
                }
            });
        });

        // Confirm batch weight edit
        $('.confirm-batch-weight').off('click.reviewResponses').on('click.reviewResponses', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const questionIndex = $(this).data('question');
            const questionId = $(this).data('question-id');
            const newWeight = parseInt($(`#batchWeight${questionIndex}`).val());
            const checkedBoxes = $(`.response-checkbox[data-question="${questionIndex}"]:checked`);

            if (checkedBoxes.length === 0) {
                alert('No responses selected.');
                return;
            }

            if (isNaN(newWeight) || newWeight < 1) {
                alert('Please enter a valid weight (minimum 1).');
                return;
            }

            const responseIndices = [];
            checkedBoxes.each(function() {
                responseIndices.push($(this).data('response-index'));
            });

            const button = $(this);
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin me-1"></i>Updating...');
            button.prop('disabled', true);

            // Disable all modal interaction
            const modal = $(`#batchEditWeightModal${questionIndex}`);
            modal.find('.modal-close-btn, .modal-cancel-btn').prop('disabled', true);

            $.ajax({
                url: '/api/batch_update_weight',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    form_id: '{{ form_id }}',
                    question_id: questionId,
                    response_indices: responseIndices,
                    weight: newWeight
                }),
                success: function(response) {
                    // Close modal immediately
                    AppUtils.safelyHideModal(`batchEditWeightModal${questionIndex}`);

                    // Reload page after a short delay
                    setTimeout(function() {
                        location.reload();
                    }, 200);
                },
                error: function(xhr) {
                    button.html(originalText);
                    button.prop('disabled', false);
                    modal.find('.modal-close-btn, .modal-cancel-btn').prop('disabled', false);

                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Unknown error occurred';
                    alert('Error: ' + errorMsg);
                }
            });
        });

        // Add response
        $('.add-response-btn').off('click.reviewResponses').on('click.reviewResponses', function() {
            const formId = $(this).data('form-id');

            const form = $(`#addResponseForm${formId}`);
            const url = form.attr('action');

            $.ajax({
                url: url,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    text: $(`#responseText${formId}`).val(),
                    weight: parseInt($(`#responseWeight${formId}`).val())
                }),
                success: function(response) {
                    location.reload();
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        });

        // Delete response
        $('.delete-response-btn').off('click.reviewResponses').on('click.reviewResponses', function(e) {
            e.preventDefault();

            const url = $(this).attr('href');

            $.ajax({
                url: url,
                type: 'DELETE',
                success: function(response) {
                    location.reload();
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        });

        // Per-Question No Duplication Toggle Functionality
        function updateQuestionNoDuplicationUI(questionId, isEnabled) {
            // Find the question's table
            const questionTable = $(`[data-question-id="${questionId}"]`).closest('.accordion-body').find('table');
            
            if (questionTable.length > 0) {
                const weightHeader = questionTable.find('th:contains("Weight")');
                const weightCells = questionTable.find('td.response-weight');
                
                if (isEnabled) {
                    // Add visual indicator that weights are ignored
                    weightHeader.addClass('text-muted');
                    weightCells.addClass('text-muted');
                    
                    // Update the header badge in the accordion
                    const accordionButton = $(`[data-question-id="${questionId}"]`)
                        .closest('.accordion-item')
                        .find('.accordion-button');
                    
                    if (!accordionButton.find('.badge.bg-warning:contains("No Dup")').length) {
                        accordionButton.append(`
                            <span class="badge bg-warning ms-2" title="No Duplication Mode">
                                <i class="fas fa-ban"></i> No Dup
                            </span>
                        `);
                    }
                } else {
                    // Remove visual indicators
                    weightHeader.removeClass('text-muted');
                    weightCells.removeClass('text-muted');
                    
                    // Remove the badge
                    const accordionButton = $(`[data-question-id="${questionId}"]`)
                        .closest('.accordion-item')
                        .find('.accordion-button');
                    accordionButton.find('.badge.bg-warning:contains("No Dup")').remove();
                }
            }
        }

        // Handle per-question no duplication toggle change
        $('.question-no-duplication-toggle').off('change.questionNoDuplication').on('change.questionNoDuplication', function() {
            const isEnabled = $(this).is(':checked');
            const formId = $(this).data('form-id');
            const questionId = $(this).data('question-id');
            const toggle = $(this);

            // Update UI immediately
            updateQuestionNoDuplicationUI(questionId, isEnabled);

            // Save setting to backend
            $.ajax({
                url: '/api/update_question_no_duplication',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    form_id: formId,
                    question_id: questionId,
                    no_duplication: isEnabled
                }),
                success: function(response) {
                    if (response.success) {
                        // Show feedback
                        const questionTitle = toggle.closest('.accordion-body')
                            .closest('.accordion-item')
                            .find('.accordion-button')
                            .text()
                            .trim()
                            .split('\n')[1]; // Get the question title
                        
                        const message = isEnabled ? 
                            `No Duplication enabled for "${questionTitle}". Each response will only be used once.` :
                            `No Duplication disabled for "${questionTitle}". Weight-based selection restored.`;
                        
                        // Create a temporary notification
                        const notification = $(`
                            <div class="alert alert-info alert-dismissible fade show position-fixed" 
                                 style="top: 20px; right: 20px; z-index: 1050; max-width: 400px;">
                                <i class="fas fa-info-circle me-2"></i>${message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        `);
                        
                        $('body').append(notification);
                        
                        // Auto-dismiss after 3 seconds
                        setTimeout(() => {
                            notification.alert('close');
                        }, 3000);
                    }
                },
                error: function(xhr) {
                    // Revert toggle state on error
                    toggle.prop('checked', !isEnabled);
                    updateQuestionNoDuplicationUI(questionId, !isEnabled);
                    
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Failed to update setting';
                    alert('Error: ' + errorMsg);
                }
            });
        });

        // Initialize UI for questions with no duplication enabled
        $('.question-no-duplication-toggle:checked').each(function() {
            const questionId = $(this).data('question-id');
            updateQuestionNoDuplicationUI(questionId, true);
        });
    });
</script>
{% endblock %}