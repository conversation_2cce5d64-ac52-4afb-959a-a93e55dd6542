"""
OpenRouter AI Provider for Google Form AutoFill

This module provides integration with OpenRouter API for generating
intelligent responses for form questions using various AI models.
"""

import json
import time
import logging
import requests
from typing import List, Dict, Any, Optional, Union

from .base_provider import <PERSON><PERSON>rovider
from .key_manager import KeyManager
from .model_manager import ModelManager
from .provider_factory import ProviderFactory

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OpenRouterKeyManager(KeyManager):
    """Key manager for OpenRouter API keys"""
    pass  # Inherits all functionality from base KeyManager


class OpenRouterProvider(AIProvider):
    """Provider for interacting with OpenRouter API"""

    API_BASE = "https://openrouter.ai/api/v1"
    DEFAULT_MODEL = "openai/gpt-3.5-turbo"

    def __init__(self, api_keys: Union[str, List[str]], model: str = None, key_strategy: str = "round_robin"):
        """
        Initialize the OpenRouter provider

        Args:
            api_keys: The API key(s) for OpenRouter (single key or list of keys)
            model: The model to use (default: openai/gpt-3.5-turbo)
            key_strategy: Strategy for key selection when multiple keys are provided
                         ("round_robin", "random", or "least_used")
        """
        self.model = model or self.DEFAULT_MODEL
        self.model_manager = ModelManager()

        # Initialize key manager if multiple keys are provided
        if isinstance(api_keys, list):
            self.key_manager = OpenRouterKeyManager(api_keys, strategy=key_strategy)
            self.api_key = self.key_manager.get_next_key()  # Initial key
            self.multiple_keys = True
            logger.info(f"Initialized OpenRouter provider with {len(api_keys)} API keys using {key_strategy} strategy")
        else:
            self.api_key = api_keys
            self.key_manager = None
            self.multiple_keys = False
            logger.info("Initialized OpenRouter provider with a single API key")

    def generate_text(self, prompt: str, max_tokens: int = 1024,
                     temperature: float = 0.7, top_p: float = 0.95, top_k: int = 40) -> str:
        """
        Generate text using OpenRouter API with automatic key rotation and fallback

        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0-1.0)
            top_p: Controls diversity via nucleus sampling
            top_k: Controls diversity via vocabulary restriction

        Returns:
            Generated text from the model

        Raises:
            Exception: If all API keys fail
        """
        # Prepare request data
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k
        }

        # If using multiple keys, try each key until one works
        if self.multiple_keys:
            # Try with current key first
            try:
                response = requests.post(
                    f"{self.API_BASE}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=30
                )
                response.raise_for_status()
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content", "")

            except Exception as e:
                # Current key failed, mark it as failed
                logger.warning(f"API key failed: {str(e)}")
                self.key_manager.mark_key_failed(self.api_key)

                # Try with other keys
                while True:
                    try:
                        # Get next key
                        self.api_key = self.key_manager.get_next_key()
                        logger.info(f"Switching to next API key")

                        # Update headers with new key
                        headers["Authorization"] = f"Bearer {self.api_key}"

                        # Try request with new key
                        response = requests.post(
                            f"{self.API_BASE}/chat/completions",
                            headers=headers,
                            json=data,
                            timeout=30
                        )
                        response.raise_for_status()
                        result = response.json()
                        return result.get("choices", [{}])[0].get("message", {}).get("content", "")

                    except Exception as e:
                        # Mark this key as failed too
                        logger.warning(f"API key failed: {str(e)}")
                        self.key_manager.mark_key_failed(self.api_key)

                        # If all keys have been tried and failed, raise exception
                        if len(self.key_manager.failed_keys) >= len(self.key_manager.api_keys):
                            raise Exception("All API keys have failed") from e
        else:
            # Single key mode - just try once
            response = requests.post(
                f"{self.API_BASE}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result.get("choices", [{}])[0].get("message", {}).get("content", "")

    def batch_generate(self, prompts: List[str], max_tokens: int = 1024) -> List[str]:
        """
        Generate responses for multiple prompts

        Args:
            prompts: List of prompts to send to the model
            max_tokens: Maximum number of tokens to generate per prompt

        Returns:
            List of generated responses
        """
        results = []
        for prompt in prompts:
            results.append(self.generate_text(prompt, max_tokens))
        return results

    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get a list of available models from OpenRouter

        Returns:
            List of model information dictionaries
        """
        # Check cache first
        cached_models = self.model_manager.get_models("openrouter")
        if cached_models:
            return cached_models

        try:
            # Prepare request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Get available models
            response = requests.get(
                f"{self.API_BASE}/models",
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            # Format model information
            model_info = []
            for model in result.get("data", []):
                # Extract modalities from architecture if available
                input_modalities = []
                output_modalities = []
                if architecture := model.get("architecture", {}):
                    input_modalities = architecture.get("input_modalities", [])
                    output_modalities = architecture.get("output_modalities", [])

                # Extract pricing information
                pricing = model.get("pricing", {})

                info = {
                    "id": model.get("id"),
                    "name": model.get("name", model.get("id", "")),
                    "description": model.get("description", ""),
                    "context_length": model.get("context_length", 0),
                    "created": model.get("created"),
                    "input_modalities": input_modalities,
                    "output_modalities": output_modalities,
                    "pricing": {
                        "prompt": pricing.get("prompt", 0),
                        "completion": pricing.get("completion", 0),
                        "image": pricing.get("image", 0),
                        "request": pricing.get("request", 0)
                    },
                    "supported_parameters": model.get("supported_parameters", [])
                }
                model_info.append(info)

            # Save to cache
            self.model_manager.save_models("openrouter", model_info)

            return model_info
        except Exception as e:
            logger.error(f"Error fetching OpenRouter models: {str(e)}")
            return []

    def set_model(self, model: str) -> bool:
        """
        Set the current model

        Args:
            model: The model identifier to use

        Returns:
            True if successful, False otherwise
        """
        try:
            # Verify model exists
            models = self.get_available_models()
            model_ids = [m["id"] for m in models]

            # If model is not in the list but seems valid, allow it anyway
            # (OpenRouter supports many models that might not be in their list)
            if model not in model_ids and "/" not in model:
                logger.warning(f"Model {model} not found in available models, but allowing it")

            # Set the model
            self.model = model
            logger.info(f"Set OpenRouter model to: {model}")
            return True
        except Exception as e:
            logger.error(f"Error setting OpenRouter model: {str(e)}")
            return False

    def get_current_model(self) -> str:
        """
        Get the current model identifier

        Returns:
            The current model identifier
        """
        return self.model


# Register the provider with the factory
ProviderFactory.register_provider("openrouter", OpenRouterProvider)
