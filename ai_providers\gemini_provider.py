"""
Gemini AI Provider for Google Form AutoFill

This module provides integration with Google's Gemini AI model for generating
intelligent responses for form questions.
"""

import json
import re
import time
import logging
from typing import List, Dict, Any, Optional, Union

import google.generativeai as genai

from .base_provider import AIProvider
from .key_manager import KeyManager
from .model_manager import ModelManager
from .provider_factory import ProviderFactory

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GeminiKeyManager(KeyManager):
    """Key manager for Gemini API keys"""
    pass  # Inherits all functionality from base KeyManager


class GeminiProvider(AIProvider):
    """Provider for interacting with Google's Gemini API"""
    
    DEFAULT_MODEL = "gemini-2.0-flash-lite"
    
    def __init__(self, api_keys: Union[str, List[str]], model: str = None, key_strategy: str = "round_robin"):
        """
        Initialize the Gemini provider
        
        Args:
            api_keys: The API key(s) for Gemini (single key or list of keys)
            model: The model to use (default: gemini-2.0-flash-lite)
            key_strategy: Strategy for key selection when multiple keys are provided
                         ("round_robin", "random", or "least_used")
        """
        self.model = model or self.DEFAULT_MODEL
        self.model_manager = ModelManager()
        
        # Initialize key manager if multiple keys are provided
        if isinstance(api_keys, list):
            self.key_manager = GeminiKeyManager(api_keys, strategy=key_strategy)
            self.api_key = self.key_manager.get_next_key()  # Initial key
            self.multiple_keys = True
            logger.info(f"Initialized Gemini provider with {len(api_keys)} API keys using {key_strategy} strategy")
        else:
            self.api_key = api_keys
            self.key_manager = None
            self.multiple_keys = False
            logger.info("Initialized Gemini provider with a single API key")
            
        # Configure the Gemini API with initial key
        genai.configure(api_key=self.api_key)
        
    def generate_text(self, prompt: str, max_tokens: int = 1024,
                     temperature: float = 0.7, top_p: float = 0.95, top_k: int = 40) -> str:
        """
        Generate text using Gemini API with automatic key rotation and fallback
        
        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0-1.0)
            top_p: Controls diversity via nucleus sampling
            top_k: Controls diversity via vocabulary restriction
            
        Returns:
            Generated text from the model
            
        Raises:
            Exception: If all API keys fail
        """
        # Configure generation parameters
        generation_config = {
            "max_output_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k
        }
        
        # If using multiple keys, try each key until one works
        if self.multiple_keys:
            # Try with current key first
            try:
                # Configure the API with current key
                genai.configure(api_key=self.api_key)
                
                # Get the model
                model = genai.GenerativeModel(model_name=self.model,
                                           generation_config=generation_config)
                
                # Generate content
                response = model.generate_content(prompt)
                return response.text
                
            except Exception as e:
                # Current key failed, mark it as failed
                logger.warning(f"API key failed: {str(e)}")
                self.key_manager.mark_key_failed(self.api_key)
                
                # Try with other keys
                while True:
                    try:
                        # Get next key
                        self.api_key = self.key_manager.get_next_key()
                        logger.info(f"Switching to next API key")
                        
                        # Configure the API with new key
                        genai.configure(api_key=self.api_key)
                        
                        # Get the model
                        model = genai.GenerativeModel(model_name=self.model,
                                                   generation_config=generation_config)
                        
                        # Generate content
                        response = model.generate_content(prompt)
                        return response.text
                        
                    except Exception as e:
                        # Mark this key as failed too
                        logger.warning(f"API key failed: {str(e)}")
                        self.key_manager.mark_key_failed(self.api_key)
                        
                        # If all keys have been tried and failed, raise exception
                        if len(self.key_manager.failed_keys) >= len(self.key_manager.api_keys):
                            raise Exception("All API keys have failed") from e
        else:
            # Single key mode - just try once
            # Get the model
            model = genai.GenerativeModel(model_name=self.model,
                                         generation_config=generation_config)
            
            # Generate content
            response = model.generate_content(prompt)
            return response.text
            
    def batch_generate(self, prompts: List[str], max_tokens: int = 1024) -> List[str]:
        """
        Generate responses for multiple prompts
        
        Args:
            prompts: List of prompts to send to the model
            max_tokens: Maximum number of tokens to generate per prompt
            
        Returns:
            List of generated responses
        """
        results = []
        for prompt in prompts:
            results.append(self.generate_text(prompt, max_tokens))
        return results
        
    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get a list of available models from Gemini
        
        Returns:
            List of model information dictionaries
        """
        # Check cache first
        cached_models = self.model_manager.get_models("gemini")
        if cached_models:
            return cached_models
            
        try:
            # Configure the API with current key
            genai.configure(api_key=self.api_key)
            
            # Get available models
            models = genai.list_models()
            
            # Format model information
            model_info = []
            for model in models:
                if hasattr(model, "name") and "gemini" in model.name.lower():
                    info = {
                        "id": model.name,
                        "name": model.name.split("/")[-1],
                        "description": getattr(model, "description", ""),
                        "input_token_limit": getattr(model, "input_token_limit", 0),
                        "output_token_limit": getattr(model, "output_token_limit", 0),
                        "supported_generation_methods": getattr(model, "supported_generation_methods", []),
                    }
                    model_info.append(info)
                    
            # Save to cache
            self.model_manager.save_models("gemini", model_info)
            
            return model_info
        except Exception as e:
            logger.error(f"Error fetching Gemini models: {str(e)}")
            return []
            
    def set_model(self, model: str) -> bool:
        """
        Set the current model
        
        Args:
            model: The model identifier to use
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Verify model exists
            models = self.get_available_models()
            model_ids = [m["id"] for m in models]
            
            # If model is just the name part, try to find the full ID
            if model not in model_ids:
                for m in models:
                    if m["name"] == model:
                        model = m["id"]
                        break
                        
            # Set the model
            self.model = model
            logger.info(f"Set Gemini model to: {model}")
            return True
        except Exception as e:
            logger.error(f"Error setting Gemini model: {str(e)}")
            return False
            
    def get_current_model(self) -> str:
        """
        Get the current model identifier
        
        Returns:
            The current model identifier
        """
        return self.model


# Register the provider with the factory
ProviderFactory.register_provider("gemini", GeminiProvider)
