"""
Submission Queue Module for Google Form AutoFill

This module provides functionality for queuing and processing
customer submission requests in a priority-based manner.
"""

import json
import os
import time
import threading
import heapq
from typing import Dict, List, Any, Optional, Callable, Tuple
from datetime import datetime

from customer_request import CustomerRequest, CustomerRequestManager
from submission_manager import SubmissionManager
from submission_history import SubmissionHistory


class SubmissionQueue:
    """Class for managing the submission queue"""
    
    def __init__(self, storage_dir: str = "responses/submission_queue"):
        """
        Initialize the submission queue
        
        Args:
            storage_dir: Directory to store queue data
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
        self.request_manager = CustomerRequestManager()
        self.submission_manager = SubmissionManager()
        self.history_manager = SubmissionHistory()
        
        self.queue = []  # Priority queue [(priority, timestamp, request_id), ...]
        self.queue_lock = threading.Lock()
        self.processing = False
        self.processing_thread = None
        self._stop_processing = threading.Event()
        
        # Load queue from storage
        self._load_queue()
        
    def _get_queue_filename(self) -> str:
        """Get the filename for the queue data"""
        return os.path.join(self.storage_dir, "submission_queue.json")
        
    def _load_queue(self) -> None:
        """Load the queue from storage"""
        filename = self._get_queue_filename()
        if not os.path.exists(filename):
            self.queue = []
            return
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                queue_data = json.load(f)
                
            # Convert to priority queue format
            self.queue = []
            for item in queue_data:
                # Priority is negated to make heapq work as a max-heap (higher priority first)
                priority = -item.get("priority", 1)  # Default to lowest priority if not specified
                timestamp = item.get("timestamp", 0)
                request_id = item.get("request_id")
                
                if request_id:
                    heapq.heappush(self.queue, (priority, timestamp, request_id))
        except (json.JSONDecodeError, FileNotFoundError):
            self.queue = []
            
    def _save_queue(self) -> None:
        """Save the queue to storage"""
        filename = self._get_queue_filename()
        
        # Convert priority queue to list of dictionaries for storage
        queue_data = []
        for priority, timestamp, request_id in self.queue:
            queue_data.append({
                "priority": -priority,  # Convert back to positive priority
                "timestamp": timestamp,
                "request_id": request_id
            })
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(queue_data, f, ensure_ascii=False, indent=2)
            
    def add_to_queue(self, request_id: str, priority: Optional[int] = None) -> bool:
        """
        Add a request to the queue
        
        Args:
            request_id: The ID of the request to add
            priority: Optional priority override (1-5, with 5 being highest)
            
        Returns:
            True if successful, False otherwise
        """
        request = self.request_manager.get_request(request_id)
        if not request:
            return False
            
        # Use provided priority or request's priority
        if priority is not None:
            request_priority = min(max(priority, 1), 5)  # Ensure priority is between 1-5
        else:
            request_priority = request.priority
            
        # Update request status to pending if it's not already
        if request.status != "pending":
            request.status = "pending"
            self.request_manager.update_request(request)
            
        with self.queue_lock:
            # Check if request is already in queue
            for _, _, queued_id in self.queue:
                if queued_id == request_id:
                    return True  # Already in queue
                    
            # Add to queue with current timestamp
            # Priority is negated to make heapq work as a max-heap (higher priority first)
            timestamp = time.time()
            heapq.heappush(self.queue, (-request_priority, timestamp, request_id))
            self._save_queue()
            
        return True
        
    def remove_from_queue(self, request_id: str) -> bool:
        """
        Remove a request from the queue
        
        Args:
            request_id: The ID of the request to remove
            
        Returns:
            True if successful, False otherwise
        """
        with self.queue_lock:
            # Find and remove the request
            for i, (priority, timestamp, queued_id) in enumerate(self.queue):
                if queued_id == request_id:
                    # Remove from queue
                    self.queue.pop(i)
                    # Restore heap property
                    heapq.heapify(self.queue)
                    self._save_queue()
                    return True
                    
        return False
        
    def get_next_request(self) -> Optional[str]:
        """
        Get the next request ID from the queue without removing it
        
        Returns:
            The next request ID, or None if queue is empty
        """
        with self.queue_lock:
            if not self.queue:
                return None
                
            # Get highest priority request (lowest tuple value in min-heap)
            priority, timestamp, request_id = self.queue[0]
            return request_id
            
    def pop_next_request(self) -> Optional[str]:
        """
        Remove and return the next request ID from the queue
        
        Returns:
            The next request ID, or None if queue is empty
        """
        with self.queue_lock:
            if not self.queue:
                return None
                
            # Pop highest priority request
            priority, timestamp, request_id = heapq.heappop(self.queue)
            self._save_queue()
            return request_id
            
    def get_queue_length(self) -> int:
        """
        Get the current length of the queue
        
        Returns:
            Number of requests in the queue
        """
        with self.queue_lock:
            return len(self.queue)
            
    def get_queue_items(self) -> List[Dict[str, Any]]:
        """
        Get all items in the queue with request details
        
        Returns:
            List of queue items with request details
        """
        with self.queue_lock:
            queue_items = []
            
            for priority, timestamp, request_id in self.queue:
                request = self.request_manager.get_request(request_id)
                if request:
                    queue_items.append({
                        "request_id": request_id,
                        "priority": -priority,  # Convert back to positive priority
                        "timestamp": timestamp,
                        "added_at": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
                        "customer_id": request.customer_id,
                        "form_title": request.form_title,
                        "submission_count": request.submission_count
                    })
                    
            return queue_items
            
    def clear_queue(self) -> int:
        """
        Clear the entire queue
        
        Returns:
            Number of items removed from the queue
        """
        with self.queue_lock:
            count = len(self.queue)
            self.queue = []
            self._save_queue()
            return count
            
    def start_processing(self, max_workers: int = 1, 
                        delay_range_ms: Tuple[int, int] = (1000, 3000),
                        callback: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> bool:
        """
        Start processing the queue in a background thread
        
        Args:
            max_workers: Maximum number of worker threads for submissions
            delay_range_ms: Range of delay between submissions in milliseconds (min, max)
            callback: Optional callback function called after each request is processed
            
        Returns:
            True if processing started, False if already processing
        """
        if self.processing:
            return False
            
        self.processing = True
        self._stop_processing.clear()
        
        self.processing_thread = threading.Thread(
            target=self._process_queue,
            args=(max_workers, delay_range_ms, callback),
            daemon=True
        )
        self.processing_thread.start()
        
        return True
        
    def stop_processing(self) -> bool:
        """
        Stop processing the queue
        
        Returns:
            True if processing was stopped, False if not processing
        """
        if not self.processing:
            return False
            
        self._stop_processing.set()
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
            
        self.processing = False
        return True
        
    def is_processing(self) -> bool:
        """
        Check if the queue is currently being processed
        
        Returns:
            True if processing, False otherwise
        """
        return self.processing and self.processing_thread and self.processing_thread.is_alive()
        
    def _process_queue(self, max_workers: int, delay_range_ms: Tuple[int, int],
                      callback: Optional[Callable[[str, Dict[str, Any]], None]]) -> None:
        """
        Process the queue in a background thread
        
        Args:
            max_workers: Maximum number of worker threads for submissions
            delay_range_ms: Range of delay between submissions in milliseconds (min, max)
            callback: Optional callback function called after each request is processed
        """
        while not self._stop_processing.is_set():
            # Get next request from queue
            request_id = self.pop_next_request()
            
            if not request_id:
                # Queue is empty, wait and check again
                time.sleep(5)
                continue
                
            # Get request details
            request = self.request_manager.get_request(request_id)
            if not request:
                # Request not found, continue to next
                continue
                
            # Update request status to in_progress
            self.request_manager.update_request_status(request_id, "in_progress")
            
            try:
                # Process the request
                result = self.submission_manager.batch_submit(
                    form_id=request.form_id,
                    form_url=request.form_url,
                    count=request.submission_count,
                    delay_range=delay_range_ms,
                    max_workers=max_workers
                )
                
                # Update request status based on result
                if result.get("cancelled", False):
                    status = "cancelled"
                elif result.get("error"):
                    status = "failed"
                else:
                    status = "completed"
                    
                self.request_manager.update_request_status(request_id, status, result)
                
                # Save to submission history
                self.history_manager.add_submission_record(
                    form_id=request.form_id,
                    form_url=request.form_url,
                    form_title=request.form_title,
                    count=request.submission_count,
                    delay_min=delay_range_ms[0],
                    delay_max=delay_range_ms[1],
                    max_workers=max_workers,
                    result=result
                )
                
                # Call callback if provided
                if callback:
                    callback(request_id, result)
                    
            except Exception as e:
                # Handle any exceptions during processing
                error_result = {
                    "error": str(e),
                    "total": request.submission_count,
                    "successful": 0,
                    "failed": request.submission_count
                }
                
                self.request_manager.update_request_status(request_id, "failed", error_result)
                
                # Call callback if provided
                if callback:
                    callback(request_id, error_result)
                    
            # Small delay before processing next request
            time.sleep(1)
            
        # Clear processing flag when done
        self.processing = False
