{"timestamp": 1748017012.2257404, "models": [{"id": "anthropic/claude-opus-4", "name": "Anthropic: <PERSON> 4", "description": "Claude Opus 4 is benchmarked as the world’s best coding model, at time of release, bringing sustained performance on complex, long-running tasks and agent workflows. It sets new benchmarks in software engineering, achieving leading results on SWE-bench (72.5%) and Terminal-bench (43.2%). Opus 4 supports extended, agentic workflows, handling thousands of task steps continuously for hours without degradation. \n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "context_length": 200000, "created": 1747931245, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.000075", "image": "0.024", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "stop", "reasoning", "include_reasoning", "tools", "tool_choice", "top_p", "top_k"]}, {"id": "anthropic/claude-sonnet-4", "name": "Anthropic: <PERSON> 4", "description": "Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "context_length": 200000, "created": 1747930371, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "stop", "reasoning", "include_reasoning", "tools", "tool_choice", "top_p", "top_k"]}, {"id": "mistralai/devstral-small:free", "name": "Mistral: <PERSON><PERSON><PERSON> (free)", "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/devstral-small", "name": "Mistral: <PERSON><PERSON><PERSON> Small", "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000007", "completion": "0.0000001", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed", "repetition_penalty", "top_k"]}, {"id": "google/gemma-3n-e4b-it:free", "name": "Google: Gemma 3n 4B (free)", "description": "Gemma 3n E4B-it is optimized for efficient execution on mobile and low-resource devices, such as phones, laptops, and tablets. It supports multimodal inputs—including text, visual data, and audio—enabling diverse tasks such as text generation, speech recognition, translation, and image analysis. Leveraging innovations like Per-Layer Embedding (PLE) caching and the MatFormer architecture, Gemma 3n dynamically manages memory usage and computational load by selectively activating model parameters, significantly reducing runtime resource requirements.\n\nThis model supports a wide linguistic range (trained in over 140 languages) and features a flexible 32K token context window. Gemma 3n can selectively load parameters, optimizing memory and computational efficiency based on the task or device capabilities, making it well-suited for privacy-focused, offline-capable applications and on-device AI solutions. [Read more in the blog post](https://developers.googleblog.com/en/introducing-gemma-3n/)", "context_length": 8192, "created": 1747776824, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "response_format"]}, {"id": "google/gemini-2.5-flash-preview-05-20", "name": "Google: Gemini 2.5 Flash Preview 05-20", "description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "created": 1747761924, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.0006192", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "google/gemini-2.5-flash-preview-05-20:thinking", "name": "Google: Gemini 2.5 Flash Preview 05-20 (thinking)", "description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "created": 1747761924, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000035", "image": "0.0006192", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "openai/codex-mini", "name": "OpenAI: Codex Mini", "description": "codex-mini-latest is a fine-tuned version of o4-mini specifically for use in Codex CLI. For direct use in the API, we recommend starting with gpt-4.1.", "context_length": 200000, "created": 1747409761, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000015", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "meta-llama/llama-3.3-8b-instruct:free", "name": "Meta: Llama 3.3 8B Instruct (free)", "description": "A lightweight and ultra-fast variant of Llama 3.3 70B, for use when quick response times are needed most.", "context_length": 128000, "created": 1747230154, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "repetition_penalty", "top_k"]}, {"id": "nousresearch/deephermes-3-mistral-24b-preview:free", "name": "Nous: DeepHermes 3 Mistral 24B Preview (free)", "description": "DeepHermes 3 (Mistral 24B Preview) is an instruction-tuned language model by Nous Research based on Mistral-Small-24B, designed for chat, function calling, and advanced multi-turn reasoning. It introduces a dual-mode system that toggles between intuitive chat responses and structured “deep reasoning” mode using special system prompts. Fine-tuned via distillation from R1, it supports structured output (JSON mode) and function call syntax for agent-based applications.\n\nDeepHermes 3 supports a **reasoning toggle via system prompt**, allowing users to switch between fast, intuitive responses and deliberate, multi-step reasoning. When activated with the following specific system instruction, the model enters a *\"deep thinking\"* mode—generating extended chains of thought wrapped in `<think></think>` tags before delivering a final answer. \n\nSystem Prompt: You are a deep thinking AI, you may use extremely long chains of thought to deeply consider the problem and deliberate with yourself via systematic reasoning processes to help come to a correct solution prior to answering. You should enclose your thoughts and internal monologue inside <think> </think> tags, and then provide your solution or response to the problem.\n", "context_length": 32768, "created": 1746830904, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/mistral-medium-3", "name": "Mistral: Mistral Medium 3", "description": "Mistral Medium 3 is a high-performance enterprise-grade language model designed to deliver frontier-level capabilities at significantly reduced operational cost. It balances state-of-the-art reasoning and multimodal performance with 8× lower cost compared to traditional large models, making it suitable for scalable deployments across professional and industrial use cases.\n\nThe model excels in domains such as coding, STEM reasoning, and enterprise adaptation. It supports hybrid, on-prem, and in-VPC deployments and is optimized for integration into custom workflows. Mistral Medium 3 offers competitive accuracy relative to larger models like Claude Sonnet 3.5/3.7, Llama 4 Maverick, and Command R+, while maintaining broad compatibility across cloud environments.", "context_length": 131072, "created": 1746627341, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000004", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "google/gemini-2.5-pro-preview", "name": "Google: Gemini 2.5 Pro Preview", "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "context_length": 1048576, "created": 1746578513, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000125", "completion": "0.00001", "image": "0.00516", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "seed", "response_format", "structured_outputs"]}, {"id": "arcee-ai/caller-large", "name": "Arcee AI: <PERSON><PERSON>", "description": "Caller Large is Arcee's specialist \"function‑calling\" SLM built to orchestrate external tools and APIs. Instead of maximizing next‑token accuracy, training focuses on structured JSON outputs, parameter extraction and multi‑step tool chains, making Caller a natural choice for retrieval‑augmented generation, robotic process automation or data‑pull chatbots. It incorporates a routing head that decides when (and how) to invoke a tool versus answering directly, reducing hallucinated calls. The model is already the backbone of Arcee Conductor's auto‑tool mode, where it parses user intent, emits clean function signatures and hands control back once the tool response is ready. Developers thus gain an OpenAI‑style function‑calling UX without handing requests to a frontier‑scale model. ", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.********", "completion": "0.********", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/spotlight", "name": "Arcee AI: Spotlight", "description": "Spotlight is a 7‑billion‑parameter vision‑language model derived from Qwen 2.5‑VL and fine‑tuned by Arcee AI for tight image‑text grounding tasks. It offers a 32 k‑token context window, enabling rich multimodal conversations that combine lengthy documents with one or more images. Training emphasized fast inference on consumer GPUs while retaining strong captioning, visual‐question‑answering, and diagram‑analysis accuracy. As a result, Spotlight slots neatly into agent workflows where screenshots, charts or UI mock‑ups need to be interpreted on the fly. Early benchmarks show it matching or out‑scoring larger VLMs such as LLaVA‑1.6 13 B on popular VQA and POPE alignment tests. ", "context_length": 131072, "created": 1746481552, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000018", "completion": "0.00000018", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/maestro-reasoning", "name": "Arcee AI: <PERSON><PERSON> Reasoning", "description": "Maestro Reasoning is Arcee's flagship analysis model: a 32 B‑parameter derivative of Qwen 2.5‑32 B tuned with DPO and chain‑of‑thought RL for step‑by‑step logic. Compared to the earlier 7 B preview, the production 32 B release widens the context window to 128 k tokens and doubles pass‑rate on MATH and GSM‑8K, while also lifting code completion accuracy. Its instruction style encourages structured \"thought → answer\" traces that can be parsed or hidden according to user preference. That transparency pairs well with audit‑focused industries like finance or healthcare where seeing the reasoning path matters. In Arcee Conductor, <PERSON><PERSON> is automatically selected for complex, multi‑constraint queries that smaller SLMs bounce. ", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000009", "completion": "0.0000033", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/virtuoso-large", "name": "Arcee AI: Virtuoso <PERSON>", "description": "Virtuoso‑Large is Arcee's top‑tier general‑purpose LLM at 72 B parameters, tuned to tackle cross‑domain reasoning, creative writing and enterprise QA. Unlike many 70 B peers, it retains the 128 k context inherited from Qwen 2.5, letting it ingest books, codebases or financial filings wholesale. Training blended DeepSeek R1 distillation, multi‑epoch supervised fine‑tuning and a final DPO/RLHF alignment stage, yielding strong performance on BIG‑Bench‑Hard, GSM‑8K and long‑context Needle‑In‑Haystack tests. Enterprises use Virtuoso‑Large as the \"fallback\" brain in Conductor pipelines when other SLMs flag low confidence. Despite its size, aggressive KV‑cache optimizations keep first‑token latency in the low‑second range on 8× H100 nodes, making it a practical production‑grade powerhouse.", "context_length": 131072, "created": 1746478885, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000075", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/coder-large", "name": "Arcee AI: <PERSON><PERSON>", "description": "Coder‑Large is a 32 B‑parameter offspring of Qwen 2.5‑Instruct that has been further trained on permissively‑licensed GitHub, CodeSearchNet and synthetic bug‑fix corpora. It supports a 32k context window, enabling multi‑file refactoring or long diff review in a single call, and understands 30‑plus programming languages with special attention to TypeScript, Go and Terraform. Internal benchmarks show 5–8 pt gains over CodeLlama‑34 B‑Python on HumanEval and competitive BugFix scores thanks to a reinforcement pass that rewards compilable output. The model emits structured explanations alongside code blocks by default, making it suitable for educational tooling as well as production copilot scenarios. Cost‑wise, Together AI prices it well below proprietary incumbents, so teams can scale interactive coding without runaway spend. ", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/virtuoso-medium-v2", "name": "Arcee AI: Virtuoso Medium V2", "description": "Virtuoso‑Medium‑v2 is a 32 B model distilled from DeepSeek‑v3 logits and merged back onto a Qwen 2.5 backbone, yielding a sharper, more factual successor to the original Virtuoso Medium. The team harvested ~1.1 B logit tokens and applied \"fusion‑merging\" plus DPO alignment, which pushed scores past Arcee‑Nova 2024 and many 40 B‑plus peers on MMLU‑Pro, MATH and HumanEval. With a 128 k context and aggressive quantization options (from BF16 down to 4‑bit GGUF), it balances capability with deployability on single‑GPU nodes. Typical use cases include enterprise chat assistants, technical writing aids and medium‑complexity code drafting where Virtuoso‑Large would be overkill. ", "context_length": 131072, "created": 1746478434, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/arcee-blitz", "name": "Arcee AI: <PERSON><PERSON>", "description": "Arcee Blitz is a 24 B‑parameter dense model distilled from DeepSeek and built on Mistral architecture for \"everyday\" chat. The distillation‑plus‑refinement pipeline trims compute while keeping DeepSeek‑style reasoning, so Blitz punches above its weight on MMLU, GSM‑8K and BBH compared with other mid‑size open models. With a default 128 k context window and competitive throughput, it serves as a cost‑efficient workhorse for summarization, brainstorming and light code help. Internally, Arcee uses Blitz as the default writer in Conductor pipelines when the heavier Virtuoso line is not required. Users therefore get near‑70 B quality at ~⅓ the latency and price. ", "context_length": 32768, "created": 1746470100, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000045", "completion": "0.00000075", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "microsoft/phi-4-reasoning-plus:free", "name": "Microsoft: Phi 4 Reasoning Plus (free)", "description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It uses the same dense decoder-only transformer architecture as Phi-4, but generates longer, more comprehensive outputs structured into a step-by-step reasoning trace and final answer.\n\nWhile it offers improved benchmark scores over Phi-4-reasoning across tasks like AIME, OmniMath, and HumanEvalPlus, its responses are typically ~50% longer, resulting in higher latency. Designed for English-only applications, it is well-suited for structured reasoning workflows where output quality takes priority over response speed.", "context_length": 32768, "created": 1746130961, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "microsoft/phi-4-reasoning-plus", "name": "Microsoft: Phi 4 Reasoning Plus", "description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It uses the same dense decoder-only transformer architecture as Phi-4, but generates longer, more comprehensive outputs structured into a step-by-step reasoning trace and final answer.\n\nWhile it offers improved benchmark scores over Phi-4-reasoning across tasks like AIME, OmniMath, and HumanEvalPlus, its responses are typically ~50% longer, resulting in higher latency. Designed for English-only applications, it is well-suited for structured reasoning workflows where output quality takes priority over response speed.", "context_length": 32768, "created": 1746130961, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000007", "completion": "0.00000035", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p"]}, {"id": "microsoft/phi-4-reasoning:free", "name": "Microsoft: Phi 4 Reasoning (free)", "description": "Phi-4-reasoning is a 14B parameter dense decoder-only transformer developed by Microsoft, fine-tuned from Phi-4 to enhance complex reasoning capabilities. It uses a combination of supervised fine-tuning on chain-of-thought traces and reinforcement learning, targeting math, science, and code reasoning tasks. With a 32k context window and high inference efficiency, it is optimized for structured responses in a two-part format: reasoning trace followed by a final solution.\n\nThe model achieves strong results on specialized benchmarks such as AIME, OmniMath, and LiveCodeBench, outperforming many larger models in structured reasoning tasks. It is released under the MIT license and intended for use in latency-constrained, English-only environments requiring reliable step-by-step logic. Recommended usage includes ChatML prompts and structured reasoning format for best results.", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "inception/mercury-coder-small-beta", "name": "Inception: Mercury Coder Small Beta", "description": "Mercury Coder Small is the first diffusion large language model (dLLM). Applying a breakthrough discrete diffusion approach, the model runs 5-10x faster than even speed optimized models like Claude 3.5 Haiku and GPT-4o Mini while matching their performance. Mercury Coder Small's speed means that developers can stay in the flow while coding, enjoying rapid chat-based iteration and responsive code completion suggestions. On Copilot Arena, Mercury Coder ranks 1st in speed and ties for 2nd in quality. Read more in the [blog post here](https://www.inceptionlabs.ai/introducing-mercury).", "context_length": 32000, "created": 1746033880, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "frequency_penalty", "presence_penalty", "stop"]}, {"id": "opengvlab/internvl3-14b:free", "name": "OpenGVLab: InternVL3 14B (free)", "description": "The 14b version of the InternVL3 series. An advanced multimodal large language model (MLLM) series that demonstrates superior overall performance. Compared to InternVL 2.5, InternVL3 exhibits superior multimodal perception and reasoning capabilities, while further extending its multimodal capabilities to encompass tool usage, GUI agents, industrial image analysis, 3D vision perception, and more.", "context_length": 32000, "created": 1746021355, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "opengvlab/internvl3-2b:free", "name": "OpenGVLab: InternVL3 2B (free)", "description": "The 2b version of the InternVL3 series, for an even higher inference speed and very reasonable performance. An advanced multimodal large language model (MLLM) series that demonstrates superior overall performance. Compared to InternVL 2.5, InternVL3 exhibits superior multimodal perception and reasoning capabilities, while further extending its multimodal capabilities to encompass tool usage, GUI agents, industrial image analysis, 3D vision perception, and more.", "context_length": 32000, "created": 1746019807, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "deepseek/deepseek-prover-v2:free", "name": "DeepSeek: DeepSeek Prover V2 (free)", "description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "context_length": 163840, "created": 1746013094, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-prover-v2", "name": "DeepSeek: DeepSeek Prover V2", "description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "context_length": 131072, "created": 1746013094, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.00000218", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format"]}, {"id": "meta-llama/llama-guard-4-12b", "name": "Meta: Llama Guard 4 12B", "description": "Llama Guard 4 is a Llama 4 Scout-derived multimodal pretrained model, fine-tuned for content safety classification. Similar to previous versions, it can be used to classify content in both LLM inputs (prompt classification) and in LLM responses (response classification). It acts as an LLM—generating text in its output that indicates whether a given prompt or response is safe or unsafe, and if unsafe, it also lists the content categories violated.\n\nLlama Guard 4 was aligned to safeguard against the standardized MLCommons hazards taxonomy and designed to support multimodal Llama 4 capabilities. Specifically, it combines features from previous Llama Guard models, providing content moderation for English and multiple supported languages, along with enhanced capabilities to handle mixed text-and-image prompts, including multiple images. Additionally, Llama Guard 4 is integrated into the Llama Moderations API, extending robust safety classification to text and images.", "context_length": 163840, "created": 1745975193, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.00000005", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "top_logprobs", "logprobs"]}, {"id": "qwen/qwen3-30b-a3b:free", "name": "Qwen: Qwen3 30B A3B (free)", "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "context_length": 40960, "created": 1745878604, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-30b-a3b", "name": "Qwen: Qwen3 30B A3B", "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "context_length": 40960, "created": 1745878604, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000008", "completion": "0.00000029", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "seed", "min_p"]}, {"id": "qwen/qwen3-8b:free", "name": "Qwen: <PERSON>wen3 8B (free)", "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "context_length": 40960, "created": 1745876632, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-8b", "name": "Qwen: <PERSON>wen3 8B", "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "context_length": 128000, "created": 1745876632, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000035", "completion": "0.000000138", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "qwen/qwen3-14b:free", "name": "Qwen: <PERSON>wen3 14B (free)", "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "context_length": 40960, "created": 1745876478, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-14b", "name": "Qwen: <PERSON>wen3 14B", "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "context_length": 40960, "created": 1745876478, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000007", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "qwen/qwen3-32b:free", "name": "Qwen: <PERSON>wen3 32B (free)", "description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "context_length": 40960, "created": 1745875945, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-32b", "name": "Qwen: <PERSON>wen3 32B", "description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "context_length": 40960, "created": 1745875945, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logprobs", "top_logprobs", "logit_bias"]}, {"id": "qwen/qwen3-235b-a22b:free", "name": "Qwen: Qwen3 235B A22B (free)", "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "context_length": 40960, "created": 1745875757, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-235b-a22b", "name": "Qwen: Qwen3 235B A22B", "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "context_length": 40960, "created": 1745875757, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000014", "completion": "0.0000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "tools", "tool_choice", "stop", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "seed", "min_p"]}, {"id": "tngtech/deepseek-r1t-chimera:free", "name": "TNG: DeepSeek R1T Chimera (free)", "description": "DeepSeek-R1T-Chimera is created by merging DeepSeek-R1 and DeepSeek-V3 (0324), combining the reasoning capabilities of R1 with the token efficiency improvements of V3. It is based on a DeepSeek-MoE Transformer architecture and is optimized for general text generation tasks.\n\nThe model merges pretrained weights from both source models to balance performance across reasoning, efficiency, and instruction-following tasks. It is released under the MIT license and intended for research and commercial use.", "context_length": 163840, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-z1-rumination-32b", "name": "THUDM: GLM Z1 Rumination 32B ", "description": "THUDM: GLM Z1 Rumination 32B is a 32B-parameter deep reasoning model from the GLM-4-Z1 series, optimized for complex, open-ended tasks requiring prolonged deliberation. It builds upon glm-4-32b-0414 with additional reinforcement learning phases and multi-stage alignment strategies, introducing “rumination” capabilities designed to emulate extended cognitive processing. This includes iterative reasoning, multi-hop analysis, and tool-augmented workflows such as search, retrieval, and citation-aware synthesis.\n\nThe model excels in research-style writing, comparative analysis, and intricate question answering. It supports function calling for search and navigation primitives (`search`, `click`, `open`, `finish`), enabling use in agent-style pipelines. Rumination behavior is governed by multi-turn loops with rule-based reward shaping and delayed decision mechanisms, benchmarked against Deep Research frameworks such as OpenAI’s internal alignment stacks. This variant is suitable for scenarios requiring depth over speed.", "context_length": 32000, "created": 1745601495, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000024", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "microsoft/mai-ds-r1:free", "name": "Microsoft: MAI DS R1 (free)", "description": "MAI-DS-R1 is a post-trained variant of DeepSeek-R1 developed by the Microsoft AI team to improve the model’s responsiveness on previously blocked topics while enhancing its safety profile. Built on top of DeepSeek-R1’s reasoning foundation, it integrates 110k examples from the Tulu-3 SFT dataset and 350k internally curated multilingual safety-alignment samples. The model retains strong reasoning, coding, and problem-solving capabilities, while unblocking a wide range of prompts previously restricted in R1.\n\nMAI-DS-R1 demonstrates improved performance on harm mitigation benchmarks and maintains competitive results across general reasoning tasks. It surpasses R1-1776 in satisfaction metrics for blocked queries and reduces leakage in harmful content categories. The model is based on a transformer MoE architecture and is suitable for general-purpose use cases, excluding high-stakes domains such as legal, medical, or autonomous systems.", "context_length": 163840, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-z1-32b:free", "name": "THUDM: GLM Z1 32B (free)", "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "context_length": 32768, "created": 1744924148, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-z1-32b", "name": "THUDM: GLM Z1 32B", "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "context_length": 32000, "created": 1744924148, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000024", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "thudm/glm-4-32b:free", "name": "THUDM: GLM 4 32B (free)", "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "context_length": 32768, "created": 1744920915, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-4-32b", "name": "THUDM: GLM 4 32B", "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "context_length": 32000, "created": 1744920915, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000024", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "google/gemini-2.5-flash-preview", "name": "Google: Gemini 2.5 Flash Preview 04-17", "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "created": 1744914667, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.0006192", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "response_format", "structured_outputs"]}, {"id": "google/gemini-2.5-flash-preview:thinking", "name": "Google: Gemini 2.5 Flash Preview 04-17 (thinking)", "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "created": 1744914667, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000035", "image": "0.0006192", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "response_format", "structured_outputs"]}, {"id": "openai/o4-mini-high", "name": "OpenAI: o4 Mini High", "description": "OpenAI o4-mini-high is the same model as [o4-mini](/openai/o4-mini) with reasoning_effort set to high. \n\nOpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "context_length": 200000, "created": 1744824212, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0.0008415", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "openai/o3", "name": "OpenAI: o3", "description": "o3 is a well-rounded and powerful model across domains. It sets a new standard for math, science, coding, and visual reasoning tasks. It also excels at technical writing and instruction-following. Use it to think through multi-step problems that involve analysis across text, code, and images. Note that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "context_length": 200000, "created": 1744823457, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00001", "completion": "0.00004", "image": "0.00765", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "openai/o4-mini", "name": "OpenAI: o4 Mini", "description": "OpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "context_length": 200000, "created": 1744820942, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0.0008415", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "shisa-ai/shisa-v2-llama3.3-70b:free", "name": "Shisa AI: Shisa V2 Llama 3.3 70B  (free)", "description": "Shisa V2 Llama 3.3 70B is a bilingual Japanese-English chat model fine-tuned by Shisa.AI on Meta’s Llama-3.3-70B-Instruct base. It prioritizes Japanese language performance while retaining strong English capabilities. The model was optimized entirely through post-training, using a refined mix of supervised fine-tuning (SFT) and DPO datasets including regenerated ShareGPT-style data, translation tasks, roleplaying conversations, and instruction-following prompts. Unlike earlier Shisa releases, this version avoids tokenizer modifications or extended pretraining.\n\nShisa V2 70B achieves leading Japanese task performance across a wide range of custom and public benchmarks, including JA MT Bench, ELYZA 100, and Rakuda. It supports a 128K token context length and integrates smoothly with inference frameworks like vLLM and SGLang. While it inherits safety characteristics from its base model, no additional alignment was applied. The model is intended for high-performance bilingual chat, instruction following, and translation tasks across JA/EN.", "context_length": 32768, "created": 1744754858, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen2.5-coder-7b-instruct", "name": "Qwen: Qwen2.5 Coder 7B Instruct", "description": "Qwen2.5-Coder-7B-Instruct is a 7B parameter instruction-tuned language model optimized for code-related tasks such as code generation, reasoning, and bug fixing. Based on the Qwen2.5 architecture, it incorporates enhancements like RoPE, SwiGLU, RMSNorm, and GQA attention with support for up to 128K tokens using YaRN-based extrapolation. It is trained on a large corpus of source code, synthetic data, and text-code grounding, providing robust performance across programming languages and agentic coding workflows.\n\nThis model is part of the Qwen2.5-Coder family and offers strong compatibility with tools like vLLM for efficient deployment. Released under the Apache 2.0 license.", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000001", "completion": "0.00000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "openai/gpt-4.1", "name": "OpenAI: GPT-4.1", "description": "GPT-4.1 is a flagship large language model optimized for advanced instruction following, real-world software engineering, and long-context reasoning. It supports a 1 million token context window and outperforms GPT-4o and GPT-4.5 across coding (54.6% SWE-bench Verified), instruction compliance (87.4% IFEval), and multimodal understanding benchmarks. It is tuned for precise code diffs, agent reliability, and high recall in large document contexts, making it ideal for agents, IDE tooling, and enterprise knowledge retrieval.", "context_length": 1047576, "created": 1744651385, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4.1-mini", "name": "OpenAI: GPT-4.1 Mini", "description": "GPT-4.1 Mini is a mid-sized model delivering performance competitive with GPT-4o at substantially lower latency and cost. It retains a 1 million token context window and scores 45.1% on hard instruction evals, 35.8% on MultiChallenge, and 84.1% on IFEval. Mini also shows strong coding ability (e.g., 31.6% on Aid<PERSON>’s polyglot diff benchmark) and vision understanding, making it suitable for interactive applications with tight performance constraints.", "context_length": 1047576, "created": 1744651381, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000004", "completion": "0.0000016", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4.1-nano", "name": "OpenAI: GPT-4.1 Nano", "description": "For tasks that demand low latency, GPT‑4.1 nano is the fastest and cheapest model in the GPT-4.1 series. It delivers exceptional performance at a small size with its 1 million token context window, and scores 80.1% on MMLU, 50.3% on GPQA, and 9.8% on Aider polyglot coding – even higher than GPT‑4o mini. It’s ideal for tasks like classification or autocompletion.", "context_length": 1047576, "created": 1744651369, "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "eleutherai/llemma_7b", "name": "EleutherAI: Llemma 7b", "description": "Llemma 7B is a language model for mathematics. It was initialized with Code Llama 7B weights, and trained on the Proof-Pile-2 for 200B tokens. Llemma models are particularly strong at chain-of-thought mathematical reasoning and using computational tools for mathematics, such as Python and formal theorem provers.", "context_length": 4096, "created": 1744643225, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "alfredpros/codellama-7b-instruct-solidity", "name": "AlfredPros: CodeLLaMa 7B Instruct Solidity", "description": "A finetuned 7 billion parameters Code LLaMA - Instruct model to generate Solidity smart contract using 4-bit QLoRA finetuning provided by PEFT library.", "context_length": 4096, "created": 1744641874, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "arliai/qwq-32b-arliai-rpr-v1:free", "name": "ArliAI: QwQ 32B RpR v1 (free)", "description": "QwQ-32B-ArliAI-RpR-v1 is a 32B parameter model fine-tuned from Qwen/QwQ-32B using a curated creative writing and roleplay dataset originally developed for the RPMax series. It is designed to maintain coherence and reasoning across long multi-turn conversations by introducing explicit reasoning steps per dialogue turn, generated and refined using the base model itself.\n\nThe model was trained using RS-QLORA+ on 8K sequence lengths and supports up to 128K context windows (with practical performance around 32K). It is optimized for creative roleplay and dialogue generation, with an emphasis on minimizing cross-context repetition while preserving stylistic diversity.", "context_length": 32768, "created": 1744555982, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "agentica-org/deepcoder-14b-preview:free", "name": "Agentica: Deepcoder 14B Preview (free)", "description": "DeepCoder-14B-Preview is a 14B parameter code generation model fine-tuned from DeepSeek-R1-Distill-Qwen-14B using reinforcement learning with GRPO+ and iterative context lengthening. It is optimized for long-context program synthesis and achieves strong performance across coding benchmarks, including 60.6% on LiveCodeBench v5, competitive with models like o3-Mini", "context_length": 96000, "created": 1744555395, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "moonshotai/kimi-vl-a3b-thinking:free", "name": "Moonshot AI: <PERSON><PERSON> VL A3B Thinking (free)", "description": "Kimi-VL is a lightweight Mixture-of-Experts vision-language model that activates only 2.8B parameters per step while delivering strong performance on multimodal reasoning and long-context tasks. The Kimi-VL-A3B-Thinking variant, fine-tuned with chain-of-thought and reinforcement learning, excels in math and visual reasoning benchmarks like MathVision, MMMU, and MathVista, rivaling much larger models such as Qwen2.5-VL-7B and Gemma-3-12B. It supports 128K context and high-resolution input via its MoonViT encoder.", "context_length": 131072, "created": 1744304841, "input_modalities": ["image", "text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "x-ai/grok-3-mini-beta", "name": "xAI: Grok 3 Mini Beta", "description": "Grok 3 Mini is a lightweight, smaller thinking model. Unlike traditional models that generate answers immediately, Grok 3 Mini thinks before responding. It’s ideal for reasoning-heavy tasks that don’t demand extensive domain knowledge, and shines in math-specific and quantitative use cases, such as solving challenging puzzles or math problems.\n\nTransparent \"thinking\" traces accessible. Defaults to low reasoning, can boost with setting `reasoning: { effort: \"high\" }`\n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000003", "completion": "0.0000005", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "x-ai/grok-3-beta", "name": "xAI: Grok 3 Beta", "description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.\n\nExcels in structured tasks and benchmarks like GPQA, LCB, and MMLU-Pro where it outperforms Grok 3 Mini even on high thinking. \n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1:free", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1 (free)", "description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "context_length": 131072, "created": 1744119494, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1", "description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "context_length": 131072, "created": 1744119494, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000013", "completion": "0.0000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "name": "NVIDIA: Llama 3.1 Nemotron Ultra 253B v1 (free)", "description": "Llama-3.1-Nemotron-Ultra-253B-v1 is a large language model (LLM) optimized for advanced reasoning, human-interactive chat, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta’s Llama-3.1-405B-Instruct, it has been significantly customized using Neural Architecture Search (NAS), resulting in enhanced efficiency, reduced memory usage, and improved inference latency. The model supports a context length of up to 128K tokens and can operate efficiently on an 8x NVIDIA H100 node.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "context_length": 131072, "created": 1744115059, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "meta-llama/llama-4-maverick:free", "name": "Meta: Llama 4 Maverick (free)", "description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "context_length": 128000, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "tools", "tool_choice"]}, {"id": "meta-llama/llama-4-maverick", "name": "Meta: Llama 4 Maverick", "description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "context_length": 1048576, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000016", "completion": "0.0000006", "image": "0.0006684", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs", "tools", "tool_choice", "structured_outputs", "response_format"]}, {"id": "meta-llama/llama-4-scout:free", "name": "Meta: Llama 4 Scout (free)", "description": "Llama 4 Scout 17B Instruct (16E) is a mixture-of-experts (MoE) language model developed by Meta, activating 17 billion parameters out of a total of 109B. It supports native multimodal input (text and image) and multilingual output (text and code) across 12 supported languages. Designed for assistant-style interaction and visual reasoning, <PERSON> uses 16 experts per forward pass and features a context length of 10 million tokens, with a training corpus of ~40 trillion tokens.\n\nBuilt for high efficiency and local or commercial deployment, Llama 4 Scout incorporates early fusion for seamless modality integration. It is instruction-tuned for use in multilingual chat, captioning, and image understanding tasks. Released under the Llama 4 Community License, it was last trained on data up to August 2024 and launched publicly on April 5, 2025.", "context_length": 200000, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "tools", "tool_choice"]}, {"id": "meta-llama/llama-4-scout", "name": "Meta: Llama 4 Scout", "description": "Llama 4 Scout 17B Instruct (16E) is a mixture-of-experts (MoE) language model developed by Meta, activating 17 billion parameters out of a total of 109B. It supports native multimodal input (text and image) and multilingual output (text and code) across 12 supported languages. Designed for assistant-style interaction and visual reasoning, <PERSON> uses 16 experts per forward pass and features a context length of 10 million tokens, with a training corpus of ~40 trillion tokens.\n\nBuilt for high efficiency and local or commercial deployment, Llama 4 Scout incorporates early fusion for seamless modality integration. It is instruction-tuned for use in multilingual chat, captioning, and image understanding tasks. Released under the Llama 4 Community License, it was last trained on data up to August 2024 and launched publicly on April 5, 2025.", "context_length": 1048576, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000008", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "response_format", "tools", "tool_choice", "structured_outputs", "repetition_penalty", "top_k", "top_logprobs", "logprobs", "logit_bias", "min_p"]}, {"id": "all-hands/openhands-lm-32b-v0.1", "name": "OpenHands LM 32B V0.1", "description": "OpenHands LM v0.1 is a 32B open-source coding model fine-tuned from Qwen2.5-Coder-32B-Instruct using reinforcement learning techniques outlined in SWE-Gym. It is optimized for autonomous software development agents and achieves strong performance on SWE-Bench Verified, with a 37.2% resolve rate. The model supports a 128K token context window, making it well-suited for long-horizon code reasoning and large codebase tasks.\n\nOpenHands LM is designed for local deployment and runs on consumer-grade GPUs such as a single 3090. It enables fully offline agent workflows without dependency on proprietary APIs. This release is intended as a research preview, and future updates aim to improve generalizability, reduce repetition, and offer smaller variants.", "context_length": 16384, "created": 1743613013, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000026", "completion": "0.0000034", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "deepseek/deepseek-v3-base:free", "name": "DeepSeek: DeepSeek V3 Base (free)", "description": "Note that this is a base model mostly meant for testing, you need to provide detailed prompts for the model to return useful responses. \n\nDeepSeek-V3 Base is a 671B parameter open Mixture-of-Experts (MoE) language model with 37B active parameters per forward pass and a context length of 128K tokens. Trained on 14.8T tokens using FP8 mixed precision, it achieves high training efficiency and stability, with strong performance across language, reasoning, math, and coding tasks. \n\nDeepSeek-V3 Base is the pre-trained model behind [DeepSeek V3](/deepseek/deepseek-chat-v3)", "context_length": 163840, "created": 1743272023, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "scb10x/llama3.1-typhoon2-8b-instruct", "name": "Typhoon2 8B Instruct", "description": "Llama3.1-Typhoon2-8B-Instruct is a Thai-English instruction-tuned model with 8 billion parameters, built on Llama 3.1. It significantly improves over its base model in Thai reasoning, instruction-following, and function-calling tasks, while maintaining competitive English performance. The model is optimized for bilingual interaction and performs well on Thai-English code-switching, MT-Bench, IFEval, and tool-use benchmarks.\n\nDespite its smaller size, it demonstrates strong generalization across math, coding, and multilingual benchmarks, outperforming comparable 8B models across most Thai-specific tasks. Full benchmark results and methodology are available in the [technical report.](https://arxiv.org/abs/2412.13702)", "context_length": 8192, "created": 1743196511, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000018", "completion": "0.00000018", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "scb10x/llama3.1-typhoon2-70b-instruct", "name": "Typhoon2 70B Instruct", "description": "Llama3.1-Typhoon2-70B-Instruct is a Thai-English instruction-tuned language model with 70 billion parameters, built on Llama 3.1. It demonstrates strong performance across general instruction-following, math, coding, and tool-use tasks, with state-of-the-art results in Thai-specific benchmarks such as IFEval, MT-Bench, and Thai-English code-switching.\n\nThe model excels in bilingual reasoning and function-calling scenarios, offering high accuracy across diverse domains. Comparative evaluations show consistent improvements over prior Thai LLMs and other Llama-based baselines. Full results and methodology are available in the [technical report.](https://arxiv.org/abs/2412.13702)", "context_length": 8192, "created": 1743196170, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000088", "completion": "0.00000088", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "qwen/qwen2.5-vl-3b-instruct:free", "name": "Qwen: Qwen2.5 VL 3B Instruct (free)", "description": "Qwen2.5 VL 3B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 64000, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemini-2.5-pro-exp-03-25", "name": "Google: Gemini 2.5 Pro Experimental", "description": "This model has been deprecated by Google in favor of the (paid Preview model)[google/gemini-2.5-pro-preview]\n \nGemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "context_length": 1048576, "created": 1742922099, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "seed", "response_format", "structured_outputs"]}, {"id": "qwen/qwen2.5-vl-32b-instruct:free", "name": "Qwen: Qwen2.5 VL 32B Instruct (free)", "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "context_length": 8192, "created": 1742839838, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty", "stop", "frequency_penalty", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen2.5-vl-32b-instruct", "name": "Qwen: Qwen2.5 VL 32B Instruct", "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "context_length": 128000, "created": 1742839838, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "DeepSeek: DeepSeek V3 0324 (free)", "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 163840, "created": 1742824755, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "top_a"]}, {"id": "deepseek/deepseek-chat-v3-0324", "name": "DeepSeek: DeepSeek V3 0324", "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 163840, "created": 1742824755, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000003", "completion": "0.00000088", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "stop", "tools", "tool_choice", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "seed", "min_p"]}, {"id": "featherless/qwerky-72b:free", "name": "Qwerky 72B (free)", "description": "Qwerky-72B is a linear-attention RWKV variant of the Qwen 2.5 72B model, optimized to significantly reduce computational cost at scale. Leveraging linear attention, it achieves substantial inference speedups (>1000x) while retaining competitive accuracy on common benchmarks like ARC, HellaSwag, Lambada, and MMLU. It inherits knowledge and language support from Qwen 2.5, supporting approximately 30 languages, making it suitable for efficient inference in large-context applications.", "context_length": 32768, "created": 1742481597, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "openai/o1-pro", "name": "OpenAI: o1-pro", "description": "The o1 series of models are trained with reinforcement learning to think before they answer and perform complex reasoning. The o1-pro model uses more compute to think harder and provide consistently better answers.", "context_length": 200000, "created": 1742423211, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00015", "completion": "0.0006", "image": "0.21675", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "mistralai/mistral-small-3.1-24b-instruct:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3.1 24B (free)", "description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments.", "context_length": 96000, "created": 1742238937, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/mistral-small-3.1-24b-instruct", "name": "Mistral: Mistra<PERSON> Small 3.1 24B", "description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments.", "context_length": 131072, "created": 1742238937, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "tools", "tool_choice", "stop", "response_format", "structured_outputs", "seed", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "open-r1/olympiccoder-32b:free", "name": "OlympicCoder 32B (free)", "description": "OlympicCoder-32B is a high-performing open-source model fine-tuned using the CodeForces-CoTs dataset, containing approximately 100,000 chain-of-thought programming samples. It excels at complex competitive programming benchmarks, such as IOI 2024 and Codeforces-style challenges, frequently surpassing state-of-the-art closed-source models. OlympicCoder-32B provides advanced reasoning, coherent multi-step problem-solving, and robust code generation capabilities, demonstrating significant potential for olympiad-level competitive programming applications.", "context_length": 32768, "created": 1742077228, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemma-3-1b-it:free", "name": "Google: Gemma 3 1B (free)", "description": "Gemma 3 1B is the smallest of the new Gemma 3 family. It handles context windows up to 32k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Note: Gemma 3 1B is not multimodal. For the smallest multimodal Gemma 3 model, please see [Gemma 3 4B](google/gemma-3-4b-it)", "context_length": 32768, "created": 1741963556, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemma-3-4b-it:free", "name": "Google: Gemma 3 4B (free)", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "context_length": 96000, "created": 1741905510, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "google/gemma-3-4b-it", "name": "Google: Gemma 3 4B", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "context_length": 131072, "created": 1741905510, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p"]}, {"id": "ai21/jamba-1.6-large", "name": "AI21: Jamba 1.6 Large", "description": "AI21 Jamba Large 1.6 is a high-performance hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. Developed by AI21, it excels in extremely long-context handling (256K tokens), demonstrates superior inference efficiency (up to 2.5x faster than comparable models), and supports structured JSON output and tool-use capabilities. It has 94 billion active parameters (398 billion total), optimized quantization support (ExpertsInt8), and multilingual proficiency in languages such as English, Spanish, French, Portuguese, Italian, Dutch, German, Arabic, and Hebrew.\n\nUsage of this model is subject to the [Jamba Open Model License](https://www.ai21.com/licenses/jamba-open-model-license).", "context_length": 256000, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop"]}, {"id": "ai21/jamba-1.6-mini", "name": "AI21: Jamba Mini 1.6", "description": "AI21 Jamba Mini 1.6 is a hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. With 12 billion active parameters (52 billion total), this model excels in extremely long-context tasks (up to 256K tokens) and achieves superior inference efficiency, outperforming comparable open models on tasks such as retrieval-augmented generation (RAG) and grounded question answering. Jamba Mini 1.6 supports multilingual tasks across English, Spanish, French, Portuguese, Italian, Dutch, German, Arabic, and Hebrew, along with structured JSON output and tool-use capabilities.\n\nUsage of this model is subject to the [Jamba Open Model License](https://www.ai21.com/licenses/jamba-open-model-license).", "context_length": 256000, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop"]}, {"id": "google/gemma-3-12b-it:free", "name": "Google: Gemma 3 12B (free)", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "context_length": 96000, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "google/gemma-3-12b-it", "name": "Google: Gemma 3 12B", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "context_length": 131072, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.0000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p"]}, {"id": "cohere/command-a", "name": "Cohere: Command A", "description": "Command A is an open-weights 111B parameter model with a 256k context window focused on delivering great performance across agentic, multilingual, and coding use cases.\nCompared to other leading proprietary and open-weights models Command A delivers maximum performance with minimum hardware costs, excelling on business-critical agentic and multilingual tasks.", "context_length": 256000, "created": 1741894342, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4o-mini-search-preview", "name": "OpenAI: GPT-4o-mini Search Preview", "description": "GPT-4o mini Search Preview is a specialized model for web search in Chat Completions. It is trained to understand and execute web search queries.", "context_length": 128000, "created": 1741818122, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.000217", "request": "0.0275"}, "supported_parameters": ["web_search_options", "max_tokens", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4o-search-preview", "name": "OpenAI: GPT-4o Search Preview", "description": "GPT-4o Search Previewis a specialized model for web search in Chat Completions. It is trained to understand and execute web search queries.", "context_length": 128000, "created": 1741817949, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0.003613", "request": "0.035"}, "supported_parameters": ["web_search_options", "max_tokens", "response_format", "structured_outputs"]}, {"id": "rekaai/reka-flash-3:free", "name": "Reka: <PERSON> 3 (free)", "description": "Reka Flash 3 is a general-purpose, instruction-tuned large language model with 21 billion parameters, developed by Reka. It excels at general chat, coding tasks, instruction-following, and function calling. Featuring a 32K context length and optimized through reinforcement learning (RLOO), it provides competitive performance comparable to proprietary models within a smaller parameter footprint. Ideal for low-latency, local, or on-device deployments, Reka Flash 3 is compact, supports efficient quantization (down to 11GB at 4-bit precision), and employs explicit reasoning tags (\"<reasoning>\") to indicate its internal thought process.\n\nReka Flash 3 is primarily an English model with limited multilingual understanding capabilities. The model weights are released under the Apache 2.0 license.", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemma-3-27b-it:free", "name": "Google: <PERSON> 3 27B (free)", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "context_length": 96000, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "google/gemma-3-27b-it", "name": "Google: Gemma 3 27B", "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "context_length": 131072, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000002", "image": "0.0000256", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logit_bias", "top_logprobs", "logprobs"]}, {"id": "thedrummer/anubis-pro-105b-v1", "name": "TheDrummer: Anubis Pro 105B V1", "description": "Anubis Pro 105B v1 is an expanded and refined variant of Meta’s Llama 3.3 70B, featuring 50% additional layers and further fine-tuning to leverage its increased capacity. Designed for advanced narrative, roleplay, and instructional tasks, it demonstrates enhanced emotional intelligence, creativity, nuanced character portrayal, and superior prompt adherence compared to smaller models. Its larger parameter count allows for deeper contextual understanding and extended reasoning capabilities, optimized for engaging, intelligent, and coherent interactions.", "context_length": 131072, "created": 1741642290, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k"]}, {"id": "thedrummer/skyfall-36b-v2", "name": "TheDrummer: Skyfall 36B V2", "description": "Skyfall 36B v2 is an enhanced iteration of Mistral Small 2501, specifically fine-tuned for improved creativity, nuanced writing, role-playing, and coherent storytelling.", "context_length": 32768, "created": 1741636566, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k"]}, {"id": "microsoft/phi-4-multimodal-instruct", "name": "Microsoft: Phi 4 Multimodal Instruct", "description": "Phi-4 Multimodal Instruct is a versatile 5.6B parameter foundation model that combines advanced reasoning and instruction-following capabilities across both text and visual inputs, providing accurate text outputs. The unified architecture enables efficient, low-latency inference, suitable for edge and mobile deployments. Phi-4 Multimodal Instruct supports text inputs in multiple languages including Arabic, Chinese, English, French, German, Japanese, Spanish, and more, with visual input optimized primarily for English. It delivers impressive performance on multimodal tasks involving mathematical, scientific, and document reasoning, providing developers and enterprises a powerful yet compact model for sophisticated interactive applications. For more information, see the [Phi-4 Multimodal blog post](https://azure.microsoft.com/en-us/blog/empowering-innovation-the-next-generation-of-the-phi-family/).\n", "context_length": 131072, "created": 1741396284, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.0000001", "image": "0.00017685", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p"]}, {"id": "perplexity/sonar-reasoning-pro", "name": "Perplexity: Sonar Reasoning Pro", "description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nSonar Reasoning Pro is a premier reasoning model powered by DeepSeek R1 with Chain of Thought (CoT). Designed for advanced use cases, it supports in-depth, multi-step queries with a larger context window and can surface more citations per search, enabling more comprehensive and extensible responses.", "context_length": 128000, "created": 1741313308, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "web_search_options", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "perplexity/sonar-pro", "name": "Perplexity: Sonar Pro", "description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nFor enterprises seeking more advanced capabilities, the Sonar Pro API can handle in-depth, multi-step queries with added extensibility, like double the number of citations per search as Sonar on average. Plus, with a larger context window, it can handle longer and more nuanced searches and follow-up questions. ", "context_length": 200000, "created": 1741312423, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "web_search_options", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "perplexity/sonar-deep-research", "name": "Perplexity: Sonar Deep Research", "description": "Sonar Deep Research is a research-focused model designed for multi-step retrieval, synthesis, and reasoning across complex topics. It autonomously searches, reads, and evaluates sources, refining its approach as it gathers information. This enables comprehensive report generation across domains like finance, technology, health, and current events.\n\nNotes on Pricing ([Source](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-deep-research)) \n- Input tokens comprise of Prompt tokens (user prompt) + Citation tokens (these are processed tokens from running searches)\n- Deep Research runs multiple searches to conduct exhaustive research. Searches are priced at $5/1000 searches. A request that does 30 searches will cost $0.15 in this step.\n- Reasoning is a distinct step in Deep Research since it does extensive automated reasoning through all the material it gathers during its research phase. Reasoning tokens here are a bit different than the CoTs in the answer - these are tokens that we use to reason through the research material prior to generating the outputs via the CoTs. Reasoning tokens are priced at $3/1M tokens", "context_length": 128000, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "deepseek/deepseek-r1-zero:free", "name": "DeepSeek: DeepSeek R1 Zero (free)", "description": "DeepSeek-R1-Zero is a model trained via large-scale reinforcement learning (RL) without supervised fine-tuning (SFT) as a preliminary step. It's 671B parameters in size, with 37B active in an inference pass.\n\nIt demonstrates remarkable performance on reasoning. With RL, DeepSeek-R1-Zero naturally emerged with numerous powerful and interesting reasoning behaviors.\n\nDeepSeek-R1-Zero encounters challenges such as endless repetition, poor readability, and language mixing. See [DeepSeek R1](/deepseek/deepseek-r1) for the SFT model.\n\n", "context_length": 128000, "created": 1741297434, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwq-32b:free", "name": "Qwen: QwQ 32B (free)", "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "context_length": 40000, "created": 1741208814, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwq-32b", "name": "Qwen: QwQ 32B", "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "context_length": 131072, "created": 1741208814, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "logprobs", "top_logprobs", "seed", "tools", "tool_choice", "structured_outputs"]}, {"id": "moonshotai/moonlight-16b-a3b-instruct:free", "name": "Moonshot AI: Moonlight 16B A3B Instruct (free)", "description": "Moonlight-16B-A3B-Instruct is a 16B-parameter Mixture-of-Experts (MoE) language model developed by Moonshot AI. It is optimized for instruction-following tasks with 3B activated parameters per inference. The model advances the Pareto frontier in performance per FLOP across English, coding, math, and Chinese benchmarks. It outperforms comparable models like Llama3-3B and Deepseek-v2-Lite while maintaining efficient deployment capabilities through Hugging Face integration and compatibility with popular inference engines like vLLM12.", "context_length": 8192, "created": 1740719801, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "nousresearch/deephermes-3-llama-3-8b-preview:free", "name": "Nous: DeepHermes 3 Llama 3 8B Preview (free)", "description": "DeepHermes 3 Preview is the latest version of our flagship Hermes series of LLMs by Nous Research, and one of the first models in the world to unify Reasoning (long chains of thought that improve answer accuracy) and normal LLM response modes into one model. We have also improved LLM annotation, judgement, and function calling.\n\nDeepHermes 3 Preview is one of the first LLM models to unify both \"intuitive\", traditional mode responses and long chain of thought reasoning responses into a single model, toggled by a system prompt.", "context_length": 131072, "created": 1740719372, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "openai/gpt-4.5-preview", "name": "OpenAI: GPT-4.5 (Preview)", "description": "GPT-4.5 (Preview) is a research preview of OpenAI’s latest language model, designed to advance capabilities in reasoning, creativity, and multi-turn conversation. It builds on previous iterations with improvements in world knowledge, contextual coherence, and the ability to follow user intent more effectively.\n\nThe model demonstrates enhanced performance in tasks that require open-ended thinking, problem-solving, and communication. Early testing suggests it is better at generating nuanced responses, maintaining long-context coherence, and reducing hallucinations compared to earlier versions.\n\nThis research preview is intended to help evaluate GPT-4.5’s strengths and limitations in real-world use cases as OpenAI continues to refine and develop future models. Read more at the [blog post here.](https://openai.com/index/introducing-gpt-4-5/)", "context_length": 128000, "created": 1740687810, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000075", "completion": "0.00015", "image": "0.108375", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "google/gemini-2.0-flash-lite-001", "name": "Google: Gemini 2.0 Flash Lite", "description": "Gemini 2.0 Flash Lite offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5), all at extremely economical token prices.", "context_length": 1048576, "created": 1740506212, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000075", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "response_format", "structured_outputs"]}, {"id": "anthropic/claude-3.7-sonnet", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON>", "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "context_length": 200000, "created": 1740422110, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "top_k", "stop"]}, {"id": "anthropic/claude-3.7-sonnet:thinking", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (thinking)", "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "context_length": 200000, "created": 1740422110, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "top_k", "stop"]}, {"id": "anthropic/claude-3.7-sonnet:beta", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (self-moderated)", "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "context_length": 200000, "created": 1740422110, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "stop", "reasoning", "include_reasoning", "tools", "tool_choice"]}, {"id": "perplexity/r1-1776", "name": "Perplexity: R1 1776", "description": "R1 1776 is a version of DeepSeek-R1 that has been post-trained to remove censorship constraints related to topics restricted by the Chinese government. The model retains its original reasoning capabilities while providing direct responses to a wider range of queries. R1 1776 is an offline chat model that does not use the perplexity search subsystem.\n\nThe model was tested on a multilingual dataset of over 1,000 examples covering sensitive topics to measure its likelihood of refusal or overly filtered responses. [Evaluation Results](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/GiN2VqC5hawUgAGJ6oHla.png) Its performance on math and reasoning benchmarks remains similar to the base R1 model. [Reasoning Performance](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/n4Z9Byqp2S7sKUvCvI40R.png)\n\nRead more on the [Blog Post](https://perplexity.ai/hub/blog/open-sourcing-r1-1776)", "context_length": 128000, "created": 1740004929, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "top_k", "frequency_penalty", "presence_penalty", "stop", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/mistral-saba", "name": "Mistral: Sa<PERSON>", "description": "Mistral Saba is a 24B-parameter language model specifically designed for the Middle East and South Asia, delivering accurate and contextually relevant responses while maintaining efficient performance. Trained on curated regional datasets, it supports multiple Indian-origin languages—including Tamil and Malayalam—alongside Arabic. This makes it a versatile option for a range of regional and multilingual applications. Read more at the blog post [here](https://mistral.ai/en/news/mistral-saba)", "context_length": 32768, "created": 1739803239, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed", "top_logprobs", "logprobs", "logit_bias"]}, {"id": "cognitivecomputations/dolphin3.0-r1-mistral-24b:free", "name": "Dolphin3.0 R1 Mistral 24B (free)", "description": "Dolphin 3.0 R1 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nThe R1 version has been trained for 3 epochs to reason using 800k reasoning traces from the Dolphin-R1 dataset.\n\nDolphin aims to be a general purpose reasoning instruct model, similar to the models behind <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>.\n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "context_length": 32768, "created": 1739462498, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "cognitivecomputations/dolphin3.0-mistral-24b:free", "name": "Dolphin3.0 Mistral 24B (free)", "description": "Dolphin 3.0 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nDolphin aims to be a general purpose instruct model, similar to the models behind <PERSON><PERSON>GP<PERSON>, <PERSON>, <PERSON>. \n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [BlouseJury](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "context_length": 32768, "created": 1739462019, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "meta-llama/llama-guard-3-8b", "name": "Llama Guard 3 8B", "description": "Llama Guard 3 is a Llama-3.1-8B pretrained model, fine-tuned for content safety classification. Similar to previous versions, it can be used to classify content in both LLM inputs (prompt classification) and in LLM responses (response classification). It acts as an LLM – it generates text in its output that indicates whether a given prompt or response is safe or unsafe, and if unsafe, it also lists the content categories violated.\n\nLlama Guard 3 was aligned to safeguard against the MLCommons standardized hazards taxonomy and designed to support Llama 3.1 capabilities. Specifically, it provides content moderation in 8 languages, and was optimized to support safety and security for search and code interpreter tool calls.\n", "context_length": 131072, "created": 1739401318, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "top_logprobs", "logprobs", "seed"]}, {"id": "openai/o3-mini-high", "name": "OpenAI: o3 Mini High", "description": "OpenAI o3-mini-high is the same model as [o3-mini](/openai/o3-mini) with reasoning_effort set to high. \n\no3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding. The model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "context_length": 200000, "created": 1739372611, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "deepseek/deepseek-r1-distill-llama-8b", "name": "DeepSeek: R1 Distill Llama 8B", "description": "DeepSeek R1 Distill Llama 8B is a distilled large language model based on [Llama-3.1-8B-Instruct](/meta-llama/llama-3.1-8b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 50.4\n- MATH-500 pass@1: 89.1\n- CodeForces Rating: 1205\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.\n\nHugging Face: \n- [Llama-3.1-8B](https://huggingface.co/meta-llama/Llama-3.1-8B) \n- [DeepSeek-R1-Distill-Llama-8B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-8B)   |", "context_length": 32000, "created": 1738937718, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000004", "completion": "0.00000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "google/gemini-2.0-flash-001", "name": "Google: Gemini 2.0 Flash", "description": "Gemini Flash 2.0 offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5). It introduces notable enhancements in multimodal understanding, coding capabilities, complex instruction following, and function calling. These advancements come together to deliver more seamless and robust agentic experiences.", "context_length": 1048576, "created": 1738769413, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000004", "image": "0.0000258", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "response_format", "structured_outputs"]}, {"id": "qwen/qwen-vl-plus", "name": "Qwen: Qwen VL Plus", "description": "Qwen's Enhanced Large Visual Language Model. Significantly upgraded for detailed recognition capabilities and text recognition abilities, supporting ultra-high pixel resolutions up to millions of pixels and extreme aspect ratios for image input. It delivers significant performance across a broad range of visual tasks.\n", "context_length": 7500, "created": 1738731255, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000021", "completion": "0.00000063", "image": "0.0002688", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "aion-labs/aion-1.0", "name": "AionLabs: Aion-1.0", "description": "Aion-1.0 is a multi-model system designed for high performance across various tasks, including reasoning and coding. It is built on DeepSeek-R1, augmented with additional models and techniques such as Tree of Thoughts (ToT) and Mixture of Experts (MoE). It is Aion Lab's most powerful reasoning model.", "context_length": 131072, "created": 1738697557, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000004", "completion": "0.000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning"]}, {"id": "aion-labs/aion-1.0-mini", "name": "AionLabs: Aion-1.0-Mini", "description": "Aion-1.0-Mini 32B parameter model is a distilled version of the DeepSeek-R1 model, designed for strong performance in reasoning domains such as mathematics, coding, and logic. It is a modified variant of a FuseAI model that outperforms R1-Distill-Qwen-32B and R1-Distill-Llama-70B, with benchmark results available on its [Hugging Face page](https://huggingface.co/FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview), independently replicated for verification.", "context_length": 131072, "created": 1738697107, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000007", "completion": "0.0000014", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning"]}, {"id": "aion-labs/aion-rp-llama-3.1-8b", "name": "AionLabs: Aion-RP 1.0 (8B)", "description": "Aion-RP-Llama-3.1-8B ranks the highest in the character evaluation portion of the RPBench-Auto benchmark, a roleplaying-specific variant of Arena-Hard-Auto, where LLMs evaluate each other’s responses. It is a fine-tuned base model rather than an instruct model, designed to produce more natural and varied writing.", "context_length": 32768, "created": 1738696718, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "qwen/qwen-vl-max", "name": "<PERSON>wen: <PERSON><PERSON> VL Max", "description": "Qwen VL Max is a visual understanding model with 7500 tokens context length. It excels in delivering optimal performance for a broader spectrum of complex tasks.\n", "context_length": 7500, "created": 1738434304, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000032", "image": "0.001024", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "qwen/qwen-turbo", "name": "Qwen: <PERSON><PERSON>-<PERSON>", "description": "Qwen-Turbo, based on Qwen2.5, is a 1M context model that provides fast speed and low cost, suitable for simple tasks.", "context_length": 1000000, "created": 1738410974, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.0000002", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "qwen/qwen2.5-vl-72b-instruct:free", "name": "Qwen: Qwen2.5 VL 72B Instruct (free)", "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "context_length": 131072, "created": 1738410311, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty", "stop", "frequency_penalty", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen2.5-vl-72b-instruct", "name": "Qwen: Qwen2.5 VL 72B Instruct", "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "context_length": 32000, "created": 1738410311, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.00000075", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs"]}, {"id": "qwen/qwen-plus", "name": "<PERSON>wen: <PERSON><PERSON>-Plus", "description": "Qwen-Plus, based on the Qwen2.5 foundation model, is a 131K context model with a balanced performance, speed, and cost combination.", "context_length": 131072, "created": 1738409840, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000004", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "qwen/qwen-max", "name": "<PERSON><PERSON>: <PERSON><PERSON>-<PERSON> ", "description": "Qwen-Max, based on Qwen2.5, provides the best inference performance among [Qwen models](/qwen), especially for complex multi-step tasks. It's a large-scale MoE model that has been pretrained on over 20 trillion tokens and further post-trained with curated Supervised Fine-Tuning (SFT) and Reinforcement Learning from Human Feedback (RLHF) methodologies. The parameter count is unknown.", "context_length": 32768, "created": 1738402289, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000016", "completion": "0.0000064", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "openai/o3-mini", "name": "OpenAI: o3 Mini", "description": "OpenAI o3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding.\n\nThis model supports the `reasoning_effort` parameter, which can be set to \"high\", \"medium\", or \"low\" to control the thinking time of the model. The default is \"medium\". OpenRouter also offers the model slug `openai/o3-mini-high` to default the parameter to \"high\".\n\nThe model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "context_length": 200000, "created": 1738351721, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "deepseek/deepseek-r1-distill-qwen-1.5b", "name": "DeepSeek: R1 <PERSON><PERSON><PERSON> 1.5B", "description": "DeepSeek R1 Distill Qwen 1.5B is a distilled large language model based on  [Qwen 2.5 Math 1.5B](https://huggingface.co/Qwen/Qwen2.5-Math-1.5B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It's a very small and efficient model which outperforms [GPT 4o 0513](/openai/gpt-4o-2024-05-13) on Math Benchmarks.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 28.9\n- AIME 2024 cons@64: 52.7\n- MATH-500 pass@1: 83.9\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 131072, "created": 1738328067, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000018", "completion": "0.00000018", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/mistral-small-24b-instruct-2501:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3 (free)", "description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/mistral-small-24b-instruct-2501", "name": "Mistral: Mistral Small 3", "description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "context_length": 28000, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000005", "completion": "0.00000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "tools", "tool_choice", "structured_outputs", "logit_bias", "logprobs"]}, {"id": "deepseek/deepseek-r1-distill-qwen-32b:free", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 32B (free)", "description": "DeepSeek R1 Distill Qwen 32B is a distilled large language model based on [Qwen 2.5 32B](https://huggingface.co/Qwen/Qwen2.5-32B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 72.6\n- MATH-500 pass@1: 94.3\n- CodeForces Rating: 1691\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 16000, "created": 1738194830, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning"]}, {"id": "deepseek/deepseek-r1-distill-qwen-32b", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 32B", "description": "DeepSeek R1 Distill Qwen 32B is a distilled large language model based on [Qwen 2.5 32B](https://huggingface.co/Qwen/Qwen2.5-32B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 72.6\n- MATH-500 pass@1: 94.3\n- CodeForces Rating: 1691\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 131072, "created": 1738194830, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000012", "completion": "0.00000018", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logit_bias"]}, {"id": "deepseek/deepseek-r1-distill-qwen-14b:free", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 14B (free)", "description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 64000, "created": 1738193940, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-r1-distill-qwen-14b", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 14B", "description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 64000, "created": 1738193940, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed"]}, {"id": "perplexity/sonar-reasoning", "name": "Perplexity: <PERSON><PERSON> Reasoning", "description": "Sonar Reasoning is a reasoning model provided by Perplexity based on [DeepSeek R1](/deepseek/deepseek-r1).\n\nIt allows developers to utilize long chain of thought with built-in web search. Sonar Reasoning is uncensored and hosted in US datacenters. ", "context_length": 127000, "created": 1738131107, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000005", "image": "0", "request": "0.005"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "web_search_options", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "perplexity/sonar", "name": "Perplexity: Sonar", "description": "Sonar is lightweight, affordable, fast, and simple to use — now featuring citations and the ability to customize sources. It is designed for companies seeking to integrate lightweight question-and-answer features optimized for speed.", "context_length": 127072, "created": 1738013808, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0.005"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "web_search_options", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "liquid/lfm-7b", "name": "Liquid: LFM 7B", "description": "LFM-7B, a new best-in-class language model. LFM-7B is designed for exceptional chat capabilities, including languages like Arabic and Japanese. Powered by the Liquid Foundation Model (LFM) architecture, it exhibits unique features like low memory footprint and fast inference speed. \n\nLFM-7B is the world’s best-in-class multilingual language model in English, Arabic, and Japanese.\n\nSee the [launch announcement](https://www.liquid.ai/lfm-7b) for benchmarks and more info.", "context_length": 32768, "created": 1737806883, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000001", "completion": "0.00000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "liquid/lfm-3b", "name": "Liquid: LFM 3B", "description": "Liquid's LFM 3B delivers incredible performance for its size. It positions itself as first place among 3B parameter transformers, hybrids, and RNN models It is also on par with Phi-3.5-mini on multiple benchmarks, while being 18.4% smaller.\n\nLFM-3B is the ideal choice for mobile and other edge text-based applications.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "context_length": 32768, "created": 1737806501, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty"]}, {"id": "deepseek/deepseek-r1-distill-llama-70b:free", "name": "DeepSeek: R1 Distill Llama 70B (free)", "description": "DeepSeek R1 Distill Llama 70B is a distilled large language model based on [Llama-3.3-70B-Instruct](/meta-llama/llama-3.3-70b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 70.0\n- MATH-500 pass@1: 94.5\n- CodeForces Rating: 1633\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 8192, "created": 1737663169, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs"]}, {"id": "deepseek/deepseek-r1-distill-llama-70b", "name": "DeepSeek: R1 Distill Llama 70B", "description": "DeepSeek R1 Distill Llama 70B is a distilled large language model based on [Llama-3.3-70B-Instruct](/meta-llama/llama-3.3-70b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 70.0\n- MATH-500 pass@1: 94.5\n- CodeForces Rating: 1633\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "context_length": 131072, "created": 1737663169, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "top_k", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "min_p", "repetition_penalty", "tools", "tool_choice", "response_format", "structured_outputs"]}, {"id": "deepseek/deepseek-r1:free", "name": "DeepSeek: R1 (free)", "description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "context_length": 163840, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "reasoning", "include_reasoning", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "top_a", "logprobs"]}, {"id": "deepseek/deepseek-r1", "name": "DeepSeek: R1", "description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "context_length": 163840, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.00000218", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs", "repetition_penalty", "response_format", "structured_outputs", "min_p", "tools", "tool_choice"]}, {"id": "minimax/minimax-01", "name": "MiniMax: MiniMax-01", "description": "MiniMax-01 is a combines MiniMax-Text-01 for text generation and MiniMax-VL-01 for image understanding. It has 456 billion parameters, with 45.9 billion parameters activated per inference, and can handle a context of up to 4 million tokens.\n\nThe text model adopts a hybrid architecture that combines Lightning Attention, Softmax Attention, and Mixture-of-Experts (MoE). The image model adopts the “ViT-MLP-LLM” framework and is trained on top of the text model.\n\nTo read more about the release, see: https://www.minimaxi.com/en/news/minimax-01-series-2", "context_length": 1000192, "created": 1736915462, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000011", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "mistralai/codestral-2501", "name": "Mistral: Codestral 2501", "description": "[Mistral](/mistralai)'s cutting-edge language model for coding. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation. \n\nLearn more on their blog post: https://mistral.ai/news/codestral-2501/", "context_length": 262144, "created": 1736895522, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000003", "completion": "0.0000009", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "microsoft/phi-4", "name": "Microsoft: Phi 4", "description": "[Microsoft Research](/microsoft) Phi-4 is designed to perform well in complex reasoning tasks and can operate efficiently in situations with limited memory or where quick responses are needed. \n\nAt 14 billion parameters, it was trained on a mix of high-quality synthetic datasets, data from curated websites, and academic materials. It has undergone careful improvement to follow instructions accurately and maintain strong safety standards. It works best with English language inputs.\n\nFor more information, please see [Phi-4 Technical Report](https://arxiv.org/pdf/2412.08905)\n", "context_length": 16384, "created": 1736489872, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000007", "completion": "0.00000014", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs", "repetition_penalty", "response_format", "min_p"]}, {"id": "deepseek/deepseek-chat:free", "name": "DeepSeek: DeepSeek V3 (free)", "description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.\n\nFor model details, please visit [the DeepSeek-V3 repo](https://github.com/deepseek-ai/DeepSeek-V3) for more information, or see the [launch announcement](https://api-docs.deepseek.com/news/news1226).", "context_length": 163840, "created": 1735241320, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "top_a"]}, {"id": "deepseek/deepseek-chat", "name": "DeepSeek: DeepSeek V3", "description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.\n\nFor model details, please visit [the DeepSeek-V3 repo](https://github.com/deepseek-ai/DeepSeek-V3) for more information, or see the [launch announcement](https://api-docs.deepseek.com/news/news1226).", "context_length": 163840, "created": 1735241320, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000038", "completion": "0.00000089", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logit_bias", "logprobs", "top_logprobs", "tools", "tool_choice", "structured_outputs"]}, {"id": "sao10k/l3.3-euryale-70b", "name": "Sao10K: Llama 3.3 Euryale 70B", "description": "Euryale L3.3 70B is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.2](/models/sao10k/l3-euryale-70b).", "context_length": 131072, "created": 1734535928, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000007", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "logit_bias"]}, {"id": "openai/o1", "name": "OpenAI: o1", "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding. The o1 model series is trained with large-scale reinforcement learning to reason using chain of thought. \n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n", "context_length": 200000, "created": 1734459999, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.00006", "image": "0.021675", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "eva-unit-01/eva-llama-3.33-70b", "name": "EVA Llama 3.33 70B", "description": "EVA Llama 3.33 70b is a roleplay and storywriting specialist model. It is a full-parameter finetune of [Llama-3.3-70B-Instruct](https://openrouter.ai/meta-llama/llama-3.3-70b-instruct) on mixture of synthetic and natural data.\n\nIt uses Celeste 70B 0.1 data mixture, greatly expanding it to improve versatility, creativity and \"flavor\" of the resulting model\n\nThis model was built with Llama by Meta.\n", "context_length": 16384, "created": 1734377303, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000004", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "x-ai/grok-2-vision-1212", "name": "xAI: Grok 2 Vision 1212", "description": "Grok 2 Vision 1212 advances image-based AI with stronger visual comprehension, refined instruction-following, and multilingual support. From object recognition to style analysis, it empowers developers to build more intuitive, visually aware applications. Its enhanced steerability and reasoning establish a robust foundation for next-generation image solutions.\n\nTo read more about this model, check out [xAI's announcement](https://x.ai/blog/grok-1212).", "context_length": 32768, "created": 1734237338, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.00001", "image": "0.0036", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "x-ai/grok-2-1212", "name": "xAI: Grok 2 1212", "description": "Grok 2 1212 introduces significant enhancements to accuracy, instruction adherence, and multilingual support, making it a powerful and flexible choice for developers seeking a highly steerable, intelligent model.", "context_length": 131072, "created": 1734232814, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.00001", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "cohere/command-r7b-12-2024", "name": "Cohere: Command R7B (12-2024)", "description": "Command R7B (12-2024) is a small, fast update of the Command R+ model, delivered in December 2024. It excels at RAG, tool use, agents, and similar tasks requiring complex reasoning and multiple steps.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1734158152, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000000375", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "google/gemini-2.0-flash-exp:free", "name": "Google: Gemini 2.0 Flash Experimental (free)", "description": "Gemini Flash 2.0 offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5). It introduces notable enhancements in multimodal understanding, coding capabilities, complex instruction following, and function calling. These advancements come together to deliver more seamless and robust agentic experiences.", "context_length": 1048576, "created": 1733937523, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop"]}, {"id": "meta-llama/llama-3.3-70b-instruct:free", "name": "Meta: Llama 3.3 70B Instruct (free)", "description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "context_length": 131072, "created": 1733506137, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "tools", "tool_choice", "repetition_penalty", "top_k", "logit_bias", "min_p", "response_format"]}, {"id": "meta-llama/llama-3.3-70b-instruct", "name": "Meta: Llama 3.3 70B Instruct", "description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "context_length": 131072, "created": 1733506137, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000007", "completion": "0.00000025", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "min_p", "repetition_penalty", "structured_outputs"]}, {"id": "amazon/nova-lite-v1", "name": "Amazon: Nova Lite 1.0", "description": "Amazon Nova Lite 1.0 is a very low-cost multimodal model from Amazon that focused on fast processing of image, video, and text inputs to generate text output. Amazon Nova Lite can handle real-time customer interactions, document analysis, and visual question-answering tasks with high accuracy.\n\nWith an input context of 300K tokens, it can analyze multiple images or up to 30 minutes of video in a single input.", "context_length": 300000, "created": 1733437363, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000006", "completion": "0.00000024", "image": "0.00009", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "amazon/nova-micro-v1", "name": "Amazon: Nova Micro 1.0", "description": "Amazon Nova Micro 1.0 is a text-only model that delivers the lowest latency responses in the Amazon Nova family of models at a very low cost. With a context length of 128K tokens and optimized for speed and cost, Amazon Nova Micro excels at tasks such as text summarization, translation, content classification, interactive chat, and brainstorming. It has  simple mathematical reasoning and coding abilities.", "context_length": 128000, "created": 1733437237, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000035", "completion": "0.00000014", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "amazon/nova-pro-v1", "name": "Amazon: Nova Pro 1.0", "description": "Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-art performance on key benchmarks including visual question answering (TextVQA) and video understanding (VATEX).\n\nAmazon Nova Pro demonstrates strong capabilities in processing both visual and textual information and at analyzing financial documents.\n\n**NOTE**: Video input is not supported at this time.", "context_length": 300000, "created": 1733436303, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000032", "image": "0.0012", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "qwen/qwq-32b-preview", "name": "Qwen: QwQ 32B Preview", "description": "QwQ-32B-Preview is an experimental research model focused on AI reasoning capabilities developed by the Qwen Team. As a preview release, it demonstrates promising analytical abilities while having several important limitations:\n\n1. **Language Mixing and Code-Switching**: The model may mix languages or switch between them unexpectedly, affecting response clarity.\n2. **Recursive Reasoning Loops**: The model may enter circular reasoning patterns, leading to lengthy responses without a conclusive answer.\n3. **Safety and Ethical Considerations**: The model requires enhanced safety measures to ensure reliable and secure performance, and users should exercise caution when deploying it.\n4. **Performance and Benchmark Limitations**: The model excels in math and coding but has room for improvement in other areas, such as common sense reasoning and nuanced language understanding.\n\n", "context_length": 32768, "created": 1732754541, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000009", "completion": "0.00000027", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "logprobs", "top_logprobs", "seed", "logit_bias", "top_k", "min_p", "repetition_penalty"]}, {"id": "eva-unit-01/eva-qwen-2.5-72b", "name": "EVA Qwen2.5 72B", "description": "EVA Qwen2.5 72B is a roleplay and storywriting specialist model. It's a full-parameter finetune of Qwen2.5-72B on mixture of synthetic and natural data.\n\nIt uses Celeste 70B 0.1 data mixture, greatly expanding it to improve versatility, creativity and \"flavor\" of the resulting model.", "context_length": 16384, "created": 1732210606, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000004", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "openai/gpt-4o-2024-11-20", "name": "OpenAI: GPT-4o (2024-11-20)", "description": "The 2024-11-20 version of GPT-4o offers a leveled-up creative writing ability with more natural, engaging, and tailored writing to improve relevance & readability. It’s also better at working with uploaded files, providing deeper insights & more thorough responses.\n\nGPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.", "context_length": 128000, "created": 1732127594, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0.003613", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "mistralai/mistral-large-2411", "name": "Mistral Large 2411", "description": "Mistral Large 2 2411 is an update of [Mistral Large 2](/mistralai/mistral-large) released together with [Pixtral Large 2411](/mistralai/pixtral-large-2411)\n\nIt provides a significant upgrade on the previous [Mistral Large 24.07](/mistralai/mistral-large-2407), with notable improvements in long context understanding, a new system prompt, and more accurate function calling.", "context_length": 131072, "created": 1731978685, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/mistral-large-2407", "name": "Mistral Large 2407", "description": "This is Mistral AI's flagship model, Mistral Large 2 (version mistral-large-2407). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.\n", "context_length": 131072, "created": 1731978415, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/pixtral-large-2411", "name": "Mistral: Pixtral Large 2411", "description": "Pixtral Large is a 124B parameter, open-weight, multimodal model built on top of [Mistral Large 2](/mistralai/mistral-large-2411). The model is able to understand documents, charts and natural images.\n\nThe model is available under the Mistral Research License (MRL) for research and educational use, and the Mistral Commercial License for experimentation, testing, and production for commercial purposes.\n\n", "context_length": 131072, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000006", "image": "0.002888", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "x-ai/grok-vision-beta", "name": "xAI: Grok Vision Beta", "description": "Grok Vision Beta is xAI's experimental language model with vision capability.\n\n", "context_length": 8192, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.009", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "infermatic/mn-inferor-12b", "name": "Infermatic: Mistral Nemo Inferor 12B", "description": "Inferor 12B is a merge of top roleplay models, expert on immersive narratives and storytelling.\n\nThis model was merged using the [Model Stock](https://arxiv.org/abs/2403.19522) merge method using [anthracite-org/magnum-v4-12b](https://openrouter.ai/anthracite-org/magnum-v4-72b) as a base.\n", "context_length": 16384, "created": 1731464428, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "qwen/qwen-2.5-coder-32b-instruct:free", "name": "Qwen2.5 Coder 32B Instruct (free)", "description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "context_length": 32768, "created": 1731368400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen-2.5-coder-32b-instruct", "name": "Qwen2.5 Coder 32B Instruct", "description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "context_length": 32768, "created": 1731368400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000006", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs", "top_a"]}, {"id": "raifle/sorcererlm-8x22b", "name": "SorcererLM 8x22B", "description": "SorcererLM is an advanced RP and storytelling model, built as a Low-rank 16-bit LoRA fine-tuned on [WizardLM-2 8x22B](/microsoft/wizardlm-2-8x22b).\n\n- Advanced reasoning and emotional intelligence for engaging and immersive interactions\n- Vivid writing capabilities enriched with spatial and contextual awareness\n- Enhanced narrative depth, promoting creative and dynamic storytelling", "context_length": 16000, "created": 1731105083, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000045", "completion": "0.0000045", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed"]}, {"id": "eva-unit-01/eva-qwen-2.5-32b", "name": "EVA Qwen2.5 32B", "description": "EVA Qwen2.5 32B is a roleplaying/storywriting specialist model. It's a full-parameter finetune of Qwen2.5-32B on mixture of synthetic and natural data.\n\nIt uses Celeste 70B 0.1 data mixture, greatly expanding it to improve versatility, creativity and \"flavor\" of the resulting model.", "context_length": 16384, "created": 1731104847, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000026", "completion": "0.0000034", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "thedrummer/unslopnemo-12b", "name": "Unslopnemo 12B", "description": "UnslopNemo v4.1 is the latest addition from the creator of Rocinante, designed for adventure writing and role-play scenarios.", "context_length": 32000, "created": 1731103448, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000045", "completion": "0.00000045", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "logprobs"]}, {"id": "anthropic/claude-3.5-haiku:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "context_length": 200000, "created": 1730678400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3.5-haiku", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "context_length": 200000, "created": 1730678400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3.5-haiku-20241022:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-10-22) (self-moderated)", "description": "Claude 3.5 Haiku features enhancements across all skill sets including coding, tool use, and reasoning. As the fastest model in the Anthropic lineup, it offers rapid response times suitable for applications that require high interactivity and low latency, such as user-facing chatbots and on-the-fly code completions. It also excels in specialized tasks like data extraction and real-time content moderation, making it a versatile tool for a broad range of industries.\n\nIt does not support image inputs.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/3-5-models-and-computer-use)", "context_length": 200000, "created": 1730678400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3.5-haiku-20241022", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-10-22)", "description": "Claude 3.5 Haiku features enhancements across all skill sets including coding, tool use, and reasoning. As the fastest model in the Anthropic lineup, it offers rapid response times suitable for applications that require high interactivity and low latency, such as user-facing chatbots and on-the-fly code completions. It also excels in specialized tasks like data extraction and real-time content moderation, making it a versatile tool for a broad range of industries.\n\nIt does not support image inputs.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/3-5-models-and-computer-use)", "context_length": 200000, "created": 1730678400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "neversleep/llama-3.1-lumimaid-70b", "name": "NeverSleep: Lumimaid v0.2 70B", "description": "Lumimaid v0.2 70B is a finetune of [Llama 3.1 70B](/meta-llama/llama-3.1-70b-instruct) with a \"HUGE step up dataset wise\" compared to Lumimaid v0.1. Sloppy chats output were purged.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 16384, "created": 1729555200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "anthracite-org/magnum-v4-72b", "name": "Magnum v4 72B", "description": "This is a series of models designed to replicate the prose quality of the Claude 3 models, specifically Sonnet(https://openrouter.ai/anthropic/claude-3.5-sonnet) and Opus(https://openrouter.ai/anthropic/claude-3-opus).\n\nThe model is fine-tuned on top of [Qwen2.5 72B](https://openrouter.ai/qwen/qwen-2.5-72b-instruct).", "context_length": 16384, "created": 1729555200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed", "logit_bias", "top_a"]}, {"id": "anthropic/claude-3.5-sonnet:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "context_length": 200000, "created": 1729555200, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3.5-sonnet", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "context_length": 200000, "created": 1729555200, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "x-ai/grok-beta", "name": "xAI: Grok Beta", "description": "Grok Beta is xAI's experimental language model with state-of-the-art reasoning capabilities, best for complex and multi-step use cases.\n\nIt is the successor of [Grok 2](https://x.ai/blog/grok-2) with enhanced context length.", "context_length": 131072, "created": 1729382400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "mistralai/ministral-8b", "name": "Mistral: Ministral 8B", "description": "Ministral 8B is an 8B parameter model featuring a unique interleaved sliding-window attention pattern for faster, memory-efficient inference. Designed for edge use cases, it supports up to 128k context length and excels in knowledge and reasoning tasks. It outperforms peers in the sub-10B category, making it perfect for low-latency, privacy-first applications.", "context_length": 128000, "created": 1729123200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/ministral-3b", "name": "Mistral: Ministral 3B", "description": "Ministral 3B is a 3B parameter model optimized for on-device and edge computing. It excels in knowledge, commonsense reasoning, and function-calling, outperforming larger models like Mistral 7B on most benchmarks. Supporting up to 128k context length, it’s ideal for orchestrating agentic workflows and specialist tasks with efficient inference.", "context_length": 131072, "created": 1729123200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000004", "completion": "0.00000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "qwen/qwen-2.5-7b-instruct:free", "name": "Qwen2.5 7B Instruct (free)", "description": "Qwen2.5 7B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "qwen/qwen-2.5-7b-instruct", "name": "Qwen2.5 7B Instruct", "description": "Qwen2.5 7B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000004", "completion": "0.0000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "structured_outputs", "seed"]}, {"id": "nvidia/llama-3.1-nemotron-70b-instruct", "name": "NVIDIA: Llama 3.1 Nemotron 70B Instruct", "description": "NVIDIA's Llama 3.1 Nemotron 70B is a language model designed for generating precise and useful responses. Leveraging [Llama 3.1 70B](/models/meta-llama/llama-3.1-70b-instruct) architecture and Reinforcement Learning from Human Feedback (RLHF), it excels in automatic alignment benchmarks. This model is tailored for applications requiring high accuracy in helpfulness and response generation, suitable for diverse user queries across multiple domains.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1728950400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000012", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "repetition_penalty", "min_p"]}, {"id": "inflection/inflection-3-productivity", "name": "Inflection: Inflection 3 Productivity", "description": "Inflection 3 Productivity is optimized for following instructions. It is better for tasks requiring JSON output or precise adherence to provided guidelines. It has access to recent news.\n\nFor emotional intelligence similar to Pi, see [Inflect 3 Pi](/inflection/inflection-3-pi)\n\nSee [Inflection's announcement](https://inflection.ai/blog/enterprise) for more details.", "context_length": 8000, "created": 1728604800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop"]}, {"id": "inflection/inflection-3-pi", "name": "Inflection: Inflection 3 Pi", "description": "Inflection 3 Pi powers Inflection's [<PERSON>](https://pi.ai) chatbot, including backstory, emotional intelligence, productivity, and safety. It has access to recent news, and excels in scenarios like customer support and roleplay.\n\n<PERSON> has been trained to mirror your tone and style, if you use more emojis, so will Pi! Try experimenting with various prompts and conversation styles.", "context_length": 8000, "created": 1728604800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop"]}, {"id": "google/gemini-flash-1.5-8b", "name": "Google: Gemini 1.5 Flash 8B", "description": "Gemini Flash 1.5 8B is optimized for speed and efficiency, offering enhanced performance in small prompt tasks like chat, transcription, and translation. With reduced latency, it is highly effective for real-time and large-scale operations. This model focuses on cost-effective solutions while maintaining high-quality results.\n\n[Click here to learn more about this model](https://developers.googleblog.com/en/gemini-15-flash-8b-is-now-generally-available-for-use/).\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).", "context_length": 1000000, "created": 1727913600, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000000375", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "tools", "tool_choice", "seed", "response_format", "structured_outputs"]}, {"id": "thedrummer/rocinante-12b", "name": "Rocinante 12B", "description": "Rocinante 12B is designed for engaging storytelling and rich prose.\n\nEarly testers have reported:\n- Expanded vocabulary with unique and expressive word choices\n- Enhanced creativity for vivid narratives\n- Adventure-filled and captivating stories", "context_length": 32768, "created": 1727654400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.0000005", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed", "logit_bias"]}, {"id": "anthracite-org/magnum-v2-72b", "name": "Magnum v2 72B", "description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the seventh in a family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "context_length": 32768, "created": 1727654400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed"]}, {"id": "liquid/lfm-40b", "name": "Liquid: LFM 40B MoE", "description": "Liquid's 40.3B Mixture of Experts (MoE) model. Liquid Foundation Models (LFMs) are large neural networks built with computational units rooted in dynamic systems.\n\nLFMs are general-purpose AI models that can be used to model any kind of sequential data, including video, audio, text, time series, and signals.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "context_length": 32768, "created": 1727654400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.00000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "meta-llama/llama-3.2-3b-instruct:free", "name": "Meta: Llama 3.2 3B Instruct (free)", "description": "Llama 3.2 3B is a 3-billion-parameter multilingual large language model, optimized for advanced natural language processing tasks like dialogue generation, reasoning, and summarization. Designed with the latest transformer architecture, it supports eight languages, including English, Spanish, and Hindi, and is adaptable for additional languages.\n\nTrained on 9 trillion tokens, the Llama 3.2 3B model excels in instruction-following, complex reasoning, and tool use. Its balanced performance makes it ideal for applications needing accuracy and efficiency in text generation across multilingual settings.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 20000, "created": 1727222400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "meta-llama/llama-3.2-3b-instruct", "name": "Meta: Llama 3.2 3B Instruct", "description": "Llama 3.2 3B is a 3-billion-parameter multilingual large language model, optimized for advanced natural language processing tasks like dialogue generation, reasoning, and summarization. Designed with the latest transformer architecture, it supports eight languages, including English, Spanish, and Hindi, and is adaptable for additional languages.\n\nTrained on 9 trillion tokens, the Llama 3.2 3B model excels in instruction-following, complex reasoning, and tool use. Its balanced performance makes it ideal for applications needing accuracy and efficiency in text generation across multilingual settings.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000001", "completion": "0.00000002", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "min_p", "repetition_penalty"]}, {"id": "meta-llama/llama-3.2-1b-instruct:free", "name": "Meta: Llama 3.2 1B Instruct (free)", "description": "Llama 3.2 1B is a 1-billion-parameter language model focused on efficiently performing natural language tasks, such as summarization, dialogue, and multilingual text analysis. Its smaller size allows it to operate efficiently in low-resource environments while maintaining strong task performance.\n\nSupporting eight core languages and fine-tunable for more, Llama 1.3B is ideal for businesses or developers seeking lightweight yet powerful AI solutions that can operate in diverse multilingual settings without the high computational demand of larger models.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "meta-llama/llama-3.2-1b-instruct", "name": "Meta: Llama 3.2 1B Instruct", "description": "Llama 3.2 1B is a 1-billion-parameter language model focused on efficiently performing natural language tasks, such as summarization, dialogue, and multilingual text analysis. Its smaller size allows it to operate efficiently in low-resource environments while maintaining strong task performance.\n\nSupporting eight core languages and fine-tunable for more, Llama 1.3B is ideal for businesses or developers seeking lightweight yet powerful AI solutions that can operate in diverse multilingual settings without the high computational demand of larger models.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000005", "completion": "0.00000001", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "seed", "min_p", "logit_bias", "top_logprobs", "logprobs"]}, {"id": "meta-llama/llama-3.2-90b-vision-instruct", "name": "Meta: Llama 3.2 90B Vision Instruct", "description": "The Llama 90B Vision model is a top-tier, 90-billion-parameter multimodal model designed for the most challenging visual reasoning and language tasks. It offers unparalleled accuracy in image captioning, visual question answering, and advanced image-text comprehension. Pre-trained on vast multimodal datasets and fine-tuned with human feedback, the Llama 90B Vision is engineered to handle the most demanding image-based AI tasks.\n\nThis model is perfect for industries requiring cutting-edge multimodal AI capabilities, particularly those dealing with complex, real-time visual and textual analysis.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000012", "completion": "0.0000012", "image": "0.001734", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed"]}, {"id": "meta-llama/llama-3.2-11b-vision-instruct:free", "name": "Meta: Llama 3.2 11B Vision Instruct (free)", "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis.\n\nIts ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "meta-llama/llama-3.2-11b-vision-instruct", "name": "Meta: Llama 3.2 11B Vision Instruct", "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis.\n\nIts ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "created": 1727222400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000049", "completion": "0.000000049", "image": "0.00007948", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format", "top_logprobs", "tools", "tool_choice", "logprobs"]}, {"id": "qwen/qwen-2.5-72b-instruct:free", "name": "Qwen2.5 72B Instruct (free)", "description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen-2.5-72b-instruct", "name": "Qwen2.5 72B Instruct", "description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000012", "completion": "0.00000039", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "seed", "min_p"]}, {"id": "neversleep/llama-3.1-lumimaid-8b", "name": "NeverSleep: Lumimaid v0.2 8B", "description": "Lumimaid v0.2 8B is a finetune of [Llama 3.1 8B](/models/meta-llama/llama-3.1-8b-instruct) with a \"HUGE step up dataset wise\" compared to Lumimaid v0.1. Sloppy chats output were purged.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.00000125", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed", "logit_bias", "top_a"]}, {"id": "openai/o1-preview", "name": "OpenAI: o1-preview", "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "context_length": 128000, "created": 1726099200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.00006", "image": "0", "request": "0"}, "supported_parameters": ["seed", "max_tokens"]}, {"id": "openai/o1-preview-2024-09-12", "name": "OpenAI: o1-preview (2024-09-12)", "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "context_length": 128000, "created": 1726099200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.00006", "image": "0", "request": "0"}, "supported_parameters": ["seed", "max_tokens"]}, {"id": "openai/o1-mini", "name": "OpenAI: o1-mini", "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "context_length": 128000, "created": 1726099200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0", "request": "0"}, "supported_parameters": ["seed", "max_tokens"]}, {"id": "openai/o1-mini-2024-09-12", "name": "OpenAI: o1-mini (2024-09-12)", "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "context_length": 128000, "created": 1726099200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000011", "completion": "0.0000044", "image": "0", "request": "0"}, "supported_parameters": ["seed", "max_tokens"]}, {"id": "mistralai/pixtral-12b", "name": "Mistral: Pixtral 12B", "description": "The first multi-modal, text+image-to-text model from Mistral AI. Its weights were launched via torrent: https://x.com/mistralai/status/1833758285167722836.", "context_length": 32768, "created": 1725926400, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0.0001445", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "logprobs", "top_logprobs", "seed", "logit_bias", "top_k", "min_p", "repetition_penalty", "tools", "tool_choice", "response_format", "structured_outputs"]}, {"id": "cohere/command-r-plus-08-2024", "name": "Cohere: Command R+ (08-2024)", "description": "command-r-plus-08-2024 is an update of the [Command R+](/models/cohere/command-r-plus) with roughly 50% higher throughput and 25% lower latencies as compared to the previous Command R+ version, while keeping the hardware footprint the same.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1724976000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "cohere/command-r-08-2024", "name": "Cohere: Command R (08-2024)", "description": "command-r-08-2024 is an update of the [Command R](/models/cohere/command-r) with improved performance for multilingual retrieval-augmented generation (RAG) and tool use. More broadly, it is better at math, code and reasoning and is competitive with the previous version of the larger Command R+ model.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1724976000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "qwen/qwen-2.5-vl-7b-instruct:free", "name": "Qwen: Qwen2.5-VL 7B Instruct (free)", "description": "Qwen2.5 VL 7B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Understanding videos of 20min+: Qwen2.5-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen-2.5-vl-7b-instruct", "name": "Qwen: Qwen2.5-VL 7B Instruct", "description": "Qwen2.5 VL 7B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Understanding videos of 20min+: Qwen2.5-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0.0001445", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs", "min_p", "seed"]}, {"id": "sao10k/l3.1-euryale-70b", "name": "Sao10K: Llama 3.1 Euryale 70B v2.2", "description": "Euryale L3.1 70B v2.2 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.1](/models/sao10k/l3-euryale-70b).", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000007", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format"]}, {"id": "microsoft/phi-3.5-mini-128k-instruct", "name": "Microsoft: Phi-3.5 Mini 128K Instruct", "description": "Phi-3.5 models are lightweight, state-of-the-art open models. These models were trained with Phi-3 datasets that include both synthetic data and the filtered, publicly available websites data, with a focus on high quality and reasoning-dense properties. Phi-3.5 Mini uses 3.8B parameters, and is a dense decoder-only transformer model using the same tokenizer as [Phi-3 Mini](/models/microsoft/phi-3-mini-128k-instruct).\n\nThe models underwent a rigorous enhancement process, incorporating both supervised fine-tuning, proximal policy optimization, and direct preference optimization to ensure precise instruction adherence and robust safety measures. When assessed against benchmarks that test common sense, language understanding, math, code, long context and logical reasoning, Phi-3.5 models showcased robust and state-of-the-art performance among models with less than 13 billion parameters.", "context_length": 131072, "created": 1724198400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000003", "completion": "0.00000009", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "nousresearch/hermes-3-llama-3.1-70b", "name": "Nous: Hermes 3 70B Instruct", "description": "Hermes 3 is a generalist language model with many improvements over [Hermes 2](/models/nousresearch/nous-hermes-2-mistral-7b-dpo), including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 70B is a competitive, if not superior finetune of the [Llama-3.1 70B foundation model](/models/meta-llama/llama-3.1-70b-instruct), focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.", "context_length": 131072, "created": 1723939200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000012", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "min_p", "repetition_penalty", "tools", "tool_choice"]}, {"id": "nousresearch/hermes-3-llama-3.1-405b", "name": "Nous: Hermes 3 405B Instruct", "description": "Hermes 3 is a generalist language model with many improvements over Hermes 2, including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 405B is a frontier-level, full-parameter finetune of the Llama-3.1 405B foundation model, focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.\n\nHermes 3 is competitive, if not superior, to Llama-3.1 Instruct models at general capabilities, with varying strengths and weaknesses attributable between the two.", "context_length": 131072, "created": 1723766400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000007", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "repetition_penalty", "min_p"]}, {"id": "openai/chatgpt-4o-latest", "name": "OpenAI: ChatGPT-4o", "description": "OpenAI ChatGPT 4o is continually updated by OpenAI to point to the current version of GPT-4o used by ChatGPT. It therefore differs slightly from the API version of [GPT-4o](/models/openai/gpt-4o) in that it has additional RLHF. It is intended for research and evaluation.\n\nOpenAI notes that this model is not suited for production use-cases as it may be removed or redirected to another model in the future.", "context_length": 128000, "created": 1723593600, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.007225", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "sao10k/l3-lunaris-8b", "name": "Sao10K: Llama 3 8B Lunaris", "description": "Lunaris 8B is a versatile generalist and roleplaying model based on Llama 3. It's a strategic merge of multiple models, designed to balance creativity with improved logic and general knowledge.\n\nCreated by [Sao10k](https://huggingface.co/Sao10k), this model aims to offer an improved experience over Stheno v3.2, with enhanced creativity and logical reasoning.\n\nFor best results, use with Llama 3 Instruct context template, temperature 1.4, and min_p 0.1.", "context_length": 8192, "created": 1723507200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000005", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format"]}, {"id": "aetherwiing/mn-starcannon-12b", "name": "Aetherwiing: Starcannon 12B", "description": "Starcannon 12B v2 is a creative roleplay and story writing model, based on Mistral Nemo, using [nothingiisreal/mn-celeste-12b](/nothingiisreal/mn-celeste-12b) as a base, with [intervitens/mini-magnum-12b-v1.1](https://huggingface.co/intervitens/mini-magnum-12b-v1.1) merged in using the [TIES](https://arxiv.org/abs/2306.01708) method.\n\nAlthough more similar to Magnum overall, the model remains very creative, with a pleasant writing style. It is recommended for people wanting more variety than <PERSON>, and yet more verbose prose than <PERSON>.", "context_length": 16384, "created": 1723507200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "openai/gpt-4o-2024-08-06", "name": "OpenAI: GPT-4o (2024-08-06)", "description": "The 2024-08-06 version of GPT-4o offers improved performance in structured outputs, with the ability to supply a JSON schema in the respone_format. Read more [here](https://openai.com/index/introducing-structured-outputs-in-the-api/).\n\nGPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)", "context_length": 128000, "created": 1722902400, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0.003613", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "meta-llama/llama-3.1-405b:free", "name": "Meta: Llama 3.1 405B (base) (free)", "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This is the base 405B pre-trained version.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 64000, "created": 1722556800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "meta-llama/llama-3.1-405b", "name": "Meta: Llama 3.1 405B (base)", "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This is the base 405B pre-trained version.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 32768, "created": 1722556800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "logprobs", "top_logprobs", "seed", "logit_bias", "top_k", "min_p", "repetition_penalty"]}, {"id": "nothingiisreal/mn-celeste-12b", "name": "Mistral Nemo 12B Celeste", "description": "A specialized story writing and roleplaying model based on Mistral's NeMo 12B Instruct. Fine-tuned on curated datasets including Reddit Writing Prompts and Opus Instruct 25K.\n\nThis model excels at creative writing, offering improved NSFW capabilities, with smarter and more active narration. It demonstrates remarkable versatility in both SFW and NSFW scenarios, with strong Out of Character (OOC) steering capabilities, allowing fine-tuned control over narrative direction and character behavior.\n\nCheck out the model's [HuggingFace page](https://huggingface.co/nothingiisreal/MN-12B-Celeste-V1.9) for details on what parameters and prompts work best!", "context_length": 16384, "created": 1722556800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "perplexity/llama-3.1-sonar-small-128k-online", "name": "Perplexity: Llama 3.1 Sonar 8B Online", "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3.1-sonar-small-128k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 127072, "created": 1722470400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0.005"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "perplexity/llama-3.1-sonar-large-128k-online", "name": "Perplexity: Llama 3.1 Sonar 70B Online", "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3.1-sonar-large-128k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 127072, "created": 1722470400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0.005"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "frequency_penalty", "presence_penalty"]}, {"id": "meta-llama/llama-3.1-8b-instruct:free", "name": "Meta: Llama 3.1 8B Instruct (free)", "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 8B instruct-tuned version is fast and efficient.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "created": 1721692800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "meta-llama/llama-3.1-8b-instruct", "name": "Meta: Llama 3.1 8B Instruct", "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 8B instruct-tuned version is fast and efficient.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 16384, "created": 1721692800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "repetition_penalty", "structured_outputs", "min_p"]}, {"id": "meta-llama/llama-3.1-405b-instruct", "name": "Meta: Llama 3.1 405B Instruct", "description": "The highly anticipated 400B class of Llama3 is here! Clocking in at 128k context with impressive eval scores, the Meta AI team continues to push the frontier of open-source LLMs.\n\nMeta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 405B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models including GPT-4o and Claude 3.5 Sonnet in evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 32768, "created": 1721692800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "min_p", "seed"]}, {"id": "meta-llama/llama-3.1-70b-instruct", "name": "Meta: Llama 3.1 70B Instruct", "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 70B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "created": 1721692800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.00000028", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs", "structured_outputs"]}, {"id": "mistralai/codestral-mamba", "name": "Mistral: Codestral Mamba", "description": "A 7.3B parameter Mamba-based model designed for code and reasoning tasks.\n\n- Linear time inference, allowing for theoretically infinite sequence lengths\n- 256k token context window\n- Optimized for quick responses, especially beneficial for code productivity\n- Performs comparably to state-of-the-art transformer models in code and reasoning tasks\n- Available under the Apache 2.0 license for free use, modification, and distribution", "context_length": 262144, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.00000025", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "seed"]}, {"id": "mistralai/mistral-nemo:free", "name": "Mistral: <PERSON><PERSON><PERSON> (free)", "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/mistral-nemo", "name": "Mistral: <PERSON><PERSON><PERSON> Nemo", "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000007", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs", "tools", "tool_choice", "response_format", "structured_outputs", "repetition_penalty", "min_p"]}, {"id": "openai/gpt-4o-mini", "name": "OpenAI: GPT-4o-mini", "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.\n\n#multimodal", "context_length": 128000, "created": 1721260800, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.000217", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs", "tools", "tool_choice"]}, {"id": "openai/gpt-4o-mini-2024-07-18", "name": "OpenAI: GPT-4o-mini (2024-07-18)", "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.\n\n#multimodal", "context_length": 128000, "created": 1721260800, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.007225", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "google/gemma-2-27b-it", "name": "Google: Gemma 2 27B", "description": "Gemma 2 27B by Google is an open model built from the same research and technology used to create the [Gemini models](/models?q=gemini).\n\nGemma models are well-suited for a variety of text generation tasks, including question answering, summarization, and reasoning.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "context_length": 8192, "created": 1720828800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs"]}, {"id": "alpindale/magnum-72b", "name": "Magnum 72B", "description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the first in a new family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "context_length": 16384, "created": 1720656000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000004", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "google/gemma-2-9b-it:free", "name": "Google: <PERSON> 2 9B (free)", "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "context_length": 8192, "created": 1719532800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemma-2-9b-it", "name": "Google: Gemma 2 9B", "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "context_length": 8192, "created": 1719532800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000002", "completion": "0.00000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format", "top_logprobs", "logprobs"]}, {"id": "01-ai/yi-large", "name": "01.AI: <PERSON>", "description": "The Yi Large model was designed by 01.AI with the following usecases in mind: knowledge search, data classification, human-like chat bots, and customer service.\n\nIt stands out for its multilingual proficiency, particularly in Spanish, Chinese, Japanese, German, and French.\n\nCheck out the [launch announcement](https://01-ai.github.io/blog/01.ai-yi-large-llm-launch) to learn more.", "context_length": 32768, "created": 1719273600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000003", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "anthropic/claude-3.5-sonnet-20240620:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON> (2024-06-20) (self-moderated)", "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\nFor the latest version (2024-10-23), check out [Claude 3.5 Sonnet](/anthropic/claude-3.5-sonnet).\n\n#multimodal", "context_length": 200000, "created": 1718841600, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3.5-sonnet-20240620", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-06-20)", "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\nFor the latest version (2024-10-23), check out [Claude 3.5 Sonnet](/anthropic/claude-3.5-sonnet).\n\n#multimodal", "context_length": 200000, "created": 1718841600, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "sao10k/l3-euryale-70b", "name": "Sao10k: Llama 3 Euryale 70B v2.1", "description": "Euryale 70B v2.1 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k).\n\n- Better prompt adherence.\n- Better anatomy / spatial awareness.\n- Adapts much better to unique and custom formatting / reply formats.\n- Very creative, lots of unique swipes.\n- Is not restrictive during roleplays.", "context_length": 8192, "created": 1718668800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000148", "completion": "0.00000148", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "cognitivecomputations/dolphin-mixtral-8x22b", "name": "Dolphin 2.9.2 Mixtral 8x22B 🐬", "description": "Dolphin 2.9 is designed for instruction following, conversational, and coding. This model is a finetune of [Mixtral 8x22B Instruct](/models/mistralai/mixtral-8x22b-instruct). It features a 64k context length and was fine-tuned with a 16k sequence length using ChatML templates.\n\nThis model is a successor to [Dolphin Mixtral 8x7B](/models/cognitivecomputations/dolphin-mixtral-8x7b).\n\nThe model is uncensored and is stripped of alignment and bias. It requires an external alignment layer for ethical use. Users are cautioned to use this highly compliant model responsibly, as detailed in a blog post about uncensored models at [erichartford.com/uncensored-models](https://erichartford.com/uncensored-models).\n\n#moe #uncensored", "context_length": 16000, "created": 1717804800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "qwen/qwen-2-72b-instruct", "name": "Qwen 2 72B Instruct", "description": "Qwen2 72B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral: Mistral 7B Instruct (free)", "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p"]}, {"id": "mistral<PERSON>/mistral-7b-instruct", "name": "Mistral: Mistral 7B Instruct", "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000028", "completion": "0.000000054", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "tools", "tool_choice", "structured_outputs"]}, {"id": "nousresearch/hermes-2-pro-llama-3-8b", "name": "NousResearch: Hermes 2 Pro - Llama-3 8B", "description": "Hermes 2 Pro is an upgraded, retrained version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house.", "context_length": 131072, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000025", "completion": "0.00000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "top_k", "min_p", "repetition_penalty"]}, {"id": "mistralai/mistral-7b-instruct-v0.3", "name": "Mistral: Mistral 7B Instruct v0.3", "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct v0.2](/models/mistralai/mistral-7b-instruct-v0.2), with the following changes:\n\n- Extended vocabulary to 32768\n- Supports v3 Tokenizer\n- Supports function calling\n\nNOTE: Support for function calling depends on the provider.", "context_length": 32768, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000028", "completion": "0.000000054", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "tools", "tool_choice", "logprobs", "structured_outputs"]}, {"id": "microsoft/phi-3-mini-128k-instruct", "name": "Microsoft: Phi-3 Mini 128K Instruct", "description": "Phi-3 Mini is a powerful 3.8B parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. This model is static, trained on an offline dataset with an October 2023 cutoff date.", "context_length": 128000, "created": 1716681600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p"]}, {"id": "microsoft/phi-3-medium-128k-instruct", "name": "Microsoft: Phi-3 Medium 128K Instruct", "description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).", "context_length": 131072, "created": 1716508800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000001", "completion": "0.0000003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "neversleep/llama-3-lumimaid-70b", "name": "NeverSleep: Llama 3 Lumimaid 70B", "description": "The NeverSleep team is back, with a Llama 3 70B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "created": 1715817600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000004", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "deepseek/deepseek-coder", "name": "DeepSeek-Coder-V2", "description": "DeepSeek-Coder-V2, an open-source Mixture-of-Experts (MoE) code language model. It is further pre-trained from an intermediate checkpoint of DeepSeek-V2 with additional 6 trillion tokens.\n\nThe original V1 model was trained from scratch on 2T tokens, with a composition of 87% code and 13% natural language in both English and Chinese. It was pre-trained on project-level code corpus by employing a extra fill-in-the-blank task.", "context_length": 128000, "created": 1715644800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000004", "completion": "0.00000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "google/gemini-flash-1.5", "name": "Google: Gemini 1.5 Flash ", "description": "Gemini 1.5 Flash is a foundation model that performs well at a variety of multimodal tasks such as visual understanding, classification, summarization, and creating content from image, audio and video. It's adept at processing visual and text inputs such as photographs, documents, infographics, and screenshots.\n\nGemini 1.5 Flash is designed for high-volume, high-frequency tasks where cost and latency matter. On most common tasks, Flash achieves comparable quality to other Gemini Pro models at a significantly reduced cost. Flash is well-suited for applications like chat assistants and on-demand content generation where speed and scale matter.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "context_length": 1000000, "created": 1715644800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000075", "completion": "0.0000003", "image": "0.00004", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "tools", "tool_choice", "seed", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4o", "name": "OpenAI: GPT-4o", "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "context_length": 128000, "created": 1715558400, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0.003613", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4o:extended", "name": "OpenAI: GPT-4o (extended)", "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "context_length": 128000, "created": 1715558400, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.000006", "completion": "0.000018", "image": "0.007225", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "meta-llama/llama-guard-2-8b", "name": "Meta: LlamaGuard 2 8B", "description": "This safeguard model has 8B parameters and is based on the Llama 3 family. Just like is predecessor, [LlamaGuard 1](https://huggingface.co/meta-llama/LlamaGuard-7b), it can do both prompt and response classification.\n\nLlamaGuard 2 acts as a normal LLM would, generating text that indicates whether the given input/output is safe/unsafe. If deemed unsafe, it will also share the content categories violated.\n\nFor best results, please use raw prompt input or the `/completions` endpoint, instead of the chat API.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "created": 1715558400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "openai/gpt-4o-2024-05-13", "name": "OpenAI: GPT-4o (2024-05-13)", "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "context_length": 128000, "created": 1715558400, "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.007225", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "web_search_options", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "allenai/olmo-7b-instruct", "name": "OLMo 7B Instruct", "description": "OLMo 7B Instruct by the Allen Institute for AI is a model finetuned for question answering. It demonstrates **notable performance** across multiple benchmarks including TruthfulQA and ToxiGen.\n\n**Open Source**: The model, its code, checkpoints, logs are released under the [Apache 2.0 license](https://choosealicense.com/licenses/apache-2.0).\n\n- [Core repo (training, inference, fine-tuning etc.)](https://github.com/allenai/OLMo)\n- [Evaluation code](https://github.com/allenai/OLMo-Eval)\n- [Further fine-tuning code](https://github.com/allenai/open-instruct)\n- [Paper](https://arxiv.org/abs/2402.00838)\n- [Technical blog post](https://blog.allenai.org/olmo-open-language-model-87ccfc95f580)\n- [W&B Logs](https://wandb.ai/ai2-llm/OLMo-7B/reports/OLMo-7B--Vmlldzo2NzQyMzk5)", "context_length": 2048, "created": **********, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000008", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "neversleep/llama-3-lumimaid-8b:extended", "name": "NeverSleep: Llama 3 Lumimaid 8B (extended)", "description": "The NeverSleep team is back, with a Llama 3 8B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 24576, "created": 1714780800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.00000125", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "neversleep/llama-3-lumimaid-8b", "name": "NeverSleep: Llama 3 Lumimaid 8B", "description": "The NeverSleep team is back, with a Llama 3 8B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 24576, "created": 1714780800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.00000125", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed", "logit_bias", "top_a"]}, {"id": "sao10k/fimbulvetr-11b-v2", "name": "Fimbulvetr 11B v2", "description": "Creative writing model, routed with permission. It's fast, it keeps the conversation going, and it stays in character.\n\nIf you submit a raw prompt, you can use Alpaca or Vicuna formats.", "context_length": 4096, "created": 1713657600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "meta-llama/llama-3-8b-instruct", "name": "Meta: Llama 3 8B Instruct", "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "created": 1713398400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000003", "completion": "0.00000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "seed", "repetition_penalty", "frequency_penalty", "presence_penalty", "stop", "min_p", "logit_bias", "tools", "tool_choice", "response_format", "top_logprobs", "logprobs", "top_a"]}, {"id": "meta-llama/llama-3-70b-instruct", "name": "Meta: Llama 3 70B Instruct", "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "created": 1713398400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000003", "completion": "0.0000004", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "top_logprobs", "logprobs", "seed", "tools", "tool_choice"]}, {"id": "mistralai/mixtral-8x22b-instruct", "name": "Mistral: Mixtral 8x22B Instruct", "description": "Mistral's official instruct fine-tuned version of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b). It uses 39B active parameters out of 141B, offering unparalleled cost efficiency for its size. Its strengths include:\n- strong math, coding, and reasoning\n- large context length (64k)\n- fluency in English, French, Italian, German, and Spanish\n\nSee benchmarks on the launch announcement [here](https://mistral.ai/news/mixtral-8x22b/).\n#moe", "context_length": 65536, "created": 1713312000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000004", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed", "top_k", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "microsoft/wizardlm-2-8x22b", "name": "WizardLM-2 8x22B", "description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.\n\nIt is an instruct finetune of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "context_length": 65536, "created": 1713225600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000005", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "stop", "seed", "min_p", "logit_bias", "response_format"]}, {"id": "google/gemini-pro-1.5", "name": "Google: Gemini 1.5 Pro", "description": "Google's latest multimodal model, supports image and video[0] in text or chat prompts.\n\nOptimized for language tasks including:\n\n- Code generation\n- Text generation\n- Text editing\n- Problem solving\n- Recommendations\n- Information extraction\n- Data extraction or generation\n- AI agents\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n* [0]: Video input is not available through OpenRouter at this time.", "context_length": 2000000, "created": 1712620800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000125", "completion": "0.000005", "image": "0.0006575", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "tools", "tool_choice", "seed", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4-turbo", "name": "OpenAI: GPT-4 Turbo", "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to December 2023.", "context_length": 128000, "created": 1712620800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0.01445", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "cohere/command-r-plus", "name": "Cohere: Command R+", "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1712188800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "cohere/command-r-plus-04-2024", "name": "Cohere: Command R+ (04-2024)", "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1712016000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "sophosympatheia/midnight-rose-70b", "name": "<PERSON> Rose 70B", "description": "A merge with a complex family tree, this model was crafted for roleplaying and storytelling. <PERSON> Rose is a successor to Rogue Rose and Aurora Nights and improves upon them both. It wants to produce lengthy output by default and is the best creative writing merge produced so far by sophosympatheia.\n\nDescending from earlier versions of <PERSON> Rose and [Wizard Tulu Dolphin 70B](https://huggingface.co/sophosympatheia/Wizard-Tu<PERSON>-Dolphin-70B-v1.0), it inherits the best qualities of each.", "context_length": 4096, "created": 1711065600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000008", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "cohere/command", "name": "Cohere: Command", "description": "Command is an instruction-following conversational model that performs language tasks with high quality, more reliably and with a longer context than our base generative models.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 4096, "created": 1710374400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "cohere/command-r", "name": "Cohere: Command R", "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1710374400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "anthropic/claude-3-haiku:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal", "context_length": 200000, "created": 1710288000, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.00000125", "image": "0.0004", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3-haiku", "name": "Anthropic: <PERSON> 3 <PERSON><PERSON>", "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal", "context_length": 200000, "created": 1710288000, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.00000125", "image": "0.0004", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3-opus:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "created": 1709596800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.000075", "image": "0.024", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3-opus", "name": "Anthropic: <PERSON> 3 Opus", "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "created": 1709596800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000015", "completion": "0.000075", "image": "0.024", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3-sonnet:beta", "name": "Anthropic: <PERSON> (self-moderated)", "description": "Claude 3 Sonnet is an ideal balance of intelligence and speed for enterprise workloads. Maximum utility at a lower price, dependable, balanced for scaled deployments.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "created": 1709596800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-3-sonnet", "name": "Anthropic: <PERSON> 3 <PERSON>", "description": "Claude 3 Sonnet is an ideal balance of intelligence and speed for enterprise workloads. Maximum utility at a lower price, dependable, balanced for scaled deployments.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "created": 1709596800, "input_modalities": ["text", "image"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "cohere/command-r-03-2024", "name": "Cohere: Command R (03-2024)", "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "context_length": 128000, "created": 1709341200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "seed", "response_format", "structured_outputs"]}, {"id": "mistralai/mistral-large", "name": "Mistral Large", "description": "This is Mistral AI's flagship model, Mistral Large 2 (version `mistral-large-2407`). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.", "context_length": 128000, "created": 1708905600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000002", "completion": "0.000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "response_format", "stop", "seed", "frequency_penalty", "presence_penalty", "structured_outputs"]}, {"id": "openai/gpt-3.5-turbo-0613", "name": "OpenAI: GPT-3.5 Turbo (older v0613)", "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "context_length": 4095, "created": 1706140800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4-turbo-preview", "name": "OpenAI: GPT-4 Turbo Preview", "description": "The preview GPT-4 model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Dec 2023.\n\n**Note:** heavily rate limited by OpenAI while in preview.", "context_length": 128000, "created": 1706140800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "name": "Nous: Hermes 2 Mixtral 8x7B DPO", "description": "Nous Hermes 2 Mixtral 8x7B DPO is the new flagship Nous Research model trained over the [Mixtral 8x7B MoE LLM](/models/mistralai/mixtral-8x7b).\n\nThe model was trained on over 1,000,000 entries of primarily [GPT-4](/models/openai/gpt-4) generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.\n\n#moe", "context_length": 32768, "created": 1705363200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000006", "completion": "0.0000006", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/mistral-medium", "name": "Mistral Medium", "description": "This is Mistral AI's closed-source, medium-sided model. It's powered by a closed-source prototype and excels at reasoning, code, JSON, chat, and more. In benchmarks, it compares with many of the flagship models of other companies.", "context_length": 32768, "created": 1704844800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000275", "completion": "0.0000081", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/mistral-small", "name": "Mistra<PERSON> Small", "description": "With 22 billion parameters, Mistral Small v24.09 offers a convenient mid-point between (Mistral NeMo 12B)[/mistralai/mistral-nemo] and (Mistral Large 2)[/mistralai/mistral-large], providing a cost-effective solution that can be deployed across various platforms and environments. It has better reasoning, exhibits more capabilities, can produce and reason about code, and is multiligual, supporting English, French, German, Italian, and Spanish.", "context_length": 32768, "created": 1704844800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/mistral-tiny", "name": "Mistral Tiny", "description": "Note: This model is being deprecated. Recommended replacement is the newer [Ministral 8B](/mistral/ministral-8b)\n\nThis model is currently powered by Mistral-7B-v0.2, and incorporates a \"better\" fine-tuning than [Mistral 7B](/models/mistralai/mistral-7b-instruct-v0.1), inspired by community work. It's best used for large batch processing tasks where cost is a significant factor but reasoning capabilities are not crucial.", "context_length": 32768, "created": 1704844800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000025", "completion": "0.00000025", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "mistralai/mistral-7b-instruct-v0.2", "name": "Mistral: Mistral 7B Instruct v0.2", "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct](/modelsmistralai/mistral-7b-instruct-v0.1), with the following changes:\n\n- 32k context window (vs 8k context in v0.1)\n- Rope-theta = 1e6\n- No Sliding-Window Attention", "context_length": 32768, "created": 1703721600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/mixtral-8x7b-instruct", "name": "Mistral: Mixtral 8x7B Instruct", "description": "Mixtral 8x7B Instruct is a pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe", "context_length": 32768, "created": 1702166400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000008", "completion": "0.00000024", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs"]}, {"id": "neversleep/noromaid-20b", "name": "Noromaid 20B", "description": "A collab between IkariDev and Undi. This merge is suitable for RP, ERP, and general knowledge.\n\n#merge #uncensored", "context_length": 8192, "created": 1700956800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000125", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "anthropic/claude-2.1:beta", "name": "Anthropic: <PERSON> v2.1 (self-moderated)", "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "created": 1700611200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-2.1", "name": "Anthropic: <PERSON> v2.1", "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "created": 1700611200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-2:beta", "name": "Anthropic: <PERSON> v2 (self-moderated)", "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "created": 1700611200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-2", "name": "Anthropic: <PERSON> v2", "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "created": 1700611200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "undi95/toppy-m-7b", "name": "Toppy M 7B", "description": "A wild 7B parameter model that merges several models using the new task_arithmetic merge method from mergekit.\nList of merged models:\n- NousResearch/Nous-Capybara-7B-V1.9\n- [HuggingFaceH4/zephyr-7b-beta](/models/huggingfaceh4/zephyr-7b-beta)\n- lemonilia/AshhLimaRP-Mistral-7B\n- Vulkane/120-Days-of-Sodom-LoRA-Mistral-7b\n- Undi95/Mistral-pippa-sharegpt-7b-qlora\n\n#merge #uncensored", "context_length": 4096, "created": 1699574400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "alpindale/goliath-120b", "name": "Goliath 120B", "description": "A large LLM created by combining two fine-tuned Llama 70B models into one 120B model. Combines Xwin and Euryale.\n\nCredits to\n- [@chargoddard](https://huggingface.co/chargoddard) for developing the framework used to merge the model - [mergekit](https://github.com/cg123/mergekit).\n- [@Undi95](https://huggingface.co/Undi95) for helping with the merge ratios.\n\n#merge", "context_length": 6144, "created": 1699574400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00001", "completion": "0.0000125", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "openrouter/auto", "name": "Auto Router", "description": "Your prompt will be processed by a meta-model and routed to one of dozens of models (see below), optimizing for the best possible output.\n\nTo see which model was used, visit [Activity](/activity), or read the `model` attribute of the response. Your response will be priced at the same rate as the routed model.\n\nThe meta-model is powered by [Not Diamond](https://docs.notdiamond.ai/docs/how-not-diamond-works). Learn more in our [docs](/docs/model-routing).\n\nRequests will be routed to the following models:\n- [openai/gpt-4o-2024-08-06](/openai/gpt-4o-2024-08-06)\n- [openai/gpt-4o-2024-05-13](/openai/gpt-4o-2024-05-13)\n- [openai/gpt-4o-mini-2024-07-18](/openai/gpt-4o-mini-2024-07-18)\n- [openai/chatgpt-4o-latest](/openai/chatgpt-4o-latest)\n- [openai/o1-preview-2024-09-12](/openai/o1-preview-2024-09-12)\n- [openai/o1-mini-2024-09-12](/openai/o1-mini-2024-09-12)\n- [anthropic/claude-3.5-sonnet](/anthropic/claude-3.5-sonnet)\n- [anthropic/claude-3.5-haiku](/anthropic/claude-3.5-haiku)\n- [anthropic/claude-3-opus](/anthropic/claude-3-opus)\n- [anthropic/claude-2.1](/anthropic/claude-2.1)\n- [google/gemini-pro-1.5](/google/gemini-pro-1.5)\n- [google/gemini-flash-1.5](/google/gemini-flash-1.5)\n- [mistralai/mistral-large-2407](/mistralai/mistral-large-2407)\n- [mistralai/mistral-nemo](/mistralai/mistral-nemo)\n- [deepseek/deepseek-r1](/deepseek/deepseek-r1)\n- [meta-llama/llama-3.1-70b-instruct](/meta-llama/llama-3.1-70b-instruct)\n- [meta-llama/llama-3.1-405b-instruct](/meta-llama/llama-3.1-405b-instruct)\n- [mistralai/mixtral-8x22b-instruct](/mistralai/mixtral-8x22b-instruct)\n- [cohere/command-r-plus](/cohere/command-r-plus)\n- [cohere/command-r](/cohere/command-r)", "context_length": 2000000, "created": 1699401600, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "-1", "completion": "-1", "image": 0, "request": 0}, "supported_parameters": []}, {"id": "openai/gpt-3.5-turbo-1106", "name": "OpenAI: GPT-3.5 Turbo 16k (older v1106)", "description": "An older GPT-3.5 Turbo model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Sep 2021.", "context_length": 16385, "created": 1699228800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-4-1106-preview", "name": "OpenAI: GPT-4 Turbo (older v1106)", "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to April 2023.", "context_length": 128000, "created": 1699228800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "openai/gpt-3.5-turbo-instruct", "name": "OpenAI: GPT-3.5 Turbo Instruct", "description": "This model is a variant of GPT-3.5 Turbo tuned for instructional prompts and omitting chat-related optimizations. Training data: up to Sep 2021.", "context_length": 4095, "created": 1695859200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000015", "completion": "0.000002", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "mistralai/mistral-7b-instruct-v0.1", "name": "Mistral: Mistral 7B Instruct v0.1", "description": "A 7.3B parameter model that outperforms Llama 2 13B on all benchmarks, with optimizations for speed and context length.", "context_length": 2824, "created": 1695859200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00000011", "completion": "0.00000019", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed"]}, {"id": "pygmalionai/mythalion-13b", "name": "Pygmalion: Mythalion 13B", "description": "A blend of the new Pygmalion-13b and MythoMax. #merge", "context_length": 4096, "created": 1693612800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "openai/gpt-3.5-turbo-16k", "name": "OpenAI: GPT-3.5 Turbo 16k", "description": "This model offers four times the context length of gpt-3.5-turbo, allowing it to support approximately 20 pages of text in a single request at a higher cost. Training data: up to Sep 2021.", "context_length": 16385, "created": 1693180800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000003", "completion": "0.000004", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-4-32k", "name": "OpenAI: GPT-4 32k", "description": "GPT-4-32k is an extended version of GPT-4, with the same capabilities but quadrupled context length, allowing for processing up to 40 pages of text in a single pass. This is particularly beneficial for handling longer content like interacting with PDFs without an external vector database. Training data: up to Sep 2021.", "context_length": 32767, "created": 1693180800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00006", "completion": "0.00012", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-4-32k-0314", "name": "OpenAI: GPT-4 32k (older v0314)", "description": "GPT-4-32k is an extended version of GPT-4, with the same capabilities but quadrupled context length, allowing for processing up to 40 pages of text in a single pass. This is particularly beneficial for handling longer content like interacting with PDFs without an external vector database. Training data: up to Sep 2021.", "context_length": 32767, "created": 1693180800, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00006", "completion": "0.00012", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}, {"id": "mancer/weaver", "name": "Mancer: <PERSON> (alpha)", "description": "An attempt to recreate Claude-style verbosity, but don't expect the same level of coherence or memory. Meant for use in roleplay/narrative situations.", "context_length": 8000, "created": 1690934400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000015", "completion": "0.0000015", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "logit_bias", "top_k", "min_p", "seed", "top_a"]}, {"id": "anthropic/claude-2.0:beta", "name": "Anthropic: <PERSON> v2.0 (self-moderated)", "description": "Anthropic's flagship model. Superior performance on tasks that require complex reasoning. Supports hundreds of pages of text.", "context_length": 100000, "created": 1690502400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "anthropic/claude-2.0", "name": "Anthropic: <PERSON> v2.0", "description": "Anthropic's flagship model. Superior performance on tasks that require complex reasoning. Supports hundreds of pages of text.", "context_length": 100000, "created": 1690502400, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "top_k", "stop"]}, {"id": "undi95/remm-slerp-l2-13b", "name": "ReMM SLERP 13B", "description": "A recreation trial of the original MythoMax-L2-B13 but with updated models. #merge", "context_length": 4096, "created": 1689984000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed", "logit_bias", "top_a"]}, {"id": "gryphe/mythomax-l2-13b", "name": "MythoMax 13B", "description": "One of the highest performing and most popular fine-tunes of Llama 2 13B, with rich descriptions and roleplay. #merge", "context_length": 4096, "created": 1688256000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.000000065", "completion": "0.000000065", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "stop", "seed", "min_p", "logit_bias", "response_format", "top_a"]}, {"id": "meta-llama/llama-2-70b-chat", "name": "Meta: Llama 2 70B Chat", "description": "The flagship, 70 billion parameter language model from Meta, fine tuned for chat completions. Llama 2 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align to human preferences for helpfulness and safety.", "context_length": 4096, "created": 1687219200, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "image": "0", "request": "0"}, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "openai/gpt-3.5-turbo", "name": "OpenAI: GPT-3.5 Turbo", "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "context_length": 16385, "created": 1685232000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-3.5-turbo-0125", "name": "OpenAI: GPT-3.5 Turbo 16k", "description": "The latest GPT-3.5 Turbo model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Sep 2021.\n\nThis version has a higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls.", "context_length": 16385, "created": 1685232000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-4", "name": "OpenAI: GPT-4", "description": "OpenAI's flagship model, GPT-4 is a large-scale multimodal language model capable of solving difficult problems with greater accuracy than previous models due to its broader general knowledge and advanced reasoning capabilities. Training data: up to Sep 2021.", "context_length": 8191, "created": 1685232000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00003", "completion": "0.00006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-4-0314", "name": "OpenAI: GPT-4 (older v0314)", "description": "GPT-4-0314 is the first version of GPT-4 released, with a context length of 8,192 tokens, and was supported until June 14. Training data: up to Sep 2021.", "context_length": 8191, "created": 1685232000, "input_modalities": ["text"], "output_modalities": ["text"], "pricing": {"prompt": "0.00003", "completion": "0.00006", "image": "0", "request": "0"}, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "structured_outputs"]}]}