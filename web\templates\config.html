{% extends "base.html" %}

{% block title %}Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Custom Dark Theme CSS for Select2 -->
<style>
/* Select2 Dark Theme Overrides */
.select2-container--bootstrap-5 .select2-dropdown {
    background-color: #2d3436 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
    min-width: 700px !important;
    max-height: 500px !important;
}

.select2-container--bootstrap-5 .select2-results__options {
    max-height: 450px !important;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search__field {
    background-color: #343a40 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
    padding: 8px 12px !important;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search__field::placeholder {
    color: #adb5bd !important;
}

.select2-container--bootstrap-5 .select2-results__option {
    background-color: #2d3436 !important;
    color: #f8f9fa !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid #495057 !important;
}

.select2-container--bootstrap-5 .select2-results__option:hover,
.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: #495057 !important;
    color: #ffffff !important;
}

.select2-container--bootstrap-5 .select2-results__option[aria-selected="true"] {
    background-color: #6c5ce7 !important;
    color: #ffffff !important;
}

.select2-container--bootstrap-5 .select2-selection--single {
    background-color: #343a40 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
    height: auto !important;
    min-height: 38px !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    color: #f8f9fa !important;
    padding: 0.375rem 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
    color: #adb5bd !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
    border-color: #adb5bd transparent transparent transparent !important;
}

.select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #adb5bd transparent !important;
}

/* Custom result template styling - 表格样式 */
.select2-result-repository {
    color: #f8f9fa !important;
    display: flex !important;
    align-items: flex-start !important;
    padding: 0 !important;
    margin: 0 !important;
}

.select2-result-repository__avatar {
    width: 40px !important;
    min-width: 40px !important;
    text-align: center !important;
    padding: 8px 0 !important;
}

.select2-result-repository__meta {
    flex: 1 !important;
    padding: 8px 12px !important;
    min-height: 60px !important;
}

.select2-result-repository__title {
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    margin-bottom: 4px !important;
    line-height: 1.3 !important;
    max-width: 300px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.select2-result-repository__description {
    color: #adb5bd !important;
    font-size: 12px !important;
    margin-bottom: 4px !important;
    line-height: 1.3 !important;
    max-height: 32px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

.select2-result-repository__statistics {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
}

.select2-result-repository__info-left,
.select2-result-repository__info-right {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.select2-result-repository__info-right {
    justify-content: flex-end !important;
}

.select2-result-repository__forks,
.select2-result-repository__stargazers,
.select2-result-repository__modalities {
    color: #74b9ff !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    background-color: #495057 !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    white-space: nowrap !important;
}

.select2-result-repository__pricing {
    color: #00b894 !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    background-color: rgba(0, 184, 148, 0.1) !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    border: 1px solid #00b894 !important;
}

.select2-result-repository__context {
    color: #fdcb6e !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    background-color: rgba(253, 203, 110, 0.1) !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    border: 1px solid #fdcb6e !important;
}

/* 表格头部样式 */
.select2-results__options::before {
    content: "🤖  Model Name  |  Description  |  Context  |  Pricing  |  Modalities";
    display: block !important;
    background-color: #495057 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    padding: 8px 16px !important;
    border-bottom: 2px solid #6c757d !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
}

/* Focus states */
.select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
    border-color: #74b9ff !important;
    box-shadow: 0 0 0 0.2rem rgba(116, 185, 255, 0.25) !important;
}

/* Loading state */
.select2-container--bootstrap-5 .select2-results__message {
    color: #adb5bd !important;
    background-color: #2d3436 !important;
    padding: 16px !important;
    text-align: center !important;
    font-size: 14px !important;
}

/* Ensure proper text contrast in all states */
.select2-container--bootstrap-5 * {
    color: inherit !important;
}

/* 增强搜索区域 */
.select2-dropdown .select2-search {
    padding: 8px 12px !important;
    background-color: #343a40 !important;
    border-bottom: 2px solid #495057 !important;
}

/* 滚动条样式 */
.select2-results__options {
    scrollbar-width: thin !important;
    scrollbar-color: #6c757d #2d3436 !important;
}

.select2-results__options::-webkit-scrollbar {
    width: 8px !important;
}

.select2-results__options::-webkit-scrollbar-track {
    background: #2d3436 !important;
}

.select2-results__options::-webkit-scrollbar-thumb {
    background-color: #6c757d !important;
    border-radius: 4px !important;
}

.select2-results__options::-webkit-scrollbar-thumb:hover {
    background-color: #adb5bd !important;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 offset-md-1">
        <form method="POST" action="{{ url_for('config') }}">
            {{ form.hidden_tag() }}

            <!-- AI Provider Configuration -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-robot me-2"></i>AI Provider Configuration</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.default_provider.label(class="form-label") }}
                        {{ form.default_provider(class="form-select") }}
                        {% if form.default_provider.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.default_provider.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Select the default AI provider to use for response generation.
                        </div>
                    </div>

                    <!-- Gemini Configuration -->
                    <div class="provider-config" id="gemini-config">
                        <h5 class="mt-4 mb-3">Google Gemini Configuration</h5>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                {{ form.gemini_enabled.label(class="form-label") }}
                                {{ form.gemini_enabled(class="form-select") }}
                                {% if form.gemini_enabled.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.gemini_enabled.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                {{ form.gemini_default_model.label(class="form-label") }}
                                {{ form.gemini_default_model(class="form-control") }}
                                {% if form.gemini_default_model.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.gemini_default_model.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                {{ form.gemini_key_strategy.label(class="form-label") }}
                                {{ form.gemini_key_strategy(class="form-select") }}
                                {% if form.gemini_key_strategy.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.gemini_key_strategy.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.gemini_api_keys.label(class="form-label") }}
                            {{ form.gemini_api_keys(class="form-control", placeholder="Enter one or more API keys separated by commas") }}
                            {% if form.gemini_api_keys.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.gemini_api_keys.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Get Gemini API keys from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>.
                                For multiple keys, separate them with commas.
                            </div>
                        </div>
                    </div>

                    <!-- OpenRouter Configuration -->
                    <div class="provider-config" id="openrouter-config">
                        <h5 class="mt-4 mb-3">OpenRouter Configuration</h5>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                {{ form.openrouter_enabled.label(class="form-label") }}
                                {{ form.openrouter_enabled(class="form-select") }}
                                {% if form.openrouter_enabled.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.openrouter_enabled.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                {{ form.openrouter_default_model.label(class="form-label") }}
                                <select id="openrouter_default_model" name="openrouter_default_model" class="form-control openrouter-model-select">
                                    <option value="{{ form.openrouter_default_model.data or '' }}">{{ form.openrouter_default_model.data or 'Select a model...' }}</option>
                                </select>
                                {% if form.openrouter_default_model.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.openrouter_default_model.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="loadOpenRouterModels">
                                        <i class="fas fa-sync-alt me-1"></i>Load Available Models
                                    </button>
                                    搜索并选择 OpenRouter 模型，支持表格式详细信息显示 (模型名称、描述、上下文长度、价格、支持格式)
                                </div>
                            </div>

                            <div class="col-md-4">
                                {{ form.openrouter_key_strategy.label(class="form-label") }}
                                {{ form.openrouter_key_strategy(class="form-select") }}
                                {% if form.openrouter_key_strategy.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.openrouter_key_strategy.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.openrouter_api_keys.label(class="form-label") }}
                            {{ form.openrouter_api_keys(class="form-control", placeholder="Enter one or more API keys separated by commas") }}
                            {% if form.openrouter_api_keys.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.openrouter_api_keys.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Get OpenRouter API keys from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a>.
                                For multiple keys, separate them with commas.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Plugin Settings -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-users me-2"></i>Customer Plugin Settings</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.customer_plugin_enabled.label(class="form-label") }}
                            {{ form.customer_plugin_enabled(class="form-select") }}
                            {% if form.customer_plugin_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.customer_plugin_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable the customer plugin feature.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.max_responses_per_customer.label(class="form-label") }}
                            {{ form.max_responses_per_customer(class="form-control") }}
                            {% if form.max_responses_per_customer.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.max_responses_per_customer.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Maximum number of responses a customer can generate.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feature Toggles -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-toggle-on me-2"></i>Feature Toggles</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.ai_generation_enabled.label(class="form-label") }}
                            {{ form.ai_generation_enabled(class="form-select") }}
                            {% if form.ai_generation_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ai_generation_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable AI-powered response generation.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.manual_review_enabled.label(class="form-label") }}
                            {{ form.manual_review_enabled(class="form-select") }}
                            {% if form.manual_review_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.manual_review_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable manual review of responses.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.batch_submission_enabled.label(class="form-label") }}
                            {{ form.batch_submission_enabled(class="form-select") }}
                            {% if form.batch_submission_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.batch_submission_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable batch submission of forms.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logging Settings -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0"><i class="fas fa-file-alt me-2"></i>Logging Settings</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.log_level.label(class="form-label") }}
                            {{ form.log_level(class="form-select") }}
                            {% if form.log_level.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.log_level.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Set the level of detail for logging.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.file_logging_enabled.label(class="form-label") }}
                            {{ form.file_logging_enabled(class="form-select") }}
                            {% if form.file_logging_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.file_logging_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable logging to files.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.console_logging_enabled.label(class="form-label") }}
                            {{ form.console_logging_enabled(class="form-select") }}
                            {% if form.console_logging_enabled.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.console_logging_enabled.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enable or disable logging to console.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="d-grid gap-2 mb-4">
                {{ form.submit(class="btn btn-primary btn-lg") }}
            </div>
        </form>

        <!-- Help Information -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>About AI Providers</h5>
            </div>
            <div class="card-body">
                <p>This application supports multiple AI providers for generating intelligent responses.</p>

                <div class="row">
                    <div class="col-md-6">
                        <h6>Google Gemini</h6>
                        <p>Google's Gemini AI models for generating high-quality responses.</p>

                        <h6>How to Get a Gemini API Key:</h6>
                        <ol>
                            <li>Go to <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
                            <li>Sign in with your Google account</li>
                            <li>Click on "Get API key" or "Create API key"</li>
                            <li>Copy the generated API key</li>
                            <li>Paste it in the Gemini API Keys field</li>
                        </ol>
                    </div>

                    <div class="col-md-6">
                        <h6>OpenRouter</h6>
                        <p>Access to multiple AI models through a single API, including OpenAI, Anthropic, and more.</p>

                        <h6>How to Get an OpenRouter API Key:</h6>
                        <ol>
                            <li>Go to <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a></li>
                            <li>Sign in or create an account</li>
                            <li>Create a new API key</li>
                            <li>Copy the generated API key</li>
                            <li>Paste it in the OpenRouter API Keys field</li>
                        </ol>
                    </div>
                </div>

                <h6 class="mt-3">Multiple API Keys</h6>
                <p>You can enter multiple API keys for each provider, separated by commas. The application will use them according to the selected key strategy:</p>
                <ul>
                    <li><strong>Round Robin:</strong> Uses each key in sequence</li>
                    <li><strong>Random:</strong> Randomly selects a key for each request</li>
                    <li><strong>Least Used:</strong> Uses the key with the fewest requests</li>
                </ul>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> Keep your API keys secure. Do not share them with others.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get provider select element
        const providerSelect = document.getElementById('default_provider');

        // Get provider config sections
        const geminiConfig = document.getElementById('gemini-config');
        const openrouterConfig = document.getElementById('openrouter-config');

        // Initialize Select2 for OpenRouter model dropdown
        let openrouterModelSelect = null;

        function initializeOpenRouterModelSelect() {
            const modelSelectElement = document.getElementById('openrouter_default_model');
            if (modelSelectElement && !openrouterModelSelect) {
                openrouterModelSelect = $(modelSelectElement).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select a model or type to search...',
                    allowClear: true,
                    ajax: {
                        url: '/api/openrouter/models',
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                search: params.term || '',
                                page: params.page || 1
                            };
                        },
                        processResults: function (data) {
                            if (data.success) {
                                return {
                                    results: data.models
                                };
                            } else {
                                return {
                                    results: []
                                };
                            }
                        },
                        cache: true
                    },
                    minimumInputLength: 0,
                    templateResult: function(model) {
                        if (model.loading) {
                            return model.text;
                        }

                        // Create formatted display for the dropdown - 表格样式
                        const $container = $(
                            '<div class="select2-result-repository clearfix">' +
                                '<div class="select2-result-repository__avatar">' +
                                    '<i class="fas fa-brain text-primary"></i>' +
                                '</div>' +
                                '<div class="select2-result-repository__meta">' +
                                    '<div class="select2-result-repository__title"></div>' +
                                    '<div class="select2-result-repository__description"></div>' +
                                    '<div class="select2-result-repository__statistics">' +
                                        '<div class="select2-result-repository__info-left">' +
                                            '<span class="select2-result-repository__context"></span>' +
                                        '</div>' +
                                        '<div class="select2-result-repository__info-right">' +
                                            '<span class="select2-result-repository__pricing"></span>' +
                                            '<span class="select2-result-repository__modalities"></span>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>'
                        );

                        $container.find('.select2-result-repository__title').text(model.name || model.id);
                        $container.find('.select2-result-repository__description').text(
                            model.description || 'No description available'
                        );
                        
                        // Add context length with styling
                        if (model.context_length) {
                            $container.find('.select2-result-repository__context').text(
                                `${model.context_length.toLocaleString()} tokens`
                            );
                        } else {
                            $container.find('.select2-result-repository__context').text('N/A');
                        }

                        // Add pricing info with enhanced styling
                        if (model.pricing && model.pricing.prompt) {
                            const promptPrice = parseFloat(model.pricing.prompt);
                            const completionPrice = model.pricing.completion ? parseFloat(model.pricing.completion) : promptPrice;
                            
                            let pricingText = `$${promptPrice.toFixed(6)}/1K`;
                            if (completionPrice !== promptPrice) {
                                pricingText += ` | $${completionPrice.toFixed(6)}/1K out`;
                            }
                            
                            $container.find('.select2-result-repository__pricing').text(pricingText);
                        } else {
                            $container.find('.select2-result-repository__pricing').text('Price N/A');
                        }

                        // Add input/output modalities
                        if (model.input_modalities && model.input_modalities.length > 0) {
                            const modalities = model.input_modalities.join(', ');
                            $container.find('.select2-result-repository__modalities').text(modalities);
                        } else {
                            $container.find('.select2-result-repository__modalities').text('Text');
                        }

                        return $container;
                    },
                    templateSelection: function(model) {
                        return model.name || model.text || model.id;
                    }
                });

                // Set current value if exists
                const currentValue = '{{ form.openrouter_default_model.data or "" }}';
                if (currentValue) {
                    const option = new Option(currentValue, currentValue, true, true);
                    $(modelSelectElement).append(option).trigger('change');
                }
            }
        }

        // Function to toggle provider configs based on selection
        function toggleProviderConfigs() {
            const selectedProvider = providerSelect.value;

            // Hide non-selected provider configs
            if (selectedProvider === 'gemini') {
                geminiConfig.style.display = 'block';
                openrouterConfig.style.display = 'none';
            } else if (selectedProvider === 'openrouter') {
                openrouterConfig.style.display = 'block';
                geminiConfig.style.display = 'none';
                
                // Initialize Select2 for OpenRouter when it becomes visible
                setTimeout(initializeOpenRouterModelSelect, 100);
            }
        }

        // Function to load OpenRouter models manually
        function loadOpenRouterModels() {
            const loadButton = document.getElementById('loadOpenRouterModels');
            const originalText = loadButton.innerHTML;
            
            // Show loading state
            loadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
            loadButton.disabled = true;

            fetch('/api/openrouter/models')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear current options
                        const modelSelect = document.getElementById('openrouter_default_model');
                        
                        if (openrouterModelSelect) {
                            // Clear Select2 options
                            openrouterModelSelect.empty();
                            
                            // Add new options
                            data.models.forEach(model => {
                                const option = new Option(model.text, model.id, false, false);
                                $(modelSelect).append(option);
                            });
                            
                            // Refresh Select2
                            openrouterModelSelect.trigger('change');
                        }
                        
                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
                        alertDiv.innerHTML = `
                            Loaded ${data.models.length} OpenRouter models successfully!
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        loadButton.parentNode.appendChild(alertDiv);
                        
                        // Auto-dismiss after 3 seconds
                        setTimeout(() => {
                            if (alertDiv.parentNode) {
                                alertDiv.remove();
                            }
                        }, 3000);
                    } else {
                        throw new Error(data.error || 'Failed to load models');
                    }
                })
                .catch(error => {
                    console.error('Error loading OpenRouter models:', error);
                    
                    // Show error message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-2';
                    alertDiv.innerHTML = `
                        Failed to load models: ${error.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    loadButton.parentNode.appendChild(alertDiv);
                })
                .finally(() => {
                    // Restore button state
                    loadButton.innerHTML = originalText;
                    loadButton.disabled = false;
                });
        }

        // Set initial state
        toggleProviderConfigs();

        // Add event listener for changes
        providerSelect.addEventListener('change', toggleProviderConfigs);

        // Add event listener for load models button
        document.getElementById('loadOpenRouterModels').addEventListener('click', loadOpenRouterModels);

        // Add event listeners for enabled toggles
        document.getElementById('gemini_enabled').addEventListener('change', function() {
            const isEnabled = this.value === 'true';
            const inputs = geminiConfig.querySelectorAll('input, select');

            // Skip the enabled toggle itself
            for (let i = 0; i < inputs.length; i++) {
                if (inputs[i] !== this) {
                    inputs[i].disabled = !isEnabled;
                }
            }
        });

        document.getElementById('openrouter_enabled').addEventListener('change', function() {
            const isEnabled = this.value === 'true';
            const inputs = openrouterConfig.querySelectorAll('input, select');

            // Skip the enabled toggle itself and the Select2 element
            for (let i = 0; i < inputs.length; i++) {
                if (inputs[i] !== this && inputs[i].id !== 'openrouter_default_model') {
                    inputs[i].disabled = !isEnabled;
                }
            }
            
            // Handle Select2 separately
            if (openrouterModelSelect) {
                openrouterModelSelect.prop('disabled', !isEnabled);
            }
        });

        // Set initial state for enabled toggles
        const geminiEnabled = document.getElementById('gemini_enabled').value === 'true';
        const openrouterEnabled = document.getElementById('openrouter_enabled').value === 'true';

        const geminiInputs = geminiConfig.querySelectorAll('input, select');
        for (let i = 0; i < geminiInputs.length; i++) {
            if (geminiInputs[i] !== document.getElementById('gemini_enabled')) {
                geminiInputs[i].disabled = !geminiEnabled;
            }
        }

        const openrouterInputs = openrouterConfig.querySelectorAll('input, select');
        for (let i = 0; i < openrouterInputs.length; i++) {
            if (openrouterInputs[i] !== document.getElementById('openrouter_enabled') && 
                openrouterInputs[i].id !== 'openrouter_default_model') {
                openrouterInputs[i].disabled = !openrouterEnabled;
            }
        }

        // Initialize Select2 if OpenRouter is the default provider
        if (providerSelect.value === 'openrouter') {
            setTimeout(initializeOpenRouterModelSelect, 100);
        }
    });
</script>
{% endblock %}