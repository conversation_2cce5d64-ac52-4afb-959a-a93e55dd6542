/**
 * Modern JavaScript for Google Form AutoFill Web Interface
 * Enhanced with animations, dark theme, and improved UX
 */

// Global state management
const AppState = {
    isLoading: false,
    activeRequests: new Set(),
    theme: 'dark',
    notifications: []
};

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    setupAnimations();
    setupTheme();
    setupTooltips();
    setupAutoFeatures();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('🚀 Google Form AutoFill App Initialized');

    // Add loading states to forms
    setupFormLoadingStates();

    // Setup AJAX defaults
    setupAjaxDefaults();

    // Initialize performance monitoring
    monitorPerformance();

    // Safely manage Bootstrap modals to prevent flashing
    safelyManageModals();
}

/**
 * Setup form loading states
 */
function setupFormLoadingStates() {
    // Add loading state classes to all forms
    const forms = document.querySelectorAll('form:not(.no-loading-state)');
    forms.forEach(form => {
        // Add data attributes for loading state management
        form.setAttribute('data-loading', 'false');

        // Track original submit button states
        const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
        submitButtons.forEach(button => {
            // Store original text if not already set
            if (!button.hasAttribute('data-original-text')) {
                button.setAttribute('data-original-text', button.innerHTML || button.value);
            }
        });

        // Add form reset handler to restore states
        form.addEventListener('reset', function() {
            removeFormOverlay(form);
            const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                restoreButton(submitButton);
            }
            form.setAttribute('data-loading', 'false');
        });
    });

    console.log(`✅ Initialized loading states for ${forms.length} forms`);
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Global click handler for buttons with loading states
    document.addEventListener('click', function(e) {
        if (e.target.matches('button[type="submit"], .btn-submit')) {
            handleButtonClick(e.target);
        }

        // Handle card hover effects
        if (e.target.closest('.hover-lift')) {
            handleCardInteraction(e.target.closest('.hover-lift'));
        }
    });

    // Global form submission handler
    document.addEventListener('submit', function(e) {
        if (e.target.matches('form:not(.no-auto-loading)')) {
            handleFormSubmission(e);
        }
    });

    // Handle window resize for responsive adjustments
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 250);
    });

    // Handle scroll events for animations
    window.addEventListener('scroll', throttle(handleScroll, 100));
}

/**
 * Setup animations and transitions
 */
function setupAnimations() {
    // Add stagger animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Setup intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.card, .alert, .table').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Setup dark theme
 */
function setupTheme() {
    document.body.classList.add('dark-theme');

    // Add theme toggle if needed
    const themeToggle = document.querySelector('#theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

/**
 * Setup tooltips and popovers
 */
function setupTooltips() {
    // Initialize Bootstrap tooltips with custom options
    const tooltipOptions = {
        delay: { show: 500, hide: 100 },
        placement: 'auto',
        trigger: 'hover focus'
    };

    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"], [title]');
    tooltips.forEach(element => {
        if (!element.hasAttribute('data-bs-toggle')) {
            element.setAttribute('data-bs-toggle', 'tooltip');
            element.setAttribute('data-bs-title', element.getAttribute('title'));
            element.removeAttribute('title');
        }
        new bootstrap.Tooltip(element, tooltipOptions);
    });

    // Initialize popovers
    const popovers = document.querySelectorAll('[data-bs-toggle="popover"]');
    popovers.forEach(element => {
        new bootstrap.Popover(element);
    });
}

/**
 * Setup automatic features
 */
function setupAutoFeatures() {
    // Auto-dismiss success alerts
    setTimeout(() => {
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(alert => {
            const bsAlert = bootstrap.Alert.getOrCreateInstance(alert);
            bsAlert.close();
        });
    }, 5000);

    // Auto-focus first input on forms
    const firstInput = document.querySelector('form input:not([type="hidden"]):not([disabled]):first-of-type');
    if (firstInput) {
        firstInput.focus();
    }

    // Setup auto-save for textareas (if needed)
    setupAutoSave();
}

/**
 * Handle button clicks with loading states
 */
function handleButtonClick(button) {
    if (button.classList.contains('loading')) return;

    const originalText = button.innerHTML;
    const loadingText = button.getAttribute('data-loading-text') || '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';

    button.classList.add('loading');
    button.innerHTML = loadingText;
    button.disabled = true;

    // Store original state for restoration
    button.dataset.originalText = originalText;

    // Auto-restore after 10 seconds if not manually restored
    setTimeout(() => {
        if (button.classList.contains('loading')) {
            restoreButton(button);
        }
    }, 10000);
}

/**
 * Restore button to original state
 */
function restoreButton(button) {
    if (button.dataset.originalText) {
        // Handle button elements
        if (button.tagName === 'BUTTON') {
            button.innerHTML = button.dataset.originalText;
        } else if (button.tagName === 'INPUT') {
            // Handle input elements
            button.value = button.dataset.originalText;
        }
        button.classList.remove('loading');
        button.disabled = false;
        delete button.dataset.originalText;
    }
}

/**
 * Handle form submissions with enhanced UX
 */
function handleFormSubmission(e) {
    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');

    if (submitButton) {
        handleButtonClick(submitButton);
    }

    // Add form loading overlay
    addFormOverlay(form);

    // Track form submission
    AppState.activeRequests.add(form);
}

/**
 * Add loading overlay to form
 */
function addFormOverlay(form) {
    const overlay = document.createElement('div');
    overlay.className = 'form-loading-overlay';
    overlay.innerHTML = `
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Processing...</span>
            </div>
            <p class="mt-2">Processing your request...</p>
        </div>
    `;

    form.style.position = 'relative';
    form.appendChild(overlay);
}

/**
 * Remove loading overlay from form
 */
function removeFormOverlay(form) {
    const overlay = form.querySelector('.form-loading-overlay');
    if (overlay) {
        overlay.remove();
    }

    AppState.activeRequests.delete(form);
}

/**
 * Handle card interactions
 */
function handleCardInteraction(card) {
    // Add ripple effect
    const ripple = document.createElement('div');
    ripple.className = 'ripple-effect';
    card.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Setup AJAX defaults
 */
function setupAjaxDefaults() {
    // jQuery AJAX setup if jQuery is available
    if (window.jQuery) {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                AppState.isLoading = true;
                updateGlobalLoadingState();
            },
            complete: function() {
                AppState.isLoading = false;
                updateGlobalLoadingState();
            },
            error: function(xhr, status, error) {
                handleAjaxError(xhr, status, error);
            }
        });
    }
}

/**
 * Handle AJAX errors gracefully
 */
function handleAjaxError(xhr, status, error) {
    let message = 'An error occurred. Please try again.';

    if (xhr.responseJSON && xhr.responseJSON.error) {
        message = xhr.responseJSON.error;
    } else if (xhr.responseText) {
        try {
            const response = JSON.parse(xhr.responseText);
            message = response.error || message;
        } catch (e) {
            message = 'Server error. Please try again later.';
        }
    }

    showNotification('Error', message, 'error');
}

/**
 * Update global loading state
 */
function updateGlobalLoadingState() {
    const loadingIndicator = document.querySelector('.global-loading');
    if (loadingIndicator) {
        loadingIndicator.style.display = AppState.isLoading ? 'block' : 'none';
    }
}

/**
 * Show modern notification
 */
function showNotification(title, message, type = 'success') {
    const notification = createNotification(title, message, type);
    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Auto-dismiss
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);

    // Add to state
    AppState.notifications.push(notification);
}

/**
 * Create notification element
 */
function createNotification(title, message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    notification.innerHTML = `
        <div class="notification-content">
            <i class="${icons[type]} notification-icon"></i>
            <div class="notification-text">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="hideNotification(this.parentElement)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    return notification;
}

/**
 * Hide notification
 */
function hideNotification(notification) {
    notification.classList.add('hide');
    setTimeout(() => {
        notification.remove();
        AppState.notifications = AppState.notifications.filter(n => n !== notification);
    }, 300);
}

/**
 * Handle window resize
 */
function handleResize() {
    // Recalculate animations if needed
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.style.transform = '';
    });
}

/**
 * Handle scroll events
 */
function handleScroll() {
    const scrollTop = window.pageYOffset;
    const navbar = document.querySelector('.navbar');

    if (navbar) {
        if (scrollTop > 100) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    }
}

/**
 * Setup auto-save functionality
 */
function setupAutoSave() {
    const textareas = document.querySelectorAll('textarea[data-auto-save]');
    textareas.forEach(textarea => {
        let saveTimeout;
        textarea.addEventListener('input', function() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                saveTextareaContent(textarea);
            }, 2000);
        });
    });
}

/**
 * Save textarea content
 */
function saveTextareaContent(textarea) {
    const key = `autosave_${textarea.name || textarea.id}`;
    localStorage.setItem(key, textarea.value);

    // Show save indicator
    showSaveIndicator(textarea);
}

/**
 * Show save indicator
 */
function showSaveIndicator(element) {
    const indicator = document.createElement('span');
    indicator.className = 'save-indicator';
    indicator.innerHTML = '<i class="fas fa-check text-success"></i> Saved';

    element.parentNode.appendChild(indicator);

    setTimeout(() => {
        indicator.remove();
    }, 2000);
}

/**
 * Monitor performance
 */
function monitorPerformance() {
    if ('performance' in window) {
        window.addEventListener('load', function() {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log(`🎯 Page loaded in ${loadTime}ms`);
        });
    }
}

/**
 * Toggle theme (if needed)
 */
function toggleTheme() {
    const currentTheme = AppState.theme;
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.body.classList.remove(`${currentTheme}-theme`);
    document.body.classList.add(`${newTheme}-theme`);

    AppState.theme = newTheme;
    localStorage.setItem('preferred-theme', newTheme);
}

/**
 * Utility function: Throttle
 */
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Utility function: Debounce
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

/**
 * Format timestamp as human-readable string
 */
function formatTimestamp(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
}

/**
 * Format number with commas
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Show confirmation dialog
 */
function confirmAction(title, message, callback) {
    const modal = createConfirmationModal(title, message, callback);
    document.body.appendChild(modal);

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Clean up after modal is hidden
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

/**
 * Create confirmation modal
 */
function createConfirmationModal(title, message, callback) {
    const modalId = 'confirm-modal-' + Date.now();
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.setAttribute('tabindex', '-1');

    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle text-warning me-2"></i>
                        ${title}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">${message}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-danger" id="${modalId}-confirm">
                        <i class="fas fa-check me-2"></i>Confirm
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add confirm event listener
    modal.querySelector(`#${modalId}-confirm`).addEventListener('click', function() {
        bootstrap.Modal.getInstance(modal).hide();
        callback();
    });

    return modal;
}

/**
 * Safely manage Bootstrap modals to prevent flashing and duplicate instances
 */
function safelyManageModals() {
    console.log('🔧 [Modal Debug] safelyManageModals called');
    
    // Only initialize once to prevent duplicate event handlers
    if (window.modalManagerInitialized) {
        console.log('⚠️ [Modal Debug] Modal manager already initialized, skipping');
        return;
    }
    window.modalManagerInitialized = true;
    console.log('✅ [Modal Debug] Initializing modal manager for first time');

    // Global modal event listeners to handle cleanup
    $(document).on('hidden.bs.modal.modalManager', '.modal', function() {
        const modal = $(this);
        const modalId = modal.attr('id');
        console.log(`🗑️ [Modal Debug] Modal hidden event fired for: ${modalId}`);
        
        // Clean up any remaining backdrops
        setTimeout(() => {
            const backdrops = $('.modal-backdrop.fade');
            console.log(`🧹 [Modal Debug] Cleaning up ${backdrops.length} backdrops`);
            backdrops.remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        }, 150);
    });

    // Prevent body scrolling when modal is shown
    $(document).on('shown.bs.modal.modalManager', '.modal', function() {
        const modal = $(this);
        const modalId = modal.attr('id');
        console.log(`👁️ [Modal Debug] Modal shown event fired for: ${modalId}`);
        $('body').addClass('modal-open');
    });

    // Monitor all Bootstrap modal events for debugging
    $(document).on('show.bs.modal', '.modal', function() {
        const modalId = $(this).attr('id');
        console.log(`📋 [Modal Debug] Bootstrap show.bs.modal event for: ${modalId}`);
    });

    $(document).on('hide.bs.modal', '.modal', function() {
        const modalId = $(this).attr('id');
        console.log(`📋 [Modal Debug] Bootstrap hide.bs.modal event for: ${modalId}`);
    });

    $(document).on('showing.bs.modal', '.modal', function() {
        const modalId = $(this).attr('id');
        console.log(`📋 [Modal Debug] Bootstrap showing.bs.modal event for: ${modalId}`);
    });

    $(document).on('hiding.bs.modal', '.modal', function() {
        const modalId = $(this).attr('id');
        console.log(`📋 [Modal Debug] Bootstrap hiding.bs.modal event for: ${modalId}`);
    });

    // Monitor backdrop clicks
    $(document).on('click', '.modal-backdrop', function(e) {
        console.log(`🎭 [Modal Debug] Backdrop clicked`);
    });

    // Monitor modal clicks
    $(document).on('click', '.modal', function(e) {
        const modalId = $(this).attr('id');
        const target = e.target;
        console.log(`🎭 [Modal Debug] Modal clicked: ${modalId}, target: ${target.tagName}.${target.className}`);
    });

    // Handle escape key properly
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            console.log('⌨️ [Modal Debug] Escape key pressed');
            const openModal = $('.modal.show').last();
            if (openModal.length && !openModal.attr('data-bs-backdrop') === 'static') {
                const modalInstance = bootstrap.Modal.getInstance(openModal[0]);
                if (modalInstance) {
                    console.log(`🚪 [Modal Debug] Hiding modal via escape key: ${openModal.attr('id')}`);
                    modalInstance.hide();
                }
            }
        }
    });
    
    console.log('✅ [Modal Debug] Modal manager initialization complete');
}

/**
 * Safely show a Bootstrap modal
 */
function safelyShowModal(modalId) {
    console.log(`🎭 [Modal Debug] safelyShowModal called for: ${modalId}`);
    
    const modalElement = document.getElementById(modalId);
    if (!modalElement) {
        console.warn(`❌ [Modal Debug] Modal with ID ${modalId} not found`);
        return false;
    }

    // Check if modal is already shown
    if ($(modalElement).hasClass('show')) {
        console.log(`⏭️ [Modal Debug] Modal ${modalId} is already shown, skipping`);
        return true;
    }

    console.log(`🔍 [Modal Debug] Checking for other open modals...`);
    // Hide any other open modals first
    $('.modal.show').each(function() {
        const existingId = $(this).attr('id');
        console.log(`🚪 [Modal Debug] Hiding existing modal: ${existingId}`);
        const existingInstance = bootstrap.Modal.getInstance(this);
        if (existingInstance) {
            existingInstance.hide();
        }
    });

    // Small delay to ensure previous modal is closed
    setTimeout(() => {
        console.log(`⏳ [Modal Debug] Creating new modal instance for: ${modalId}`);
        
        // Clean up any existing instance
        const existingInstance = bootstrap.Modal.getInstance(modalElement);
        if (existingInstance) {
            console.log(`🗑️ [Modal Debug] Disposing existing instance for: ${modalId}`);
            existingInstance.dispose();
        }

        // Create new instance with proper options
        const modalInstance = new bootstrap.Modal(modalElement, {
            backdrop: modalElement.getAttribute('data-bs-backdrop') || 'static',
            keyboard: modalElement.getAttribute('data-bs-keyboard') !== 'false'
        });

        console.log(`🎬 [Modal Debug] Showing modal: ${modalId}`);
        // Show the modal
        modalInstance.show();
    }, 150);

    return true;
}

/**
 * Safely hide a Bootstrap modal
 */
function safelyHideModal(modalId) {
    console.log(`🚪 [Modal Debug] safelyHideModal called for: ${modalId}`);
    
    const modalElement = document.getElementById(modalId);
    if (!modalElement) {
        console.warn(`❌ [Modal Debug] Modal element not found: ${modalId}`);
        return false;
    }

    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
        console.log(`🎭 [Modal Debug] Hiding modal instance: ${modalId}`);
        modalInstance.hide();
        
        // Ensure cleanup happens
        setTimeout(() => {
            console.log(`🧹 [Modal Debug] Cleaning up after hiding: ${modalId}`);
            $('.modal-backdrop.fade').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        }, 300);
        
        return true;
    } else {
        console.warn(`⚠️ [Modal Debug] No modal instance found for: ${modalId}`);
    }
    return false;
}

// Export functions for global use
window.AppUtils = {
    showNotification,
    confirmAction,
    formatTimestamp,
    formatNumber,
    handleButtonClick,
    restoreButton,
    safelyShowModal,
    safelyHideModal,
    safelyManageModals
};

// Add CSS for animations and notifications
const style = document.createElement('style');
style.textContent = `
    .form-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(42, 42, 42, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: var(--radius-md);
    }

    .loading-content {
        text-align: center;
        color: var(--text-primary);
    }

    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 9999;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
        min-width: 300px;
        max-width: 400px;
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification.hide {
        transform: translateX(100%);
    }

    .notification-content {
        display: flex;
        align-items: center;
        padding: 1rem;
        gap: 0.75rem;
    }

    .notification-icon {
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .notification-success .notification-icon {
        color: var(--accent-success);
    }

    .notification-error .notification-icon {
        color: var(--accent-danger);
    }

    .notification-warning .notification-icon {
        color: var(--accent-warning);
    }

    .notification-info .notification-icon {
        color: var(--accent-info);
    }

    .notification-text {
        flex: 1;
    }

    .notification-title {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .notification-message {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .notification-close {
        background: none;
        border: none;
        color: var(--text-muted);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--radius-sm);
        transition: var(--transition-fast);
    }

    .notification-close:hover {
        color: var(--text-primary);
        background: var(--bg-tertiary);
    }

    .ripple-effect {
        position: absolute;
        border-radius: 50%;
        background: rgba(108, 92, 231, 0.3);
        pointer-events: none;
        animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        100% {
            transform: scale(4);
            opacity: 0;
        }
    }

    .animate-in {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .navbar-scrolled {
        background: rgba(45, 45, 45, 0.95) !important;
        backdrop-filter: blur(10px);
    }

    .save-indicator {
        position: absolute;
        top: -1.5rem;
        right: 0;
        font-size: 0.75rem;
        background: var(--bg-card);
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
        animation: fadeInOut 2s ease-in-out;
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0; transform: translateY(5px); }
        20%, 80% { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
