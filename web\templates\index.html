{% extends "base.html" %}

{% block title %}Home - Google Form AutoFill{% endblock %}

{% block content %}
<style>
    /* Custom styles for optimized table */
    .forms-table {
        table-layout: fixed;
        width: 100%;
    }

    .forms-table th,
    .forms-table td {
        vertical-align: middle !important;
    }

    /* Column width distribution */
    .forms-table .col-title {
        width: 35%;
    }

    .forms-table .col-questions {
        width: 10%;
        text-align: center;
    }

    .forms-table .col-ready {
        width: 15%;
        text-align: center;
    }

    .forms-table .col-review {
        width: 15%;
        text-align: center;
    }

    .forms-table .col-actions {
        width: 25%;
        text-align: center;
    }

    /* Form title handling */
    .form-title-container {
        display: flex;
        align-items: center;
        min-height: 60px;
    }

    .form-title-text {
        flex: 1;
        min-width: 0; /* Allow text to shrink */
    }

    .form-title-main {
        font-weight: 600;
        margin-bottom: 4px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: 1.3;
        max-height: 2.6em; /* 2 lines max */
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .form-title-subtitle {
        font-size: 0.8em;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Badge containers for consistent spacing */
    .badge-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-height: 45px;
        justify-content: center;
    }

    /* Enhanced Ready column badge container for better alignment */
    .ready-badge-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
        min-height: 50px;
        width: 100%;
    }

    .ready-badge-main {
        font-size: 0.9em;
        padding: 6px 12px;
        min-width: 35px;
        font-weight: 600;
        border-radius: 12px;
    }

    .ready-badge-subtitle {
        font-size: 0.7em;
        line-height: 1.1;
        text-align: center;
        font-weight: 500;
        margin: 0;
        opacity: 0.8;
    }

    .badge-main {
        font-size: 0.9em;
        padding: 6px 12px;
        min-width: 30px;
    }

    .badge-subtitle {
        font-size: 0.75em;
        line-height: 1.2;
        text-align: center;
    }

    /* Action buttons optimization */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
        justify-content: center;
        align-items: center;
        min-height: 60px;
    }

    .btn-action {
        font-size: 0.8em;
        padding: 4px 8px;
        min-width: 60px;
        white-space: nowrap;
    }

    /* Custom dark theme tooltip/popover styles */
    .popover {
        --bs-popover-max-width: 300px;
        --bs-popover-border-color: #495057;
        --bs-popover-header-bg: #343a40;
        --bs-popover-header-color: #ffffff;
        --bs-popover-body-bg: #2d3436;
        --bs-popover-body-color: #ffffff;
        --bs-popover-arrow-border: #495057;
        border: 1px solid var(--bs-popover-border-color) !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.35) !important;
    }

    .popover-header {
        background-color: var(--bs-popover-header-bg) !important;
        color: var(--bs-popover-header-color) !important;
        border-bottom: 1px solid #495057 !important;
        font-weight: 600;
    }

    .popover-body {
        background-color: var(--bs-popover-body-bg) !important;
        color: var(--bs-popover-body-color) !important;
        font-size: 0.9em;
        line-height: 1.4;
    }

    .popover-body strong {
        color: #74b9ff !important;
        font-weight: 600;
    }

    /* Arrow styling for dark theme */
    .popover.bs-popover-top > .popover-arrow::before,
    .popover.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before {
        border-top-color: var(--bs-popover-border-color) !important;
    }

    .popover.bs-popover-top > .popover-arrow::after,
    .popover.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after {
        border-top-color: var(--bs-popover-body-bg) !important;
    }

    .popover.bs-popover-right > .popover-arrow::before,
    .popover.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before {
        border-right-color: var(--bs-popover-border-color) !important;
    }

    .popover.bs-popover-right > .popover-arrow::after,
    .popover.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after {
        border-right-color: var(--bs-popover-body-bg) !important;
    }

    .popover.bs-popover-bottom > .popover-arrow::before,
    .popover.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before {
        border-bottom-color: var(--bs-popover-border-color) !important;
    }

    .popover.bs-popover-bottom > .popover-arrow::after,
    .popover.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after {
        border-bottom-color: var(--bs-popover-body-bg) !important;
    }

    .popover.bs-popover-left > .popover-arrow::before,
    .popover.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before {
        border-left-color: var(--bs-popover-border-color) !important;
    }

    .popover.bs-popover-left > .popover-arrow::after,
    .popover.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
        border-left-color: var(--bs-popover-body-bg) !important;
    }

    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .btn-action {
            font-size: 0.75em;
            padding: 3px 6px;
            min-width: 50px;
        }

        .btn-action i {
            display: none; /* Hide icons on smaller screens */
        }
    }

    @media (max-width: 992px) {
        .forms-table .col-title {
            width: 40%;
        }

        .forms-table .col-actions {
            width: 30%;
        }

        .forms-table .col-questions,
        .forms-table .col-ready,
        .forms-table .col-review {
            width: 10%;
        }
    }

    /* Hover effects */
    .forms-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transform: none; /* Remove transform to prevent layout shift */
    }

    /* Info button styling */
    .info-btn {
        margin-left: 4px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
</style>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4 fade-in-up">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-magic me-2"></i><span class="text-gradient">Google Form AutoFill</span> & Submit</h4>
            </div>
            <div class="card-body">
                <p class="lead">
                    🚀 Advanced automation tool for Google Forms with AI-powered response generation using <strong>Google's Gemini AI</strong>. Create, review, and submit forms with intelligent responses tailored to your needs.
                </p>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card h-100 hover-lift">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Load a New Form</h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-file-import fa-3x text-info mb-3"></i>
                                </div>
                                <p>Load a Google Form to start generating intelligent responses and automate submission.</p>
                                <a href="{{ url_for('load_form') }}" class="btn btn-info btn-lg">
                                    <i class="fas fa-plus-circle me-2"></i>Load Form
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100 hover-lift">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuration</h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-robot fa-3x text-warning mb-3"></i>
                                </div>
                                <p>Configure your Gemini AI API key and personalize settings for optimal performance.</p>
                                <a href="{{ url_for('config') }}" class="btn btn-warning btn-lg">
                                    <i class="fas fa-cog me-2"></i>Configure AI
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if saved_forms %}
        <div class="card fade-in-up">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-database me-2"></i>Saved Forms <span class="badge bg-light text-dark ms-2">{{ saved_forms|length }}</span></h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover forms-table">
                        <thead class="table-dark">
                            <tr>
                                <th class="col-title">
                                    <i class="fas fa-file-alt me-2"></i>Form Title
                                </th>
                                <th class="col-questions">
                                    <i class="fas fa-question-circle me-1"></i>
                                    <span class="d-none d-md-inline">Questions</span>
                                </th>
                                <th class="col-ready">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span class="d-none d-lg-inline">Ready</span>
                                </th>
                                <th class="col-review">
                                    <i class="fas fa-comments me-1"></i>
                                    <span class="d-none d-lg-inline">Review</span>
                                </th>
                                <th class="col-actions">
                                    <i class="fas fa-tools me-1"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for form in saved_forms %}
                            <tr>
                                <td class="col-title">
                                    <div class="form-title-container">
                                        <i class="fas fa-file-alt text-primary me-2 flex-shrink-0"></i>
                                        <div class="form-title-text">
                                            <div class="form-title-main" title="{{ form.title }}">
                                                {{ form.title }}
                                            </div>
                                            <div class="form-title-subtitle" title="{{ form.required_questions }} required, {{ form.optional_questions }} optional">
                                                {{ form.required_questions }} req, {{ form.optional_questions }} opt
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="col-questions">
                                    <div class="badge-container">
                                        <span class="badge bg-info badge-main">{{ form.question_count }}</span>
                                    </div>
                                </td>
                                <td class="col-ready">
                                    <div class="ready-badge-container">
                                        {% if form.submittable_count > 0 %}
                                            <span class="badge bg-success ready-badge-main">{{ form.submittable_count }}</span>
                                            <small class="ready-badge-subtitle text-success">Ready</small>
                                        {% else %}
                                            <span class="badge bg-warning text-dark ready-badge-main">0</span>
                                            <small class="ready-badge-subtitle text-warning">Needed</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="col-review">
                                    <div class="badge-container">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <span class="badge bg-secondary badge-main">{{ form.total_review_responses }}</span>
                                            {% if form.total_review_responses > 0 %}
                                                <button type="button" class="btn btn-sm btn-outline-info info-btn"
                                                        data-bs-toggle="popover"
                                                        data-bs-placement="left"
                                                        data-bs-html="true"
                                                        data-bs-content="<div class='text-start'><strong>Required:</strong><br>
                                                        {% for req in form.breakdown.required_responses %}
                                                        • {{ req.title[:20] }}{% if req.title|length > 20 %}...{% endif %}: {{ req.response_count }}<br>
                                                        {% endfor %}
                                                        {% if form.breakdown.optional_responses %}
                                                        <strong>Optional:</strong><br>
                                                        {% for opt in form.breakdown.optional_responses %}
                                                        • {{ opt.title[:20] }}{% if opt.title|length > 20 %}...{% endif %}: {{ opt.response_count }}<br>
                                                        {% endfor %}
                                                        {% endif %}</div>"
                                                        title="Response Breakdown">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="col-actions">
                                    <div class="action-buttons">
                                        <a href="{{ url_for('form_details', form_id=form.id) }}"
                                           class="btn btn-sm btn-info btn-action" title="View Details">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="{{ url_for('generate_responses', form_id=form.id) }}"
                                           class="btn btn-sm btn-success btn-action" title="Generate Responses">
                                            <i class="fas fa-magic me-1"></i>Generate
                                        </a>
                                        <a href="{{ url_for('review_responses', form_id=form.id) }}"
                                           class="btn btn-sm btn-warning btn-action" title="Review Responses">
                                            <i class="fas fa-edit me-1"></i>Review
                                        </a>
                                        <a href="{{ url_for('customer_form', form_id=form.id) }}"
                                           class="btn btn-sm btn-secondary btn-action" title="Customer Interface">
                                            <i class="fas fa-user-cog me-1"></i>Customer
                                        </a>
                                        <a href="{{ url_for('customer_form_wizard', form_id=form.id) }}"
                                           class="btn btn-sm btn-outline-secondary btn-action" title="Wizard Interface">
                                            <i class="fas fa-magic me-1"></i>Wizard
                                        </a>
                                        {% if form.submittable_count > 0 %}
                                        <a href="{{ url_for('submit_form', form_id=form.id) }}"
                                           class="btn btn-sm btn-primary btn-action" title="Submit Form">
                                            <i class="fas fa-paper-plane me-1"></i>Submit
                                        </a>
                                        {% else %}
                                        <button class="btn btn-sm btn-primary btn-action disabled"
                                                title="Need responses for required questions" disabled>
                                            <i class="fas fa-paper-plane me-1"></i>Submit
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-sm btn-danger btn-action delete-form-btn"
                                                data-bs-toggle="modal" data-bs-target="#deleteFormModal{{ form.id }}"
                                                title="Delete Form">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card fade-in-up">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-file-import fa-5x text-light mb-3"></i>
                </div>
                <h5 class="text-light mb-3">No Forms Found</h5>
                <p class="text-light mb-4">Get started by loading your first Google Form and experience the power of AI-driven automation.</p>
                <a href="{{ url_for('load_form') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus-circle me-2"></i>Load Your First Form
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Delete Form Modals -->
        {% for form in saved_forms %}
        <div class="modal fade" id="deleteFormModal{{ form.id }}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Deletion</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this form?</p>
                        <p><strong>Form Title:</strong> {{ form.title }}</p>
                        <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. All responses and settings for this form will be deleted.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger confirm-delete-btn" data-form-id="{{ form.id }}">Delete</button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize Bootstrap popovers for response breakdown
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl, {
                trigger: 'hover focus',
                delay: { "show": 300, "hide": 100 }
            });
        });

        // Delete form
        $('.confirm-delete-btn').click(function() {
            const formId = $(this).data('form-id');

            $.ajax({
                url: '/api/form/' + formId,
                type: 'DELETE',
                success: function(response) {
                    location.reload();
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        });

        // Add loading state for action buttons
        $('.btn-action').click(function() {
            if (!$(this).hasClass('delete-form-btn') && !$(this).is(':disabled')) {
                $(this).prop('disabled', true).append(' <i class="fas fa-spinner fa-spin"></i>');
            }
        });
    });
</script>
{% endblock %}
