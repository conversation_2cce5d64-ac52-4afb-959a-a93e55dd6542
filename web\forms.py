"""
Forms Module for Google Form AutoFill Web Interface

This module defines the forms used in the web interface.
"""

from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import StringField, IntegerField, FloatField, SelectField, SubmitField, FieldList, FormField
from wtforms.validators import DataRequired, URL, NumberRange, Optional


class LoadFormForm(FlaskForm):
    """Form for loading a Google Form"""
    url = StringField('Google Form URL', validators=[DataRequired(), URL()])
    submit = SubmitField('Load Form')


class GenerateResponsesForm(FlaskForm):
    """Form for generating responses"""
    method = SelectField('Generation Method', choices=[
        ('manual', 'Manual Input'),
        ('ai_assisted', 'AI-Assisted'),
        ('fully_automated', 'Fully Automated'),
        ('batch_optimized', 'Batch Optimized (Most Efficient)')
    ], validators=[DataRequired()])

    provider = SelectField('AI Provider', choices=[
        ('gemini', 'Google Gemini'),
        ('openrouter', 'OpenRouter')
    ], default='gemini')

    model = StringField('AI Model', validators=[Optional()], 
                       render_kw={"placeholder": "Select a model or enter custom model ID"})

    sample_count = IntegerField('Sample Count', validators=[NumberRange(min=1, max=100)], default=5)
    api_key = StringField('API Key (Optional - uses configured keys if empty)')
    examples_file = FileField('Examples File (Optional)', validators=[
        Optional(),
        FileAllowed(['json'], 'JSON files only!')
    ])

    submit = SubmitField('Generate Responses')


class SubmitFormForm(FlaskForm):
    """Form for submitting form responses"""
    url = StringField('Google Form URL', validators=[DataRequired(), URL()])
    count = IntegerField('Number of Submissions', validators=[NumberRange(min=1, max=10000)], default=5)
    delay_min = IntegerField('Minimum Delay (milliseconds)', validators=[NumberRange(min=50, max=60000)], default=1000)
    delay_max = IntegerField('Maximum Delay (milliseconds)', validators=[NumberRange(min=50, max=60000)], default=3000)
    max_workers = IntegerField('Max Threads (1-50)', validators=[NumberRange(min=1, max=50)], default=5)

    submit = SubmitField('Start Submission')


class ApiKeyForm(FlaskForm):
    """Form for configuring API keys and system settings"""
    # API Provider Settings
    default_provider = SelectField('Default AI Provider', choices=[
        ('gemini', 'Google Gemini'),
        ('openrouter', 'OpenRouter')
    ], default='gemini')

    # Gemini Settings
    gemini_enabled = SelectField('Enable Gemini', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')
    gemini_api_keys = StringField('Gemini API Keys (comma-separated for multiple keys)')
    gemini_default_model = StringField('Gemini Default Model', default='gemini-2.0-flash-lite')
    gemini_key_strategy = SelectField('Gemini Key Strategy', choices=[
        ('round_robin', 'Round Robin'),
        ('random', 'Random'),
        ('least_used', 'Least Used')
    ], default='round_robin')

    # OpenRouter Settings
    openrouter_enabled = SelectField('Enable OpenRouter', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')
    openrouter_api_keys = StringField('OpenRouter API Keys (comma-separated for multiple keys)')
    openrouter_default_model = StringField('OpenRouter Default Model', default='openai/gpt-3.5-turbo')
    openrouter_key_strategy = SelectField('OpenRouter Key Strategy', choices=[
        ('round_robin', 'Round Robin'),
        ('random', 'Random'),
        ('least_used', 'Least Used')
    ], default='round_robin')

    # Customer Plugin Settings
    customer_plugin_enabled = SelectField('Enable Customer Plugin', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    # Response Limits
    max_responses_per_customer = IntegerField('Maximum Responses per Customer',
                                             validators=[NumberRange(min=1, max=10000)],
                                             default=100)

    # Feature Toggles
    ai_generation_enabled = SelectField('AI Generation', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    manual_review_enabled = SelectField('Manual Review', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    batch_submission_enabled = SelectField('Batch Submission', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    # Logging Settings
    log_level = SelectField('Log Level', choices=[
        ('DEBUG', 'Debug'),
        ('INFO', 'Info'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error')
    ], default='INFO')

    file_logging_enabled = SelectField('File Logging', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    console_logging_enabled = SelectField('Console Logging', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

    submit = SubmitField('Save Configuration')


class AddResponseForm(FlaskForm):
    """Form for adding a response"""
    text = StringField('Response Text', validators=[DataRequired()])
    weight = IntegerField('Weight', validators=[NumberRange(min=1)], default=1)

    submit = SubmitField('Add Response')


class EditResponseForm(FlaskForm):
    """Form for editing a response"""
    text = StringField('Response Text', validators=[DataRequired()])
    weight = IntegerField('Weight', validators=[NumberRange(min=1)], default=1)

    submit = SubmitField('Update Response')


class CustomerFormConfigForm(FlaskForm):
    """Form for customer configuration of form responses"""
    total_responses = IntegerField('Total Number of Responses Needed',
                                  validators=[NumberRange(min=1, max=10000)],
                                  default=10)
    submit = SubmitField('Save Configuration')
