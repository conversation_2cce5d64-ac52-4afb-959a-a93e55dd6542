{% extends "base.html" %}

{% block title %}Generate Responses - Google Form AutoFill{% endblock %}

{% block head %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Custom Dark Theme CSS for Select2 -->
<style>
/* Select2 Dark Theme Overrides */
.select2-container--bootstrap-5 .select2-dropdown {
    background-color: #2d3436 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search__field {
    background-color: #343a40 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search__field::placeholder {
    color: #adb5bd !important;
}

.select2-container--bootstrap-5 .select2-results__option {
    background-color: #2d3436 !important;
    color: #f8f9fa !important;
}

.select2-container--bootstrap-5 .select2-results__option:hover,
.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: #495057 !important;
    color: #ffffff !important;
}

.select2-container--bootstrap-5 .select2-results__option[aria-selected="true"] {
    background-color: #6c5ce7 !important;
    color: #ffffff !important;
}

.select2-container--bootstrap-5 .select2-selection--single {
    background-color: #343a40 !important;
    border: 1px solid #495057 !important;
    color: #f8f9fa !important;
    height: auto !important;
    min-height: 38px !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    color: #f8f9fa !important;
    padding: 0.375rem 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
    color: #adb5bd !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
    border-color: #adb5bd transparent transparent transparent !important;
}

.select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #adb5bd transparent !important;
}

/* Custom result template styling */
.select2-result-model__title {
    color: #ffffff !important;
    font-weight: 600 !important;
    margin-bottom: 2px !important;
}

.select2-result-model__description {
    color: #adb5bd !important;
    font-size: 0.875rem !important;
    margin-bottom: 2px !important;
}

.select2-result-model__details {
    color: #74b9ff !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
}

/* Focus states */
.select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
    border-color: #74b9ff !important;
    box-shadow: 0 0 0 0.2rem rgba(116, 185, 255, 0.25) !important;
}

/* Loading state */
.select2-container--bootstrap-5 .select2-results__message {
    color: #adb5bd !important;
    background-color: #2d3436 !important;
}

/* Ensure proper text contrast in all states */
.select2-container--bootstrap-5 * {
    color: inherit !important;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-magic me-2"></i>Generate Responses for {{ form_data.form_title }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p>
                            <strong>Form ID:</strong> {{ form_id }}<br>
                            <strong>Questions:</strong> {{ form_data.questions|length }}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i>View Form
                            </a>
                            <a href="{{ url_for('review_responses', form_id=form_id) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Review Responses
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Response Generation Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('generate_responses', form_id=form_id) }}" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.method.label(class="form-label") }}
                                {{ form.method(class="form-select") }}
                                {% if form.method.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.method.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <ul>
                                        <li><strong>Manual Input:</strong> Initialize the form for manual input of responses</li>
                                        <li><strong>AI-Assisted:</strong> Generate responses using AI with user review</li>
                                        <li><strong>Fully Automated:</strong> Generate and weight responses automatically using AI</li>
                                        <li><strong>Batch Optimized:</strong> Generate responses for all questions in one API call (most efficient)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.sample_count.label(class="form-label") }}
                                {{ form.sample_count(class="form-control") }}
                                {% if form.sample_count.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.sample_count.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Number of samples to generate per question (1-100)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="ai-settings">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.provider.label(class="form-label") }}
                                {{ form.provider(class="form-select", id="ai-provider-select") }}
                                {% if form.provider.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.provider.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Select the AI provider to use for generating responses.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.model.label(class="form-label") }}
                                <select id="ai-model-select" name="model" class="form-control">
                                    <option value="{{ form.model.data or '' }}">{{ form.model.data or 'Select a model...' }}</option>
                                </select>
                                {% if form.model.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.model.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Select the AI model to use. For OpenRouter, you can search through available models.
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="refreshModels">
                                        <i class="fas fa-sync-alt me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.api_key.label(class="form-label") }}
                        {{ form.api_key(class="form-control") }}
                        {% if form.api_key.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.api_key.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Optional: Provide a specific API key for this request. If left empty, the configured keys will be used.
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.examples_file.label(class="form-label") }}
                        {{ form.examples_file(class="form-control") }}
                        {% if form.examples_file.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.examples_file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Optional JSON file with example responses. If not provided, Gemini will generate all responses from scratch.
                            <br><br>
                            Format example:
                            <pre><code>{
  "question_id_1": [
    "Example response 1",
    "Example response 2"
  ],
  "question_id_2": [
    "Example response 1",
    "Example response 2"
  ]
}</code></pre>
                        </div>
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-success btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        // Get form elements
        const methodSelect = document.getElementById('method');
        const aiSettings = document.getElementById('ai-settings');
        const providerSelect = document.getElementById('ai-provider-select');
        const modelSelect = document.getElementById('ai-model-select');
        
        let modelSelectInstance = null;

        // Initialize Select2 for model dropdown
        function initializeModelSelect() {
            if (modelSelectInstance) {
                modelSelectInstance.destroy();
            }
            
            const provider = providerSelect.value;
            
            if (provider === 'openrouter') {
                // Use Select2 with AJAX for OpenRouter
                modelSelectInstance = $(modelSelect).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Search and select a model...',
                    allowClear: true,
                    ajax: {
                        url: '/api/openrouter/models',
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                search: params.term || '',
                                page: params.page || 1
                            };
                        },
                        processResults: function (data) {
                            if (data.success) {
                                return {
                                    results: data.models
                                };
                            } else {
                                return {
                                    results: []
                                };
                            }
                        },
                        cache: true
                    },
                    minimumInputLength: 0,
                    templateResult: function(model) {
                        if (model.loading) {
                            return model.text;
                        }

                        // Create formatted display for OpenRouter models
                        const $container = $(
                            '<div class="select2-result-model clearfix">' +
                                '<div class="select2-result-model__meta">' +
                                    '<div class="select2-result-model__title"></div>' +
                                    '<div class="select2-result-model__description"></div>' +
                                    '<div class="select2-result-model__details"></div>' +
                                '</div>' +
                            '</div>'
                        );

                        $container.find('.select2-result-model__title').text(model.name || model.id);
                        
                        const description = model.description || 'No description available';
                        $container.find('.select2-result-model__description').text(
                            description.length > 80 ? description.substring(0, 80) + '...' : description
                        );
                        
                        // Add context length and pricing info
                        let details = [];
                        if (model.context_length) {
                            details.push(`Context: ${model.context_length.toLocaleString()}`);
                        }
                        if (model.pricing && model.pricing.prompt) {
                            details.push(`$${model.pricing.prompt}/1K tokens`);
                        }
                        if (model.input_modalities && model.input_modalities.includes('image')) {
                            details.push('🖼️ Image support');
                        }
                        
                        if (details.length > 0) {
                            $container.find('.select2-result-model__details').text(details.join(' • '));
                        }

                        return $container;
                    },
                    templateSelection: function(model) {
                        return model.name || model.text || model.id;
                    }
                });
                
                // Set current value if exists
                const currentValue = '{{ form.model.data or "" }}';
                if (currentValue) {
                    const option = new Option(currentValue, currentValue, true, true);
                    $(modelSelect).append(option).trigger('change');
                }
            } else {
                // Use regular Select2 for other providers (like Gemini)
                modelSelectInstance = $(modelSelect).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select a model...',
                    allowClear: true
                });
                
                // Load models for non-OpenRouter providers
                loadModelsForProvider(provider);
            }
        }

        // Function to load models for non-OpenRouter providers
        function loadModelsForProvider(provider) {
            if (provider === 'openrouter') return; // OpenRouter uses AJAX loading
            
            // Clear current options and show loading
            $(modelSelect).empty().append('<option value="">Loading models...</option>');

            // Fetch models for the selected provider
            fetch(`/api/models/${provider}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.models) {
                        // Clear loading option
                        $(modelSelect).empty();

                        // Add default option
                        $(modelSelect).append('<option value="">Select a model</option>');

                        // Add models to select
                        data.models.forEach(model => {
                            // Create model name with context length if available
                            let displayName = model.name || model.id;
                            if (model.context_length) {
                                displayName += ` (${Math.round(model.context_length / 1000)}k ctx)`;
                            }

                            // Add modality icons if available
                            const hasImage = model.input_modalities && model.input_modalities.includes('image');
                            if (hasImage) {
                                displayName = '🖼️ ' + displayName;
                            }

                            $(modelSelect).append(`<option value="${model.id}">${displayName}</option>`);
                        });
                    } else {
                        $(modelSelect).empty().append('<option value="">No models available</option>');
                    }
                })
                .catch(error => {
                    console.error('Error loading models:', error);
                    $(modelSelect).empty().append('<option value="">Error loading models</option>');
                });
        }

        // Show/hide API key field and AI settings based on method
        $('#method').change(function() {
            if ($(this).val() === 'manual') {
                $('#api_key').parent().hide();
                $('#ai-settings').hide();
            } else {
                $('#api_key').parent().show();
                $('#ai-settings').show();
            }
        });

        // Highlight batch_optimized as recommended
        $('#method option[value="batch_optimized"]').text($('#method option[value="batch_optimized"]').text() + ' (Recommended)');

        // Default to batch_optimized method if not already set
        if (!$('#method').val()) {
            $('#method').val('batch_optimized');
        }

        // Add event listener for provider change
        providerSelect.addEventListener('change', function() {
            initializeModelSelect();
        });

        // Add event listener for refresh models button
        $('#refreshModels').click(function() {
            const $button = $(this);
            const originalText = $button.html();
            
            // Show loading state
            $button.html('<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...').prop('disabled', true);
            
            // Reinitialize the model select
            setTimeout(() => {
                initializeModelSelect();
                $button.html(originalText).prop('disabled', false);
                
                // Show success message
                const alertDiv = $('<div class="alert alert-success alert-dismissible fade show mt-2">' +
                    'Models refreshed successfully! ' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');
                $button.parent().append(alertDiv);
                
                // Auto-dismiss after 3 seconds
                setTimeout(() => alertDiv.remove(), 3000);
            }, 100);
        });

        // Initialize model select on page load
        initializeModelSelect();

        // Trigger change event on page load
        $('#method').trigger('change');
    });
</script>
{% endblock %}
