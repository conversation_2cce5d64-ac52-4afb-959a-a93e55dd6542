/* Modern Dark Theme CSS for Google Form AutoFill Web Interface */

:root {
    /* Color Palette */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-card: #2a2a2a;
    --bg-card-hover: #343434;

    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;

    --accent-primary: #6c5ce7;
    --accent-secondary: #a29bfe;
    --accent-success: #00b894;
    --accent-warning: #fdcb6e;
    --accent-danger: #e17055;
    --accent-info: #74b9ff;

    --border-color: #404040;
    --border-light: #505050;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);

    /* Border radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, var(--bg-primary) 0%, #1e1e1e 100%);
    color: var(--text-primary);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding-bottom: 60px;
    transition: var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.75rem;
}

h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.375rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Ensure all text elements use proper colors */
strong, b {
    color: var(--text-primary) !important;
    font-weight: 600;
}

span, div, p, li, td, th {
    color: var(--text-primary);
}

p {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, #333333 100%) !important;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    color: var(--text-primary) !important;
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--accent-primary) !important;
    transform: none !important;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: var(--transition-fast);
    border-radius: var(--radius-sm);
    margin: 0 0.25rem;
    padding: 0.5rem 1rem !important;
}

.navbar-nav .nav-link:hover {
    color: var(--text-primary) !important;
    background-color: rgba(108, 92, 231, 0.1);
    transform: none !important;
}

/* Container */
.container {
    max-width: 1100px;
    padding: 0 1rem;
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    transition: var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: none !important;
    box-shadow: var(--shadow-lg);
    border-color: var(--border-light);
}

.card-header {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    color: var(--text-primary) !important;
    font-weight: 600;
    padding: 1rem 1.25rem;
    border-bottom: none;
}

.card-header * {
    color: var(--text-primary) !important;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--accent-success) 0%, #00a085 100%) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--accent-warning) 0%, #e17055 100%) !important;
    color: #1a1a1a !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--accent-info) 0%, #5a9bd4 100%) !important;
}

.card-body {
    padding: 1.25rem;
    background: var(--bg-card);
}

.card-body * {
    color: var(--text-primary);
}

/* Buttons */
.btn {
    border-radius: var(--radius-sm);
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    transition: var(--transition-fast);
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5b4cdb 0%, #918af5 100%);
    color: var(--text-primary);
    transform: none !important;
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-success) 0%, #00a085 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    background: linear-gradient(135deg, #009975 0%, #008f75 100%);
    color: var(--text-primary);
    transform: none !important;
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-warning) 0%, #e17055 100%);
    color: #1a1a1a;
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #fbb954 0%, #d4634a 100%);
    color: #1a1a1a;
    transform: none !important;
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-danger) 0%, #d63447 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d4634a 0%, #c0392b 100%);
    color: var(--text-primary);
    transform: none !important;
    box-shadow: var(--shadow-md);
}

.btn-info {
    background: linear-gradient(135deg, var(--accent-info) 0%, #5a9bd4 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-info:hover {
    background: linear-gradient(135deg, #5a9bd4 0%, #4a8bc2 100%);
    color: var(--text-primary);
    transform: none !important;
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    border-color: var(--border-light);
    transform: none !important;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Forms */
.form-control {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
}

.form-control:focus {
    background: var(--bg-card);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
}

.form-select:focus {
    background: var(--bg-card);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    color: var(--text-primary);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Tables */
.table {
    color: var(--text-primary);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--bg-tertiary);
    color: var(--text-primary) !important;
    font-weight: 600;
    border-color: var(--border-color);
    padding: 0.75rem;
    font-size: 0.9rem;
}

.table td {
    border-color: var(--border-color);
    padding: 0.75rem;
    vertical-align: middle;
    color: var(--text-primary) !important;
    font-size: 0.9rem;
}

.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(255, 255, 255, 0.02);
}

.table-hover tbody tr:hover {
    background: var(--bg-card-hover);
    transition: var(--transition-fast);
}

/* Badges */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border: 1px solid transparent;
    transition: var(--transition-fast);
}

.badge:hover {
    transform: none !important;
    box-shadow: var(--shadow-sm);
}

.badge.bg-success {
    background: var(--accent-success) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-danger {
    background: var(--accent-danger) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-warning {
    background: var(--accent-warning) !important;
    color: #1a1a1a !important;
    font-weight: 700;
    text-shadow: none;
    border-color: rgba(26, 26, 26, 0.2);
}

.badge.bg-info {
    background: var(--accent-info) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.2);
}

.badge.bg-secondary {
    background: var(--bg-tertiary) !important;
    color: #ffffff !important;
    border-color: var(--border-light);
}

.badge.bg-primary {
    background: var(--accent-primary) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Additional badge variants for better compatibility */
.badge.bg-light {
    background: #f8f9fa !important;
    color: #1a1a1a !important;
    font-weight: 700;
    text-shadow: none;
    border-color: rgba(26, 26, 26, 0.1);
}

.badge.bg-dark {
    background: #212529 !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Text color overrides for badges */
.badge.text-dark {
    color: #1a1a1a !important;
    font-weight: 700;
    text-shadow: none;
}

.badge.text-light {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Alerts */
.alert {
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid;
}

.alert-success {
    background: rgba(0, 184, 148, 0.1);
    border-color: var(--accent-success);
    color: var(--accent-success);
}

.alert-warning {
    background: rgba(253, 203, 110, 0.1);
    border-color: var(--accent-warning);
    color: var(--accent-warning);
}

.alert-danger {
    background: rgba(225, 112, 85, 0.1);
    border-color: var(--accent-danger);
    color: var(--accent-danger);
}

.alert-info {
    background: rgba(116, 185, 255, 0.1);
    border-color: var(--accent-info);
    color: var(--accent-info);
}

/* Accordion */
.accordion {
    border-radius: var(--radius-md);
    overflow: hidden;
}

.accordion-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
}

.accordion-button {
    background: var(--bg-card);
    color: var(--text-primary);
    font-weight: 500;
    padding: 1.5rem;
    border: none;
    transition: var(--transition-fast);
}

.accordion-button:not(.collapsed) {
    background: var(--bg-tertiary);
    color: var(--accent-primary);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
}

.accordion-body {
    background: var(--bg-card);
    padding: 2rem;
    border-top: 1px solid var(--border-color);
}

/* Modals */
.modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
}

.modal-title {
    color: var(--text-primary) !important;
    font-weight: 600;
    font-size: 1.1rem;
}

.modal-body {
    padding: 1.25rem;
    color: var(--text-primary) !important;
}

.modal-body * {
    color: var(--text-primary) !important;
}

.modal-footer {
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.btn-close {
    filter: invert(1);
    opacity: 0.7;
}

.btn-close:hover {
    opacity: 1;
}

/* Modal Tabs Styling */
.modal .nav-tabs {
    border-bottom: 1px solid var(--border-color);
}

.modal .nav-tabs .nav-link {
    color: var(--text-secondary) !important;
    background-color: transparent;
    border: 1px solid transparent;
    border-top-left-radius: var(--radius-sm);
    border-top-right-radius: var(--radius-sm);
    padding: 0.5rem 1rem;
    font-weight: 500;
    margin-bottom: -1px;
}

.modal .nav-tabs .nav-link:hover {
    color: var(--text-primary) !important;
    border-color: transparent;
    background-color: rgba(108, 92, 231, 0.1);
}

.modal .nav-tabs .nav-link.active {
    color: var(--accent-primary) !important;
    background-color: var(--bg-card);
    border-color: var(--border-color);
    border-bottom-color: var(--bg-card);
    font-weight: 600;
}

.modal .tab-content {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-top: none;
    padding: 1rem;
    border-bottom-left-radius: var(--radius-sm);
    border-bottom-right-radius: var(--radius-sm);
}

/* Progress bars */
.progress {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    height: 30px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-bar {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 30px;
    transition: var(--transition-normal);
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    position: fixed;
    bottom: 0;
    width: 100%;
    backdrop-filter: blur(10px);
}

.footer a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer a:hover {
    color: var(--accent-secondary);
}

/* Code blocks */
pre {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
    overflow-x: auto;
}

code {
    color: var(--accent-info);
    background: var(--bg-tertiary);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.875rem;
}

/* Custom utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(42, 42, 42, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hover-lift {
    transition: var(--transition-fast);
}

.hover-lift:hover {
    transform: none !important;
    box-shadow: var(--shadow-lg);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .btn-group {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: var(--radius-sm) !important;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
}

@media (max-width: 576px) {
    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-header,
    .modal-footer {
        padding: 1rem;
    }
}

/* Dark scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Enhanced Question Type Styling - Unified Purple Gradient Design */

/* Grid Configuration Cards */
.grid-config, .checkbox-config, .date-config, .time-config {
    background: var(--bg-card);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.grid-config::before, .checkbox-config::before, .date-config::before, .time-config::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.grid-config h6, .checkbox-config h6, .date-config h6, .time-config h6 {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Combination and Range Items */
.combination-item, .range-item {
    background: var(--bg-card);
    border: 1px solid var(--accent-primary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.combination-item::before, .range-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.combination-item:hover, .range-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--accent-secondary);
}

.combination-item:hover::before, .range-item:hover::before {
    height: 6px;
    background: linear-gradient(135deg, #5b4cdb 0%, #918af5 100%);
}

.combination-item h6, .range-item h6 {
    color: var(--accent-primary) !important;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

/* Add Combination/Range Buttons */
.add-multi-grid-combination, .add-checkbox-grid-combination,
.add-checkbox-combination, .add-date-range, .add-time-range {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) !important;
    border: none !important;
    color: var(--text-primary) !important;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
    text-transform: none;
    font-size: 0.9rem;
}

.add-multi-grid-combination:hover, .add-checkbox-grid-combination:hover,
.add-checkbox-combination:hover, .add-date-range:hover, .add-time-range:hover {
    background: linear-gradient(135deg, #5b4cdb 0%, #918af5 100%) !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    color: var(--text-primary) !important;
}

/* Remove Combination/Range Buttons */
.remove-combination, .remove-range {
    background: linear-gradient(135deg, var(--accent-danger) 0%, #d63447 100%) !important;
    border: none !important;
    color: var(--text-primary) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
    font-size: 0.8rem;
}

.remove-combination:hover, .remove-range:hover {
    background: linear-gradient(135deg, #d4634a 0%, #c0392b 100%) !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    color: var(--text-primary) !important;
}

/* Grid Tables */
.grid-config .table, .checkbox-config .table {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
}

.grid-config .table thead th, .checkbox-config .table thead th {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    color: var(--text-primary) !important;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
    text-align: center;
}

.grid-config .table tbody td, .checkbox-config .table tbody td {
    background: var(--bg-card);
    color: var(--text-primary) !important;
    border-color: var(--border-color);
    padding: 0.75rem;
    vertical-align: middle;
}

.grid-config .table tbody tr:hover, .checkbox-config .table tbody tr:hover {
    background: var(--bg-card-hover);
}

/* Grid Option Badges */
.grid-config .badge, .checkbox-config .badge {
    background: rgba(108, 92, 231, 0.1);
    color: var(--accent-primary) !important;
    border: 1px solid rgba(108, 92, 231, 0.3);
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.grid-config .badge:hover, .checkbox-config .badge:hover {
    background: rgba(108, 92, 231, 0.2);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
}

/* Checkbox Groups */
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.checkbox-group .form-check {
    margin-bottom: 0;
}

.checkbox-group .form-check-input {
    background-color: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
}

.checkbox-group .form-check-input:checked {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.checkbox-group .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    border-color: var(--accent-primary);
}

.checkbox-group .form-check-label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

/* Date and Time Inputs */
.date-config input[type="date"], .time-config input[type="time"],
.combination-item input[type="date"], .combination-item input[type="time"],
.range-item input[type="date"], .range-item input[type="time"] {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
}

.date-config input[type="date"]:focus, .time-config input[type="time"]:focus,
.combination-item input[type="date"]:focus, .combination-item input[type="time"]:focus,
.range-item input[type="date"]:focus, .range-item input[type="time"]:focus {
    background: var(--bg-card);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    color: var(--text-primary);
}

/* Validation States for Date and Time Inputs */
.date-config input[type="date"].is-invalid, .time-config input[type="time"].is-invalid,
.combination-item input[type="date"].is-invalid, .combination-item input[type="time"].is-invalid,
.range-item input[type="date"].is-invalid, .range-item input[type="time"].is-invalid,
.combination-item input[type="number"].is-invalid, .range-item input[type="number"].is-invalid {
    border-color: var(--accent-danger) !important;
    background: rgba(220, 53, 69, 0.1);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.date-config input[type="date"].is-invalid:focus, .time-config input[type="time"].is-invalid:focus,
.combination-item input[type="date"].is-invalid:focus, .combination-item input[type="time"].is-invalid:focus,
.range-item input[type="date"].is-invalid:focus, .range-item input[type="time"].is-invalid:focus,
.combination-item input[type="number"].is-invalid:focus, .range-item input[type="number"].is-invalid:focus {
    border-color: var(--accent-danger) !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Weight Input Fields */
.combination-item input[type="number"], .range-item input[type="number"] {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    max-width: 120px;
}

.combination-item input[type="number"]:focus, .range-item input[type="number"]:focus {
    background: var(--bg-card);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    color: var(--text-primary);
}

/* Select Dropdowns in Combinations */
.combination-item .form-select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
}

.combination-item .form-select:focus {
    background: var(--bg-card);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
    color: var(--text-primary);
}

/* Enhanced Alert Styling for Wizard Integration */
.grid-config .alert-info, .checkbox-config .alert-info,
.date-config .alert-info, .time-config .alert-info {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(162, 155, 254, 0.1) 100%);
    border: 1px solid var(--accent-primary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-top: 1rem;
}

.grid-config .alert-info .btn, .checkbox-config .alert-info .btn,
.date-config .alert-info .btn, .time-config .alert-info .btn {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) !important;
    border: none !important;
    color: var(--text-primary) !important;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
    text-decoration: none;
}

.grid-config .alert-info .btn:hover, .checkbox-config .alert-info .btn:hover,
.date-config .alert-info .btn:hover, .time-config .alert-info .btn:hover {
    background: linear-gradient(135deg, #5b4cdb 0%, #918af5 100%) !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    color: var(--text-primary) !important;
}

/* Form Labels in Combinations */
.combination-item .form-label, .range-item .form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Responsive Design for New Question Types */
@media (max-width: 768px) {
    .combination-item, .range-item {
        padding: 1rem;
    }

    .checkbox-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .grid-config .table, .checkbox-config .table {
        font-size: 0.8rem;
    }

    .grid-config .table th, .checkbox-config .table th,
    .grid-config .table td, .checkbox-config .table td {
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 576px) {
    .combination-item .col-md-5, .combination-item .col-md-2,
    .range-item .col-md-5, .range-item .col-md-2 {
        padding: 0;
        margin-bottom: 0.75rem;
    }

    .add-multi-grid-combination, .add-checkbox-grid-combination,
    .add-checkbox-combination, .add-date-range, .add-time-range {
        width: 100%;
        margin-top: 1rem;
    }
}

/* Customer Review Page - New Question Types */
.grid-configuration, .checkbox-configuration, .date-configuration, .time-configuration {
    background: var(--bg-card);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.grid-configuration::before, .checkbox-configuration::before,
.date-configuration::before, .time-configuration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.grid-configuration h6, .checkbox-configuration h6,
.date-configuration h6, .time-configuration h6 {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.combinations-list .combination-item, .ranges-list .range-item {
    background: var(--bg-card);
    border: 1px solid var(--accent-primary);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.combinations-list .combination-item::before, .ranges-list .range-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.combinations-list .combination-item h6, .ranges-list .range-item h6 {
    color: var(--accent-primary) !important;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.combinations-list .combination-item:hover, .ranges-list .range-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--accent-secondary);
}

.selected-options, .range-details {
    color: var(--text-primary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.selected-options strong, .range-details strong {
    color: var(--accent-primary);
    font-weight: 600;
}

/* Custom focus styles */
*:focus {
    outline: none;
}

.form-control:focus,
.form-select:focus,
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
}

/* Force correct text colors everywhere */
.card-title, .card-text {
    color: var(--text-primary) !important;
}

.table tbody tr td {
    color: var(--text-primary) !important;
}

/* Badge color inheritance removed - using specific color rules instead */

/* Smaller lead text */
.lead {
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.4;
    margin-bottom: 1rem;
}

/* Adjust navbar height */
.navbar {
    padding: 0.5rem 0;
}

.navbar-brand {
    font-size: 1.2rem;
    font-weight: 600;
}

.nav-link {
    font-size: 0.9rem;
}

/* Compact spacing */
.mt-4 {
    margin-top: 1.5rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
}

/* List Group Styles - Fix for options text visibility */
.list-group {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.list-group-item {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
    font-size: 0.9rem;
    line-height: 1.4;
}

.list-group-item:first-child {
    border-top-left-radius: var(--radius-md);
    border-top-right-radius: var(--radius-md);
}

.list-group-item:last-child {
    border-bottom-left-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
}

.list-group-item:hover {
    background: var(--bg-card-hover) !important;
    color: var(--text-primary) !important;
    transform: none !important;
}

.list-group-item.active {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) !important;
    border-color: var(--accent-primary) !important;
    color: var(--text-primary) !important;
}

/* Ensure all text within list items is visible */
.list-group-item * {
    color: var(--text-primary) !important;
}

/* List group flush variant */
.list-group-flush .list-group-item {
    border-left: 0;
    border-right: 0;
    border-radius: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: 0;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: 0;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius-md) !important;
    box-shadow: var(--shadow-lg) !important;
    padding: 0.5rem 0 !important;
    margin-top: 0.5rem !important;
}

.dropdown-item {
    color: var(--text-primary) !important;
    padding: 0.75rem 1rem !important;
    transition: var(--transition-fast) !important;
    border: none !important;
    background: transparent !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: var(--bg-card-hover) !important;
    color: var(--text-primary) !important;
    transform: none !important;
}

.dropdown-item:active {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.dropdown-item i {
    color: var(--accent-primary) !important;
    width: 1.25rem !important;
    text-align: center !important;
}

.dropdown-item small {
    color: var(--text-muted) !important;
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
}

.dropdown-divider {
    border-color: var(--border-color) !important;
    margin: 0.5rem 0 !important;
}

/* Dropdown toggle button styling */
.btn.dropdown-toggle {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.btn.dropdown-toggle::after {
    margin-left: auto !important;
}
