"""
Response Generator Module for Google Form AutoFill

This module integrates the Gemini client with the form parser to generate
intelligent responses for form questions.
"""

import random
import datetime
from typing import Dict, List, Any, Optional, Union, Callable

import gemini_client
from form_enhanced import EnhancedFormParser
from response_storage import FormResponseManager


class ResponseGenerator:
    """Class for generating responses for form questions"""

    def __init__(self, gemini: Optional[gemini_client.GeminiClient] = None):
        """
        Initialize the response generator

        Args:
            gemini: Optional GeminiClient instance
        """
        self.gemini = gemini
        self.response_manager = FormResponseManager()

    def generate_responses_for_form(self,
                                   form_parser: EnhancedFormParser,
                                   method: str = "manual",
                                   sample_count: int = 5,
                                   examples: Optional[Dict[str, List[str]]] = None) -> str:
        """
        Generate responses for all questions in a form

        Args:
            form_parser: EnhancedFormParser instance with loaded form
            method: Generation method ("manual", "gemini_assisted", "fully_automated", or "batch_optimized")
            sample_count: Number of samples to generate per question
            examples: Optional dictionary mapping question IDs to example responses

        Returns:
            Status message
        """
        if not form_parser.questions:
            return "Error: Form not loaded or contains no questions"

        # Initialize form data in storage
        form_id = form_parser.get_form_id()
        form_title = form_parser.get_form_title()

        # Check if form data already exists
        existing_data = self.response_manager.load_form_data(form_id)
        if not existing_data:
            # Create new form data
            self.response_manager.initialize_form_data(form_id, form_title, form_parser.questions)

        # Initialize non-open-ended questions with equal weights
        self._initialize_non_open_ended_questions(form_parser, form_id)

        # Generate responses based on method
        if method == "manual":
            return f"Form initialized for manual input. Form ID: {form_id}"

        elif method == "gemini_assisted":
            if not self.gemini:
                return "Error: Gemini client not initialized"

            return self._generate_gemini_assisted(form_parser, form_id, sample_count, examples)

        elif method == "fully_automated":
            if not self.gemini:
                return "Error: Gemini client not initialized"

            return self._generate_fully_automated(form_parser, form_id, examples)

        elif method == "batch_optimized":
            if not self.gemini:
                return "Error: Gemini client not initialized"

            return self._generate_batch_optimized(form_parser, form_id, sample_count)

        else:
            return f"Error: Unknown generation method '{method}'"

    def _initialize_non_open_ended_questions(self, form_parser: EnhancedFormParser, form_id: str) -> None:
        """
        Initialize non-open-ended questions with all options having equal weights

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
        """
        questions = form_parser.get_questions()

        # Load existing form data
        form_data = self.response_manager.load_form_data(form_id)

        for question in questions:
            question_id = question["id"]
            question_type = question["type"]

            # Skip questions with default values
            if "default_value" in question:
                continue

            # Skip open-ended questions
            if question.get("is_open_ended", False):
                continue

            # Initialize multiple choice, dropdown, and checkboxes with equal weights
            if question_type in ["multiple_choice", "dropdown", "checkboxes"] and question.get("options"):
                # Check if responses already exist for this question
                if form_data and "responses" in form_data and str(question_id) in form_data["responses"]:
                    if form_data["responses"][str(question_id)]:
                        # Responses already exist, skip initialization
                        continue

                # Initialize all options with equal weight of 1
                for option in question["options"]:
                    self.response_manager.add_response(form_id, question_id, option, weight=1)

    def _generate_gemini_assisted(self,
                                 form_parser: EnhancedFormParser,
                                 form_id: str,
                                 sample_count: int,
                                 examples: Optional[Dict[str, List[str]]]) -> str:
        """
        Generate responses using Gemini-assisted method
        (Only for open-ended questions)

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            sample_count: Number of samples to generate per question
            examples: Optional dictionary mapping question IDs to example responses

        Returns:
            Status message
        """
        # Only process open-ended questions
        return self._generate_open_ended_only(form_parser, form_id, sample_count, examples)

    def _generate_fully_automated(self,
                                 form_parser: EnhancedFormParser,
                                 form_id: str,
                                 examples: Optional[Dict[str, List[str]]]) -> str:
        """
        Generate responses using fully automated Gemini method
        (Only for open-ended questions)

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            examples: Optional dictionary mapping question IDs to example responses

        Returns:
            Status message
        """
        # Only process open-ended questions
        return self._generate_open_ended_only(form_parser, form_id, 10, examples, fully_automated=True)

    def _generate_open_ended_only(self,
                                  form_parser: EnhancedFormParser,
                                  form_id: str,
                                  sample_count: int,
                                  examples: Optional[Dict[str, List[str]]],
                                  fully_automated: bool = False) -> str:
        """
        Generate responses only for open-ended questions using Gemini

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            sample_count: Number of samples to generate per question
            examples: Optional dictionary mapping question IDs to example responses
            fully_automated: Whether to use fully automated mode (with weights)

        Returns:
            Status message
        """
        questions = form_parser.get_questions()
        processed_count = 0
        skipped_count = 0

        for question in questions:
            question_id = question["id"]
            question_title = question["title"]
            question_type = question["type"]

            # Skip questions with default values
            if "default_value" in question:
                continue

            # Skip non-open-ended questions
            if not question.get("is_open_ended", False):
                if question_type in ["multiple_choice", "dropdown", "checkboxes"]:
                    skipped_count += 1
                continue

            # Get examples for this question if available
            question_examples = examples.get(question_id, []) if examples else []

            try:
                # Generate open-ended responses using Gemini
                prompt = gemini_client.create_open_ended_prompt(
                    question_title,
                    count=sample_count,
                    examples=question_examples
                )

                response_text = gemini_client.generate_with_retry(self.gemini, prompt)
                responses = gemini_client.parse_open_ended_responses(response_text, sample_count)

                if fully_automated:
                    # Assign weights automatically
                    weighted_responses = gemini_client.assign_weightage(responses)

                    # Save each response with its weight
                    for response, weight in weighted_responses.items():
                        self.response_manager.add_response(form_id, question_id, response, weight)
                else:
                    # Save each response with equal weight
                    for response in responses:
                        self.response_manager.add_response(form_id, question_id, response, weight=1)

                processed_count += 1

            except Exception as e:
                print(f"Error generating responses for question '{question_title}': {e}")

        method_name = "fully automated" if fully_automated else "Gemini-assisted"
        result_msg = f"Generated responses for {processed_count} open-ended questions using {method_name} method"

        if skipped_count > 0:
            result_msg += f"\nSkipped {skipped_count} non-open-ended questions (use manual weight adjustment for these)"

        return result_msg

    def _generate_individual_questions(self,
                                      form_parser: EnhancedFormParser,
                                      form_id: str,
                                      sample_count: int,
                                      examples: Optional[Dict[str, List[str]]],
                                      fully_automated: bool = False) -> str:
        """
        Generate responses by processing each question individually
        (Kept for backward compatibility, redirects to open-ended only)
        """
        return self._generate_open_ended_only(form_parser, form_id, sample_count, examples, fully_automated)

    def _generate_batch_optimized(self,
                                form_parser: EnhancedFormParser,
                                form_id: str,
                                sample_count: int,
                                fully_automated: bool = False) -> str:
        """
        Generate responses for all open-ended questions in a single batch request

        Args:
            form_parser: EnhancedFormParser instance
            form_id: Form ID
            sample_count: Number of samples to generate per question
            fully_automated: Whether to use fully automated mode (with weights)

        Returns:
            Status message
        """
        questions = form_parser.get_questions()
        processed_count = 0
        skipped_count = 0

        # Filter questions that need AI-generated responses (only open-ended)
        filtered_questions = []
        for question in questions:
            # Skip questions with default values
            if "default_value" in question:
                continue

            # Skip non-open-ended questions
            if not question.get("is_open_ended", False):
                if question["type"] in ["multiple_choice", "dropdown", "checkboxes"]:
                    skipped_count += 1
                continue

            filtered_questions.append(question)

        if not filtered_questions:
            return f"No open-ended questions require AI-generated responses. Skipped {skipped_count} non-open-ended questions."

        try:
            # Create batch prompt
            prompt = gemini_client.create_batch_prompt(filtered_questions, sample_count)

            # Generate responses
            response_text = gemini_client.generate_with_retry(self.gemini, prompt, max_tokens=4096)

            # Parse responses
            question_responses = gemini_client.parse_batch_responses(response_text, filtered_questions)

            # Process each question's responses
            for question in filtered_questions:
                question_id = question["id"]

                # Get responses for this question
                responses = question_responses.get(question_id, [])
                if not responses:
                    # Generate default response if no responses were generated
                    default_value = self._generate_default_value(question)
                    self.response_manager.add_response(form_id, question_id, default_value,
                                                     100 if fully_automated else sample_count)
                    continue

                if fully_automated:
                    # For open-ended, assign weights
                    weighted_responses = gemini_client.assign_weightage(responses)

                    # Save each response with its weight
                    for response, weight in weighted_responses.items():
                        self.response_manager.add_response(form_id, question_id, response, weight)
                else:
                    # Save each response with equal weight
                    for response in responses:
                        self.response_manager.add_response(form_id, question_id, response, weight=1)

                processed_count += 1

        except Exception as e:
            print(f"Error in batch generation: {e}")
            # Fall back to individual questions approach
            return self._generate_open_ended_only(form_parser, form_id, sample_count, None, fully_automated)

        method_name = "fully automated batch" if fully_automated else "Gemini-assisted batch"
        result_msg = f"Generated responses for {processed_count} open-ended questions using {method_name} method"

        if skipped_count > 0:
            result_msg += f"\nSkipped {skipped_count} non-open-ended questions (use manual weight adjustment for these)"

        return result_msg

    def _generate_default_value(self, question: Dict[str, Any]) -> Any:
        """
        Generate a default value for a question based on its type

        Args:
            question: Question object

        Returns:
            A default value appropriate for the question type
        """
        question_type_id = question.get("type_id")

        if question_type_id == 9:  # Date
            return datetime.date.today().strftime('%Y-%m-%d')

        elif question_type_id == 10:  # Time
            return datetime.datetime.now().strftime('%H:%M')

        elif question.get("type") == "email":
            return "<EMAIL>"

        elif question.get("options"):
            return random.choice(question["options"])

        else:
            return "Sample response"
