"""
Customer Specification Module for Google Form AutoFill

This module provides functionality for storing, managing, and retrieving
customer response specifications for Google Forms.
"""

import json
import os
import time
import uuid
from typing import Dict, List, Any, Optional, Union

from response_storage import ResponseStorage, FormResponseManager


class CustomerSpecification:
    """Class for managing customer response specifications"""
    
    def __init__(self, storage_dir: str = "responses/customer_specs"):
        """
        Initialize the customer specification manager
        
        Args:
            storage_dir: Directory to store specification files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        self.form_manager = FormResponseManager()
        
    def _get_spec_filename(self, spec_id: str) -> str:
        """
        Get the filename for a specification
        
        Args:
            spec_id: The ID of the specification
            
        Returns:
            The filename for the specification
        """
        return os.path.join(self.storage_dir, f"{spec_id}.json")
        
    def _get_all_specs_filename(self) -> str:
        """
        Get the filename for the index of all specifications
        
        Returns:
            The filename for the specifications index
        """
        return os.path.join(self.storage_dir, "specifications_index.json")
    
    def _load_specs_index(self) -> Dict[str, Dict[str, Any]]:
        """
        Load the specifications index
        
        Returns:
            Dictionary mapping spec_ids to basic specification metadata
        """
        filename = self._get_all_specs_filename()
        if not os.path.exists(filename):
            return {}
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
    
    def _save_specs_index(self, index: Dict[str, Dict[str, Any]]) -> None:
        """
        Save the specifications index
        
        Args:
            index: Dictionary mapping spec_ids to basic specification metadata
        """
        filename = self._get_all_specs_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(index, f, ensure_ascii=False, indent=2)
    
    def save_specification(self, spec_data: Dict[str, Any]) -> str:
        """
        Save a customer specification
        
        Args:
            spec_data: The specification data to save
            
        Returns:
            The ID of the saved specification
        """
        # Generate a new ID if not provided
        if 'id' not in spec_data:
            spec_data['id'] = str(uuid.uuid4())
            
        spec_id = spec_data['id']
        filename = self._get_spec_filename(spec_id)
        
        # Add metadata
        spec_data["_metadata"] = {
            "last_updated": time.time(),
            "version": "1.0"
        }
        
        # Save the specification
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(spec_data, f, ensure_ascii=False, indent=2)
            
        # Update the index
        index = self._load_specs_index()
        index[spec_id] = {
            "id": spec_id,
            "name": spec_data.get("name", "Unnamed Specification"),
            "form_id": spec_data.get("form_id", ""),
            "form_title": spec_data.get("form_title", ""),
            "customer_name": spec_data.get("customer_name", ""),
            "created_at": spec_data.get("created_at", time.time()),
            "last_updated": time.time()
        }
        self._save_specs_index(index)
        
        return spec_id
        
    def load_specification(self, spec_id: str) -> Optional[Dict[str, Any]]:
        """
        Load a customer specification
        
        Args:
            spec_id: The ID of the specification
            
        Returns:
            The specification data, or None if not found
        """
        filename = self._get_spec_filename(spec_id)
        if not os.path.exists(filename):
            return None
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            print(f"Error: Could not parse specification file {filename}")
            return None
            
    def delete_specification(self, spec_id: str) -> bool:
        """
        Delete a customer specification
        
        Args:
            spec_id: The ID of the specification
            
        Returns:
            True if successful, False otherwise
        """
        filename = self._get_spec_filename(spec_id)
        if not os.path.exists(filename):
            return False
            
        try:
            os.remove(filename)
            
            # Update the index
            index = self._load_specs_index()
            if spec_id in index:
                del index[spec_id]
                self._save_specs_index(index)
                
            return True
        except Exception as e:
            print(f"Error deleting specification: {e}")
            return False
            
    def list_specifications(self) -> List[Dict[str, Any]]:
        """
        List all customer specifications
        
        Returns:
            List of specification metadata
        """
        index = self._load_specs_index()
        return list(index.values())
        
    def create_specification(self, form_id: str, name: str, customer_name: str, 
                            description: str = "") -> Optional[str]:
        """
        Create a new customer specification
        
        Args:
            form_id: The ID of the form
            name: Name of the specification
            customer_name: Name of the customer
            description: Optional description
            
        Returns:
            The ID of the created specification, or None if failed
        """
        # Load form data to get form title and questions
        form_data = self.form_manager.load_form_data(form_id)
        if not form_data:
            print(f"Form data not found for form_id: {form_id}")
            return None
            
        # Create specification data
        spec_data = {
            "id": str(uuid.uuid4()),
            "name": name,
            "customer_name": customer_name,
            "description": description,
            "form_id": form_id,
            "form_title": form_data.get("form_title", "Unknown Form"),
            "form_url": form_data.get("form_url", ""),
            "created_at": time.time(),
            "weights": {},  # Will store question_id -> weights mapping
            "examples": {},  # Will store question_id -> examples mapping
            "share_token": None  # Will be set when sharing
        }
        
        # Save the specification
        return self.save_specification(spec_data)
        
    def set_question_weights(self, spec_id: str, question_id: str, 
                           weights: Dict[str, Union[int, float]]) -> bool:
        """
        Set weights for a question's options
        
        Args:
            spec_id: The ID of the specification
            question_id: The ID of the question
            weights: Dictionary mapping option text to weight (percentage or count)
            
        Returns:
            True if successful, False otherwise
        """
        spec_data = self.load_specification(spec_id)
        if not spec_data:
            return False
            
        # Initialize weights dictionary if it doesn't exist
        if "weights" not in spec_data:
            spec_data["weights"] = {}
            
        # Store the weights
        spec_data["weights"][str(question_id)] = weights
        
        # Save the updated specification
        self.save_specification(spec_data)
        return True
        
    def get_question_weights(self, spec_id: str, question_id: str) -> Dict[str, Union[int, float]]:
        """
        Get weights for a question's options
        
        Args:
            spec_id: The ID of the specification
            question_id: The ID of the question
            
        Returns:
            Dictionary mapping option text to weight
        """
        spec_data = self.load_specification(spec_id)
        if not spec_data or "weights" not in spec_data:
            return {}
            
        return spec_data["weights"].get(str(question_id), {})
        
    def set_question_examples(self, spec_id: str, question_id: str, examples: List[str]) -> bool:
        """
        Set example responses for an open-ended question
        
        Args:
            spec_id: The ID of the specification
            question_id: The ID of the question
            examples: List of example responses
            
        Returns:
            True if successful, False otherwise
        """
        spec_data = self.load_specification(spec_id)
        if not spec_data:
            return False
            
        # Initialize examples dictionary if it doesn't exist
        if "examples" not in spec_data:
            spec_data["examples"] = {}
            
        # Store the examples
        spec_data["examples"][str(question_id)] = examples
        
        # Save the updated specification
        self.save_specification(spec_data)
        return True
        
    def get_question_examples(self, spec_id: str, question_id: str) -> List[str]:
        """
        Get example responses for an open-ended question
        
        Args:
            spec_id: The ID of the specification
            question_id: The ID of the question
            
        Returns:
            List of example responses
        """
        spec_data = self.load_specification(spec_id)
        if not spec_data or "examples" not in spec_data:
            return []
            
        return spec_data["examples"].get(str(question_id), [])
        
    def convert_percentage_weights_to_counts(self, weights: Dict[str, float], 
                                           total_responses: int) -> Dict[str, int]:
        """
        Convert percentage-based weights to count-based weights
        
        Args:
            weights: Dictionary mapping option text to percentage weight (0-100)
            total_responses: Total number of responses to generate
            
        Returns:
            Dictionary mapping option text to count-based weight
        """
        count_weights = {}
        remaining_count = total_responses
        remaining_percentage = 100.0
        
        # First pass: calculate integer counts for each option
        for option, percentage in weights.items():
            if percentage <= 0:
                count_weights[option] = 0
                continue
                
            count = int((percentage / 100.0) * total_responses)
            count_weights[option] = count
            remaining_count -= count
            remaining_percentage -= percentage
            
        # Second pass: distribute any remaining counts based on relative percentages
        if remaining_count > 0 and remaining_percentage > 0:
            sorted_options = sorted(
                [(option, percentage) for option, percentage in weights.items() if percentage > 0],
                key=lambda x: x[1],
                reverse=True
            )
            
            for i in range(remaining_count):
                option = sorted_options[i % len(sorted_options)][0]
                count_weights[option] += 1
                
        return count_weights
        
    def apply_specification_to_form(self, spec_id: str, total_responses: int = 100) -> bool:
        """
        Apply a customer specification to a form by setting up the responses
        
        Args:
            spec_id: The ID of the specification
            total_responses: Total number of responses to generate
            
        Returns:
            True if successful, False otherwise
        """
        spec_data = self.load_specification(spec_id)
        if not spec_data:
            return False
            
        form_id = spec_data.get("form_id")
        if not form_id:
            return False
            
        # Load form data
        form_data = self.form_manager.load_form_data(form_id)
        if not form_data:
            return False
            
        # Process weights for each question
        for question_id, weights in spec_data.get("weights", {}).items():
            # Clear existing responses for this question
            if str(question_id) in form_data.get("responses", {}):
                form_data["responses"][str(question_id)] = []
            
            # Convert percentage weights to counts if needed
            count_weights = {}
            is_percentage = False
            
            # Check if weights are percentages (sum to approximately 100)
            weight_sum = sum(weights.values())
            if 95 <= weight_sum <= 105:  # Allow for small rounding errors
                is_percentage = True
                count_weights = self.convert_percentage_weights_to_counts(weights, total_responses)
            else:
                # Weights are already counts
                count_weights = {option: int(weight) for option, weight in weights.items()}
            
            # Add responses with weights
            for option, count in count_weights.items():
                if count > 0:
                    self.form_manager.add_response(form_id, question_id, option, count)
        
        # Process examples for each question
        for question_id, examples in spec_data.get("examples", {}).items():
            # For examples, we add each example with equal weight
            for example in examples:
                self.form_manager.add_response(form_id, question_id, example, 1)
        
        return True
