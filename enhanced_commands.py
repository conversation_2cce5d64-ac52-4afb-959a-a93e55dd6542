"""
Enhanced Commands Module for Google Form AutoFill

This module provides additional commands for the enhanced Google Form AutoFill tool,
focusing on example-based response generation, quality control, and feedback.
"""

import argparse
import json
import os
import sys
import time
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path

from form_enhanced import EnhancedFormParser
from enhanced_gemini_client import EnhancedGeminiClient
from enhanced_response_generator import <PERSON>hancedResponseGenerator
from feedback_manager import FeedbackManager
from response_storage import FormResponseManager


def display_progress(progress: float, current: int, total: int, prefix: str = "Progress"):
    """
    Display progress bar

    Args:
        progress: Progress as a float between 0 and 1
        current: Current item number
        total: Total number of items
        prefix: Prefix text for the progress bar
    """
    bar_length = 40
    filled_length = int(bar_length * progress)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)

    sys.stdout.write(f'\r{prefix}: [{bar}] {progress*100:.1f}% | {current}/{total}')
    sys.stdout.flush()

    if progress >= 1:
        print()


def load_examples_file(file_path: str) -> Dict[str, List[str]]:
    """
    Load examples from a JSON file

    Args:
        file_path: Path to the JSON file

    Returns:
        Dictionary mapping question IDs to example responses
    """
    if not os.path.exists(file_path):
        print(f"Error: Examples file not found: {file_path}")
        return {}

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            examples = json.load(f)
        
        # Validate format
        if not isinstance(examples, dict):
            print(f"Error: Examples file must contain a JSON object")
            return {}
            
        # Convert all keys to strings
        examples = {str(k): v for k, v in examples.items()}
        
        # Validate values
        for question_id, responses in examples.items():
            if not isinstance(responses, list):
                print(f"Warning: Examples for question {question_id} must be a list. Skipping.")
                examples[question_id] = []
                continue
                
            # Ensure all responses are strings
            examples[question_id] = [str(r) for r in responses]
        
        return examples
        
    except json.JSONDecodeError:
        print(f"Error: Examples file contains invalid JSON")
        return {}
    except Exception as e:
        print(f"Error loading examples file: {e}")
        return {}


def load_style_guidance_file(file_path: str) -> Dict[str, str]:
    """
    Load style guidance from a JSON file

    Args:
        file_path: Path to the JSON file

    Returns:
        Dictionary mapping question types to style guidance
    """
    if not os.path.exists(file_path):
        print(f"Error: Style guidance file not found: {file_path}")
        return {}

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            guidance = json.load(f)
        
        # Validate format
        if not isinstance(guidance, dict):
            print(f"Error: Style guidance file must contain a JSON object")
            return {}
            
        # Ensure all values are strings
        guidance = {k: str(v) for k, v in guidance.items()}
        
        return guidance
        
    except json.JSONDecodeError:
        print(f"Error: Style guidance file contains invalid JSON")
        return {}
    except Exception as e:
        print(f"Error loading style guidance file: {e}")
        return {}


def generate_with_examples(form_url: str, examples_file: str, 
                         api_key: Optional[Union[str, List[str]]] = None,
                         sample_count: int = 5,
                         quality_threshold: float = 0.7,
                         diversity_factor: float = 0.8,
                         style_guidance_file: Optional[str] = None,
                         key_strategy: str = "round_robin") -> str:
    """
    Generate responses using customer-provided examples

    Args:
        form_url: URL of the form
        examples_file: Path to the JSON file with examples
        api_key: Gemini API key or list of keys
        sample_count: Number of samples to generate per question
        quality_threshold: Minimum quality score for responses (0.0-1.0)
        diversity_factor: Factor controlling response diversity (0.0-1.0)
        style_guidance_file: Optional path to style guidance file
        key_strategy: Strategy for key selection when multiple keys are provided

    Returns:
        Status message
    """
    # Load form
    print(f"Loading form from {form_url}...")
    parser = EnhancedFormParser(form_url)
    
    if not parser.load_form():
        return "Failed to load form"
        
    print(f"Form loaded: {parser.get_form_title()}")
    print(f"Found {len(parser.get_questions())} questions ({len(parser.get_questions(True))} required)")
    
    # Load examples
    examples = load_examples_file(examples_file)
    if not examples:
        return "Failed to load examples or examples file is empty"
        
    print(f"Loaded examples for {len(examples)} questions")
    
    # Load style guidance if provided
    style_guidance = None
    if style_guidance_file:
        style_guidance = load_style_guidance_file(style_guidance_file)
        if style_guidance:
            print(f"Loaded style guidance for {len(style_guidance)} question types")
    
    # Initialize Gemini client
    if isinstance(api_key, str) and ',' in api_key:
        api_key = [key.strip() for key in api_key.split(',') if key.strip()]
        
    gemini = EnhancedGeminiClient(api_key, key_strategy=key_strategy)
    print(f"Initialized Gemini client with {'multiple' if isinstance(api_key, list) else 'single'} API key(s)")
    
    # Initialize enhanced response generator
    generator = EnhancedResponseGenerator(gemini)
    
    # Generate responses
    print("Generating responses with examples...")
    result = generator.generate_responses_with_examples(
        parser,
        examples,
        sample_count=sample_count,
        quality_threshold=quality_threshold,
        diversity_factor=diversity_factor,
        style_guidance=style_guidance
    )
    
    return result


def batch_generate(form_url: str, examples_file: str, 
                 api_key: Optional[Union[str, List[str]]] = None,
                 batch_size: int = 10,
                 total_count: int = 100,
                 quality_threshold: float = 0.7,
                 diversity_factor: float = 0.8,
                 style_guidance_file: Optional[str] = None,
                 key_strategy: str = "round_robin") -> str:
    """
    Generate responses in batches for efficiency

    Args:
        form_url: URL of the form
        examples_file: Path to the JSON file with examples
        api_key: Gemini API key or list of keys
        batch_size: Number of responses to generate in each batch
        total_count: Total number of responses to generate
        quality_threshold: Minimum quality score for responses (0.0-1.0)
        diversity_factor: Factor controlling response diversity (0.0-1.0)
        style_guidance_file: Optional path to style guidance file
        key_strategy: Strategy for key selection when multiple keys are provided

    Returns:
        Status message
    """
    # Load form
    print(f"Loading form from {form_url}...")
    parser = EnhancedFormParser(form_url)
    
    if not parser.load_form():
        return "Failed to load form"
        
    print(f"Form loaded: {parser.get_form_title()}")
    print(f"Found {len(parser.get_questions())} questions ({len(parser.get_questions(True))} required)")
    
    # Get form ID
    form_id = parser.get_form_id()
    
    # Load examples
    examples = load_examples_file(examples_file)
    if not examples:
        return "Failed to load examples or examples file is empty"
        
    print(f"Loaded examples for {len(examples)} questions")
    
    # Load style guidance if provided
    style_guidance = None
    if style_guidance_file:
        style_guidance = load_style_guidance_file(style_guidance_file)
        if style_guidance:
            print(f"Loaded style guidance for {len(style_guidance)} question types")
    
    # Initialize Gemini client
    if isinstance(api_key, str) and ',' in api_key:
        api_key = [key.strip() for key in api_key.split(',') if key.strip()]
        
    gemini = EnhancedGeminiClient(api_key, key_strategy=key_strategy)
    print(f"Initialized Gemini client with {'multiple' if isinstance(api_key, list) else 'single'} API key(s)")
    
    # Initialize enhanced response generator
    generator = EnhancedResponseGenerator(gemini)
    generator.quality_threshold = quality_threshold
    generator.diversity_factor = diversity_factor
    
    if style_guidance:
        generator.style_guidance = style_guidance
    
    # Define progress callback
    def progress_callback(progress, current, total):
        display_progress(progress, current, total, "Batch progress")
    
    # Generate responses in batches
    print(f"Generating {total_count} responses in batches of {batch_size}...")
    result = generator.batch_generate(
        parser,
        form_id,
        examples,
        batch_size=batch_size,
        total_count=total_count,
        progress_callback=progress_callback
    )
    
    return result


def apply_feedback(form_id: str) -> str:
    """
    Apply stored feedback to adjust response weights

    Args:
        form_id: ID of the form

    Returns:
        Status message
    """
    # Initialize feedback manager
    feedback_manager = FeedbackManager()
    
    # Apply feedback
    print(f"Applying feedback for form {form_id}...")
    result = feedback_manager.apply_feedback(form_id)
    
    if result:
        return f"Successfully applied feedback for form {form_id}"
    else:
        return f"No feedback applied for form {form_id} (no feedback found or no changes needed)"


def add_customer_feedback(form_id: str, feedback_file: str) -> str:
    """
    Add customer feedback from a JSON file

    Args:
        form_id: ID of the form
        feedback_file: Path to the JSON file with feedback data

    Returns:
        Status message
    """
    # Load feedback data
    if not os.path.exists(feedback_file):
        return f"Error: Feedback file not found: {feedback_file}"
        
    try:
        with open(feedback_file, 'r', encoding='utf-8') as f:
            feedback_data = json.load(f)
            
        if not isinstance(feedback_data, dict):
            return f"Error: Feedback file must contain a JSON object"
            
        # Validate required fields
        if "customer_id" not in feedback_data:
            return f"Error: Feedback data must include 'customer_id'"
            
        if "ratings" not in feedback_data or not isinstance(feedback_data["ratings"], dict):
            return f"Error: Feedback data must include 'ratings' as a dictionary"
            
        # Initialize feedback manager
        feedback_manager = FeedbackManager()
        
        # Add feedback
        result = feedback_manager.add_customer_feedback(
            form_id,
            feedback_data["customer_id"],
            feedback_data["ratings"],
            feedback_data.get("comments")
        )
        
        if result:
            return f"Successfully added customer feedback for form {form_id}"
        else:
            return f"Failed to add customer feedback for form {form_id}"
            
    except json.JSONDecodeError:
        return f"Error: Feedback file contains invalid JSON"
    except Exception as e:
        return f"Error loading feedback file: {e}"


def add_expert_feedback(form_id: str, feedback_file: str) -> str:
    """
    Add expert feedback from a JSON file

    Args:
        form_id: ID of the form
        feedback_file: Path to the JSON file with feedback data

    Returns:
        Status message
    """
    # Load feedback data
    if not os.path.exists(feedback_file):
        return f"Error: Feedback file not found: {feedback_file}"
        
    try:
        with open(feedback_file, 'r', encoding='utf-8') as f:
            feedback_data = json.load(f)
            
        if not isinstance(feedback_data, dict):
            return f"Error: Feedback file must contain a JSON object"
            
        # Validate required fields
        if "expert_id" not in feedback_data:
            return f"Error: Feedback data must include 'expert_id'"
            
        if "response_feedback" not in feedback_data or not isinstance(feedback_data["response_feedback"], dict):
            return f"Error: Feedback data must include 'response_feedback' as a dictionary"
            
        # Initialize feedback manager
        feedback_manager = FeedbackManager()
        
        # Add feedback
        result = feedback_manager.add_expert_feedback(
            form_id,
            feedback_data["expert_id"],
            feedback_data["response_feedback"],
            feedback_data.get("general_comments")
        )
        
        if result:
            return f"Successfully added expert feedback for form {form_id}"
        else:
            return f"Failed to add expert feedback for form {form_id}"
            
    except json.JSONDecodeError:
        return f"Error: Feedback file contains invalid JSON"
    except Exception as e:
        return f"Error loading feedback file: {e}"


def get_feedback_summary(form_id: str, output_file: Optional[str] = None) -> str:
    """
    Get a summary of feedback for a form

    Args:
        form_id: ID of the form
        output_file: Optional path to save the summary as JSON

    Returns:
        Status message
    """
    # Initialize feedback manager
    feedback_manager = FeedbackManager()
    
    # Get summary
    summary = feedback_manager.get_feedback_summary(form_id)
    
    # Save to file if requested
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            print(f"Saved feedback summary to {output_file}")
        except Exception as e:
            print(f"Error saving feedback summary: {e}")
    
    # Print summary
    print("\nFeedback Summary:")
    print(f"Form ID: {summary['form_id']}")
    print(f"Total feedback: {summary['feedback_count']}")
    print(f"Customer feedback: {summary['customer_feedback_count']}")
    print(f"Expert feedback: {summary['expert_feedback_count']}")
    
    if summary['average_ratings']:
        print("\nAverage Ratings by Question:")
        for question_id, rating in summary['average_ratings'].items():
            print(f"  Question {question_id}: {rating}")
    
    if summary['question_feedback']:
        print("\nQuestion Feedback:")
        for question_id, feedback in summary['question_feedback'].items():
            print(f"  Question {question_id}:")
            print(f"    Approved: {feedback['approve_count']}")
            print(f"    Rejected: {feedback['reject_count']}")
            print(f"    Edited: {feedback['edit_count']}")
    
    return "Feedback summary displayed"


def get_learning_insights(form_id: str, output_file: Optional[str] = None) -> str:
    """
    Get insights from feedback for improving future generations

    Args:
        form_id: ID of the form
        output_file: Optional path to save the insights as JSON

    Returns:
        Status message
    """
    # Initialize feedback manager
    feedback_manager = FeedbackManager()
    
    # Get insights
    insights = feedback_manager.get_learning_insights(form_id)
    
    # Save to file if requested
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(insights, f, ensure_ascii=False, indent=2)
            print(f"Saved learning insights to {output_file}")
        except Exception as e:
            print(f"Error saving learning insights: {e}")
    
    # Print insights
    print("\nLearning Insights:")
    print(f"Form ID: {insights['form_id']}")
    
    if insights['question_insights']:
        print("\nQuestion Insights:")
        for question_id, question_insights in insights['question_insights'].items():
            print(f"\n  Question: {question_insights['title']}")
            
            if question_insights['high_rated_responses']:
                print("    High-rated responses examples:")
                for i, response in enumerate(question_insights['high_rated_responses'][:3], 1):
                    print(f"      {i}. {response[:100]}{'...' if len(response) > 100 else ''}")
            
            if question_insights['low_rated_responses']:
                print("    Low-rated responses examples:")
                for i, response in enumerate(question_insights['low_rated_responses'][:3], 1):
                    print(f"      {i}. {response[:100]}{'...' if len(response) > 100 else ''}")
            
            if question_insights['improvement_suggestions']:
                print("    Improvement suggestions:")
                for i, suggestion in enumerate(question_insights['improvement_suggestions'][:3], 1):
                    print(f"      {i}. {suggestion[:100]}{'...' if len(suggestion) > 100 else ''}")
    
    if insights['general_insights']:
        print("\nGeneral Insights:")
        for i, insight in enumerate(insights['general_insights'][:5], 1):
            print(f"  {i}. {insight[:100]}{'...' if len(insight) > 100 else ''}")
    
    return "Learning insights displayed"
